import React, { useEffect, useState } from 'react';
import { Button, Container } from 'reactstrap';
import Row from 'reactstrap/lib/Row';
import { AdyenSubscription, PaymentErrorModal } from 'components';
import styles from '../styles/sass/_registerPage.module.scss';

export default function RegisterStepThree({
  hasUserData,
  stepOneData,
  toStepTwo,
  toStepFour,
}) {
  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);

  useEffect(() => {
    // window.scrollTo(0, 0);
    window.location.href = '/settings';
  }, []);

  const onPaymentError = () => {
    setShowPaymentErrorModal(true);
  };

  return hasUserData ? (
    <>
      <Container>
        <Row className="justify-content-center">
          <h1 className="main-header">Kasse</h1>
        </Row>
      </Container>
      <AdyenSubscription
        selectedProductPackage={{
          registrationCode: stepOneData?.registrationCode,
          productPackageId: stepOneData?.productPackageId,
        }}
        onPaymentSuccess={toStepFour}
        onPaymentError={onPaymentError}
      />
      <Container className="mb-5 step-3-back-btn-wrapper">
        <Button onClick={toStepTwo} className={styles.nextPrevBtn}>
          Zurück
        </Button>
      </Container>

      <PaymentErrorModal
        show={showPaymentErrorModal}
        onClose={() => setShowPaymentErrorModal(false)}
      />
    </>
  ) : (
    <Container>
      <h1 className="w-100 p-4 text-center">
        Bitte melde dich an, um mit der Zahlung fortzufahren.
      </h1>
    </Container>
  );
}
