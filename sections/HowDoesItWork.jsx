import { Section } from '../components';
import VideoContainer from '../components/VideoContainer/VideoContainer';

const HowDoesItWork = (props) => {
  return (
    <>
      <Section background="#fff" className="pad-b-5" customPadding={true}>
        <div className="col-lg-6 col-sm-12">
          <h1 className="jumbo-header text-lg-left text-center">
            Gutscheincodes, die immer funktionieren
          </h1>
          <p className="text-n text-lg-left text-center section1-howdoesitwork">
            Registriere dich auf unserer Website und erhalte Zugang zu
            Gutscheincodes, die immer funktionieren. Viele unsere Coupons stehen
            dir auch kostenlos und ohne Registrierung zur Verfügung.
          </p>
        </div>
        <div className="col-lg-6 col-sm-12 d-flex justify-content-center align-items-start">
          <VideoContainer
            videoUrl="https://player.vimeo.com/video/515753935"
            thumblUrl="/headers/Captaincoupon_About-1.jpg"
          />
        </div>
      </Section>
      <Section
        background="#fff"
        rowClass="pad-t-3 pad-b-5 flex-lg-row-reverse"
        customPadding={true}
      >
        <div className="col-lg-6 col-sm-12">
          <h1 className="jumbo-header text-left">Finde, die besten Rabatte</h1>
          <p className="text-n text-lg-left text-center">
            Logge dich als CC Plus-Mitglied ein um Zugriff auf alle unsere
            Coupons und Angebote zu erhalten. Auf CaptainCoupon findest du
            Angebote und Coupons aus über 15 Kategorien, wie z.B. Mode, Technik
            oder Sport.
          </p>
        </div>
        <div className="col-lg-6 col-sm-12 d-flex justify-content-center align-items-start">
          <VideoContainer
            videoUrl="https://player.vimeo.com/video/515754089"
            thumblUrl="/headers/Captaincoupon_About-2.jpg"
          />
        </div>
      </Section>
      <Section background="#fff" className="pad-b-5" customPadding={true}>
        <div className="col-lg-6 col-sm-12 pad-lg-tb-3 pad-t-0">
          <h1 className="jumbo-header text-lg-left text-center">
            Viel Geld sparen
          </h1>
          <p className="text-n text-lg-left text-center">
            Je nachdem ob der Gutschein im Onlineshop oder im Ladengeschäft
            einzulösen ist, kannst du den Code direkt kopieren und im Onlineshop
            deiner Wahl anwenden oder den Gutschein auf dem Handy vorzeigen, als
            PDF speichern oder klassisch ausdrucken und in der Filiale des
            ausgewählten stationieren Händlers einlösen. Egal was deine Wahl
            ist, du wirst viel Geld sparen, wenn du vorher prüfst, ob wir einen
            Gutschein für deinen Einkauf haben.
          </p>
        </div>
        <div className="col-lg-6 col-sm-12 pad-lg-t-3 pad-t-0 pad-b-3 d-flex justify-content-center align-items-start">
          <VideoContainer
            videoUrl="https://player.vimeo.com/video/515754028"
            thumblUrl="/headers/Captaincoupon_About-3.jpg"
          />
        </div>
      </Section>
    </>
  );
};
export default HowDoesItWork;
