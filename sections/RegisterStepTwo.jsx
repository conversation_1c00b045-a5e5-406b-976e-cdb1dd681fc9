import React, { useEffect } from 'react';

import { Section, RegisterForm } from 'components';
import { isAuth } from 'services/auth.service';

export default function RegisterStepTwo({
  stepTwoData,
  toStepThree,
  toStepOne,
}) {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <Section background="#fff" customPadding={true}>
      <h1 className="main-header">Konto einrichten</h1>
      {stepTwoData ? (
        <RegisterForm
          formData={stepTwoData}
          submitButtonLabel={isAuth() ? 'Weiter' : 'Konto erstellen'}
          actionOnSubmit={toStepThree}
          backButtonLabel="Zurück"
          onBackButton={toStepOne}
        />
      ) : null}
    </Section>
  );
}
