import { useEffect, useState } from 'react';
import * as BrandService from 'services/brands.service';
import Link from 'next/link';
import { Section, BrandCarousel } from '../components';
import { useRouter } from 'next/router';

export default function BrandsCarouselSection (props) {
  const { showBtn = true, className = '', landingPage = false } = props;
  const [brands, setBrands] = useState();
  const [loading, setLoading] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const getBrands = async (options) => {
    setLoading(true);
    const data = await BrandService.getBrands(options);
    setBrands(data);
    setLoading(false);
  };

  useEffect(() => {
    getBrands({
      pageSize: -1,
      sort: [
        {
          field: 'isPopular',
          dir: 'DESC',
        },
      ],
    });

    setIsMobile(
      Boolean(
        navigator.userAgent.match(
          /Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i
        )
      )
    );

    return () => {};
  }, []);

  const getBrandsForSlide = (brandList) => {
    if (!brandList?.length) {
      return [];
    }

    const numOfItemsPerSLide = isMobile ? 12 : 24;
    const numOfSlides = Math.ceil(brandList.length / numOfItemsPerSLide);
    const brandSlices = [];

    for (let i = 0; i < numOfSlides; i++) {
      brandSlices.push(
        brandList.slice(
          i * numOfItemsPerSLide,
          i * numOfItemsPerSLide + numOfItemsPerSLide
        )
      );
    }

    return brandSlices;
  };

  const brandsToRender = getBrandsForSlide(brands);
  const router = useRouter();
  return (
    <Section className={`${className}`} background="#fff" customPadding={true}>
      <div className="col-sm-12">
        <div className="row">
          <h1
            className={`${
              router.pathname == '/' ? 'text-center' : ''
            } col-sm-12 section-header pad-sm-b-1 text-lg-left`}
          >
            Beliebte Marken
          </h1>
        </div>
      </div>

      <div className="col-sm-12">
        {loading && <p>loading....</p>}

        {!brandsToRender?.length ? (
          <div className="not-found">
            <h1>Leider haben wir für deine Suche keine Ergebnisse gefunden.</h1>
          </div>
        ) : (
          <>
            <div className="row justify-content-center">
              <BrandCarousel
                brandsForSlides={brandsToRender}
                showIndicator={!isMobile}
              />
            </div>
          </>
        )}
      </div>

      <div
        className={`col-sm-12 d-flex justify-content-center ${
          landingPage ? 'alle-marken-btn-landing-page' : ''
        }`}
      >
        {showBtn && (
          <Link href="/marken" className="btn btn-dark">
            Alle Marken
          </Link>
        )}
      </div>
    </Section>
  );
}