import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { Dropdown, DropdownMenu, DropdownToggle } from 'reactstrap';
import * as BrandService from 'services/brands.service';
import { ModalSearchBlock, SearchBlock, Section } from '../components';

const BrandsSection = ({
  showBtn = true,
  className = '',
  landingPage = false,
  pageSize = 24,
}) => {
  const [brands, setBrands] = useState();

  const displayCount = 24;
  const [userQuery, setUserQuery] = useState('');
  const [loading, setLoading] = useState(false);

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const router = useRouter();
  const [sorting, setSorting] = useState(
    router.pathname === '/marken' ? 'Neueste Marken' : 'Neueste Gutscheine'
  );
  const sortingsList = [
    {
      value: 'newest',
      label:
        router.pathname === '/marken' ? 'Neueste Marken' : 'Neueste Gutscheine',
    },
    {
      value: 'name',
      label: router.pathname === '/marken' ? 'Marken A-Z' : 'Anbieter A-Z',
    },
  ];
  const [sortBy, setSortBy] = useState({
    dir: 'DESC',
    field: 'timeAdded',
  });

  const sortingOptions = {
    name: () => {
      setSortBy({
        dir: 'ASC',
        field: 'name',
      });
    },
    newest: () => {
      setSortBy({
        dir: 'DESC',
        field: 'timeAdded',
      });
    },
  };

  const handleSorting = (data) => {
    sortingOptions[data]();
  };

  const getBrands = async (options) => {
    const data = await BrandService.getBrands(options);
    setBrands(data);
  };

  useEffect(() => {
    getBrands({
      pageSize,
      sort: [sortBy],
      search: userQuery,
    });

    return () => {};
  }, [sortBy, userQuery]);

  const brandsToRender = showBtn ? brands?.slice(0, displayCount) : brands;

  // This special styling for brand links needs special handling
  const BrandLink = ({ href, children }) => (
    <Link href={href} className="col-lg-2 col-4 align-self-center brand-link">
      {children}
    </Link>
  );

  return (
    <Section className={`${className}`} background="#fff" customPadding={true}>
      <div className="col-sm-12">
        <div className="row">
          <h1 className="main-header main-header-brand-section col-lg-6 col-sm-12">
            Beliebte Marken
          </h1>
          <div className="col-lg-6 col-12 filter-and-sort-section ">
            <div className="row">
              <div className="col-sm-5 offset-sm-2 col-4 search-block">
                <SearchBlock
                  className="shorter d-none d-lg-flex"
                  handleChange={setUserQuery}
                />
                <ModalSearchBlock
                  className="shorter d-block d-lg-none"
                  handleChange={setUserQuery}
                />
              </div>
              <div className="col-sm-5 col-8">
                <div className="selectDark sorting col-12 float-right">
                  <Dropdown
                    isOpen={dropdownOpen}
                    toggle={() => setDropdownOpen(!dropdownOpen)}
                    onMouseOver={() => setDropdownOpen(true)}
                    onMouseLeave={() => setDropdownOpen(false)}
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                  >
                    <DropdownToggle tag="span">
                      <p>{sorting}</p>
                    </DropdownToggle>
                    <DropdownMenu>
                      {sortingsList?.map((item) => {
                        return (
                          <a
                            className="custom-dropdown-item"
                            key={item.value}
                            onClick={() => {
                              handleSorting(item.value);
                              setSorting(item.label);
                            }}
                          >
                            {item.label}
                          </a>
                        );
                      })}
                    </DropdownMenu>
                  </Dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="col-sm-12">
        {loading && <p>loading....</p>}

        {!brandsToRender?.length ? (
          <div className="not-found">
            <h1>Leider haben wir für deine Suche keine Ergebnisse gefunden.</h1>
          </div>
        ) : (
          <>
            <div className="row">
              {brandsToRender?.map((item, index) => (
                <BrandLink href={`/brand/${item.slug}`} key={index}>
                  <img
                    src={item.compressedLogo || item.logo}
                    alt={`Logo der Brand ${item.slug} gutschein`}
                    loading="lazy"
                  />
                </BrandLink>
              ))}
            </div>
          </>
        )}
      </div>

      <div
        className={`col-sm-12 d-flex justify-content-center ${
          landingPage ? 'alle-marken-btn-landing-page' : ''
        }`}
      >
        {showBtn && (
          <Link href="/marken" className="btn btn-dark">
            Alle Marken
          </Link>
        )}
      </div>
    </Section>
  );
};

export default BrandsSection;