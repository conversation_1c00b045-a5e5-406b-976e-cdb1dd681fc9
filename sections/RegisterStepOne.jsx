import { PackageSelect, Section } from 'components';
import { useCallback, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Button, Container } from 'reactstrap';
import Benefits from '../components/Benefits/Benefits';
import SpecialAudienceBenefits from '../components/SpecialAudienceBenefits/SpecialAudienceBenefits';

import badges from '../model/badges.json';
import styles from '../styles/sass/_registerPage.module.scss';

const REGISTRATION_CODE_INPUT_LABELS_BY_AUDIENCE_KEY = {
  corporate:
    'Der Registrierungscode, den du von deinem Arbeitgeber erhalten hast:',
  club: 'Der Registrierungscode, den du von deinem Verein erhalten hast:',
};

export default function RegisterStepOne({
  stepOneData,
  toStepTwo,
  setProductInfo,
  specialAudience, // 'corporate' | 'club' | undefined
}) {
  const packageSelectRef = useRef(null);
  const [productFromCoupon, setProductFromCouponCode] = useState(null);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: { stepOne: stepOneData },
  });

  const valuesWatch = watch('stepOne');

  const onProductFromCouponLoad = useCallback(
    (product) => {
      setProductFromCouponCode(product);
    },
    [setProductFromCouponCode]
  );

  const onSubmit = (data) => {
    toStepTwo(data.stepOne);
  };

  return (
    <div className="container">
      <Section background="#fff" customPadding={false}>
        {!specialAudience && <Benefits badges={badges} />}
        {specialAudience && (
          <SpecialAudienceBenefits
            isProductFromCodeLoaded={!!productFromCoupon}
            specialAudience={specialAudience}
          />
        )}

        {!specialAudience && (
          <h1 className="main-header">Wähle deine CC+ Mitgliedschaft</h1>
        )}
        <form className="w-100" onSubmit={(e) => e.preventDefault()}>
          <Controller
            control={control}
            name="stepOne"
            render={({ field: { onChange, value } }) => (
              <PackageSelect
                refProp={packageSelectRef}
                selected={value}
                onSelect={(stepOneData) => onChange({ ...stepOneData })}
                onClickSelected={handleSubmit(onSubmit)}
                setProductInfo={setProductInfo}
                onProductFromCouponCodeLoad={onProductFromCouponLoad}
                hideDefaultOffers={!!specialAudience}
                couponCodeInputLabel={
                  REGISTRATION_CODE_INPUT_LABELS_BY_AUDIENCE_KEY[
                    specialAudience
                  ] ||
                  'Hast du einen Gutscheincode?'
                }
              />
            )}
            rules={{
              required: true,
              validate: (value) => {
                if (!value?.productPackageId) {
                  packageSelectRef.current.scrollIntoView();
                  return 'Please select a product package';
                }
              },
            }}
          />
          {errors.stepOne ? <div>{errors.stepOne.message}</div> : null}
        </form>
        <Container className="d-flex flex-row-reverse pb-4">
          <Button
            onClick={handleSubmit(onSubmit)}
            className={styles.nextPrevBtn}
            disabled={!valuesWatch?.productPackageId}
          >
            Weiter
          </Button>
        </Container>
      </Section>
    </div>
  );
}
