import { Section } from '../components';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

import { getConsumer } from 'services/consumer.service';
import { isAuth } from 'services/auth.service';
import { newsletterSubscribe } from 'services/newsletter.service';
import Link from 'next/link';

const getErrorMessage = (error) => {
  if (
    error.startsWith('NewsletterSubscription with email [') &&
    error.endsWith('] already exists!')
  ) {
    const regExp = /\[(.*?)\]/;
    const emailAddress = error.match(regExp)[1];
    return `Mit der E-Mail Adresse [${emailAddress}] bist du bereits beim Newsletter registriert.`;
  } else if (error.includes('Invalid Email')) {
    return 'Diese Email-Adresse ist leider nicht gültig - bitte überprüfe noch einmal deine Eingabe.';
  } else if (error?.response?.status === 500) {
    return 'Leider gab es ein technisches Problem - bitte versuch es später noch einmal.';
  } else return error;
};

const NewsletterSection = ({ headerText, brandInfo = null, brandPageFullWidth = true }) => {

  const { register, handleSubmit, formState: { errors }, reset } = useForm();
  const [newsletterStatus, setNewsletterStatus] = useState();
  const [userEmailAddress, setUserEmailAddress] = useState('');
  const [isSubscribedToNewsletter, setSubscribedToNewsletter] = useState(false);

  const { useCustomTheme } = useSelector((state) => state.settings);


  useEffect(() => {
    if (isAuth()) {
      // TODO use state management system to prevent extra HTTP call
      getConsumer()
        .then((response) => {
          if (response.data.isNewslettersSubscribed) {
            setSubscribedToNewsletter(true);
          }

          const firstEmailAddress = response.data.emails.find(
            (email) => email.primaryEmail
          );
          setUserEmailAddress(firstEmailAddress.email);
        })
        .catch(() => {
          setSubscribedToNewsletter(false);
        });
    }
  }, [setUserEmailAddress, setSubscribedToNewsletter]);

  const onSubmit = async (data) => {
    try {
      const formBody = new URLSearchParams();
        
      // Use uppercase keys as shown in the form data
      formBody.append("EMAIL", data.newsletter_input);
      formBody.append("REGISTRATION_URL", window.location.href);
      formBody.append("locale", "en");
      formBody.append("email_address_check", "");

      if (brandInfo) {
        formBody.append("BRAND", brandInfo.public.name);
      }

      const userLoggedIn = isAuth();
      formBody.append("LOGGED_IN", !!userLoggedIn);

      if (userLoggedIn) {
        const userData = JSON.parse(localStorage.getItem('userAttributes'));
        formBody.append("USERNAME", `${userData.firstName} ${userData.surname}`);
      }

      const response = await fetch(
        "https://sibforms.com/serve/MUIFAA38mmvgan7tL-a8FN_ROiU0dw7wR2beR18aPuE-IY2vUy2ydiGy4qEH-A2kKLyasETVm7aTJGEoubz5x_Q4-GOhX4Vj1GJZ3k_wl1W_T30al29_NzQ9UdwpQgilKuSxTSOYUPjkdWIEq2tz80ceM5S8o2Kaz-zzVDt6hDgTKaddUPZXPI-Tp4u63KaLyfyF8Je4ZDL3lVi7?isAjax=1",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: formBody.toString(),
        }
      );

      const responseData = await response.json();

      if (response.ok && responseData.success) {
        setNewsletterStatus({
          type: 'success',
          message: responseData.message || 'Your subscription has been successful.',
        });
        reset();
        
        // Handle redirect if provided
        if (responseData.redirect) {
          window.location.href = responseData.redirect;
        }
      } else {
        setNewsletterStatus({
          type: 'danger',
          message: 'Subscription failed. Please try again.',
        });
        reset();
      }
    } catch (error) {
      setNewsletterStatus({
        type: 'danger',
        message: getErrorMessage(error.request.responseText),
      });
    }
  };

  if (isSubscribedToNewsletter || useCustomTheme) {
    return null;
  }

  let sectionClass = "pad-t-2 newsletter-section"
  if((brandInfo && !brandPageFullWidth)) {
    sectionClass += " brand br-15 mx-0 pad-b-6 newsletter-brand-section mb-35"
  } else {
    sectionClass += " pad-b-4"
  }

  return (
    <Section
      background="#e6e6e6"
      className={sectionClass}
      customPadding={true}
      rowClass='justify-content-center'
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        {headerText && !!headerText.length && (
          <div className={`col-sm-12 ${brandInfo ? 'newsletter-brand-padding' : 'pad-t-2'} d-flex justify-content-center`}>
            <h2 className="main-header text-center">
              {headerText}
            </h2>
          </div>
        )}
        {brandInfo && (
          <div className={`m-auto newsletter-info ${brandInfo ? 'newsletter-brand-padding' : 'pad-t-2'}`}>
            {brandPageFullWidth && (
              <h4 className="text-center brand-newsletter-subtext">
                Verpasse nie wieder Coupons von {brandInfo.public.name}
              </h4>
            )}

            {!brandPageFullWidth && (
              <h2 className="text-center brand-newsletter-subtext">
                Werde benachrichtigt, sobald der neue Coupon von {brandInfo.public.name} online ist
              </h2>
            )}
          </div>
        )}
        <div className={`${brandInfo ? 'col-lg-10 offset-lg-1' : 'col-lg-6 offset-lg-3'} col-sm-10 offset-sm-1 mar-t-3`}>
          <div className=" input-group">
            <input
              type="email"
              className={`form-control newsletterInput ${brandInfo ? 'brand-newsletterInput' : ''}`}
              placeholder="E-Mail Adresse"
              aria-label="Input"
              aria-describedby="basic-addon2"
              defaultValue={userEmailAddress}
              {...register('newsletter_input', {
                required: true
              })}
            />
            <div className="input-group-append">
              <button className={`btn newsletter-btn ${brandInfo ? 'brand-btn': ''}`} type="submit">
                anmelden
              </button>
            </div>
          </div>

          {errors.newsletter_input && (
            <p className="inline-error">please enter valid email</p>
          )}
        </div>
        <div className={`${brandInfo ? 'newsletter-brand-padding col-sm-10 offset-sm-1': 'pt-3 col-sm-8 offset-sm-2'} col-xs-12`}>
          <small className={`d-inline-block text-center text-s ${brandInfo ? 'brand-newsletter-info' : ''}`}>
            Mit dem Klick auf „anmelden" meldest du dich für den CaptainCoupon-Newsletter an und akzeptierst unsere{' '}
            <Link href="/datenschutz" legacyBehavior>
              <a>Datenschutzbestimmungen.</a>
            </Link>{' '}
          </small>
          {newsletterStatus && (
            <div
              className={`alert alert-${newsletterStatus.type} text-center p-3`}
            >
              {newsletterStatus.message}
            </div>
          )}
        </div>
      </form>
    </Section>
  );
};
export default NewsletterSection;