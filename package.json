{"name": "captain-coupon-web", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f ./env/.env.staging next dev", "dev:staging": "env-cmd -f ./env/.env.staging next dev", "dev:acceptance": "env-cmd -f ./env/.env.acceptance next dev", "dev:production": "env-cmd -f ./env/.env.production next dev", "build": "next build ", "build:staging": "env-cmd -f ./env/.env.staging next build", "build:acceptance": "env-cmd -f ./env/.env.acceptance next build", "build:production": "env-cmd -f ./env/.env.production next build", "build:development": "env-cmd -f ./env/.env.development next build", "start": "./node_modules/next/dist/bin/next start -p 8080", "start:staging": "env-cmd -f ./env/.env.staging next start -p 8080", "start:acceptance": "env-cmd -f ./env/.env.acceptance next start -p 8080", "start:production": "env-cmd -f ./env/.env.production next start -p 8080", "start:development": "env-cmd -f ./env/.env.development next start -p 8080"}, "dependencies": {"@react-pdf/renderer": "^3.1.14", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/postcss": "^4.0.14", "@types/node": "^20.11.5", "aws-amplify": "^6.0.12", "axios": "^1.6.5", "bootstrap": "^4.5.0", "cookie": "^0.6.0", "cypress-react-selector": "^3.0.0", "env-cmd": "^10.1.0", "framer-motion": "^2.9.1", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "jwt-simple": "^0.5.6", "next": "^14.0.4", "next-compose-plugins": "^2.2.1", "next-redux-wrapper": "^8.1.0", "postcss": "^8.5.3", "react": "^18.2.0", "react-date-picker": "^10.6.0", "react-datepicker": "^8.2.0", "react-dom": "^18.2.0", "react-facebook-login": "^4.1.1", "react-hook-form": "^7.49.3", "react-html-parser": "^2.0.2", "react-icons": "^4.12.0", "react-player": "^2.14.1", "react-redux": "^9.1.0", "react-visibility-sensor": "^5.1.1", "reactstrap": "^8.5.1", "reactstrap-date-picker": "0.0.11", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "sass": "^1.69.7", "tailwindcss": "^4.0.14"}, "devDependencies": {"cypress": "^13.6.2", "env-cmd": "^10.1.0"}}