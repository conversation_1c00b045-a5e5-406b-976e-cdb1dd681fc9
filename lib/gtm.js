export const GTMPageView = (url) => {
  const pageEvent = {
    event: 'pageview',
    page: url,
  };
  window && window.dataLayer && window.dataLayer.push(pageEvent);
  return pageEvent;
};

// Send custom event with custom props to Google Tag Manager
// export const GTMCustomEvent = (title) => {
//   window.dataLayer.push({
//     event: 'event',
//     eventProps: {
//       category: 'category',
//       action: 'action',
//       label: 'label',
//       value: 'value',
//     },
//   });
// };
