export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_TRACKING_ID;

// https://developers.google.com/analytics/devguides/collection/gtagjs/pages
export const pageview = (url) => {
  window.gtag('config', GA_TRACKING_ID, {
    page_path: url,
  });
};

// https://developers.google.com/analytics/devguides/collection/gtagjs/events
export const event = ({ action, category, label, value }) => {
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  });
};

// https://developers.google.com/analytics/devguides/collection/gtagjs/screens
export const screenView = ({
  screenName,
  appName,
  appId,
  appVersion,
  appInstallerId,
}) => {
  window.gtag('event', 'screen_view', {
    screen_name: screenName,
    app_name: appName,
    app_id: appId,
    app_version: appVersion,
    app_installer_id: appInstallerId,
  });
};
