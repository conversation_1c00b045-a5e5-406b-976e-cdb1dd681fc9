import cookie from 'cookie';
import { getNewIdToken } from 'services/auth.service';

export const getRequestCookies = function (request) {
  return cookie.parse(request?.headers?.cookie || '');
};

export const getJWTPayload = function (jwtToken) {
  return JSON.parse(Buffer.from(jwtToken.split('.')[1], 'base64').toString());
};

export const getJWTCookieExpiration = (jwtToken) =>
  new Date(getJWTPayload(jwtToken).exp * 1000).toUTCString();

export const isJWTExpired = function (jwtToken) {
  return new Date().getTime() / 1000 > getJWTPayload(jwtToken).exp;
};

export const getCustomHeadersFromReq = async function (request, response) {
  const cookies = getRequestCookies(request);
  if (!cookies.idToken) {
    // not logged in
    return null;
  } else {
    try {
      let idToken;
      if (isJWTExpired(cookies.idToken)) {
        try {
          const responseToken = await getNewIdToken(cookies.refreshToken);
          idToken = responseToken.data.idToken;
          // set idToken
          response.setHeader('set-cookie', [
            `idToken=${idToken};expires=${getJWTCookieExpiration(idToken)};`,
          ]);
          response.setHeader('location', request?.url);
          response.statusCode = 302;
          response.end();
        } catch (error) {
          throw 'TokenExpired';
        }
      } else {
        idToken = cookies.idToken;
      }
      return { Authorization: `Bearer ${idToken}` };
    } catch (error) {
      // logout
      throw 'TokenExpired';
    }
  }
};

export const getNewToken = (cookies, customHeaders) => {
  return (
    cookies.idToken &&
    cookies.idToken !== customHeaders?.Authorization.split(' ')[1] &&
    customHeaders?.Authorization.split(' ')[1]
  );
};

export const logout = (response) => {
  response.setHeader('set-cookie', [
    'idToken=>;expires=Thu, 01 Jan 1970 00:00:00 GMT;',
  ]);
  response.setHeader('location', '/logout');
  response.statusCode = 302;
  response.end();
};
