import jwt from 'jsonwebtoken';
import * as subscriptionService from 'services/subscription.service';
import * as EmailErrors from 'components/SettingsSegments/EmailErrorMessages';

export const CodeType = {
  GOLDEN: 'GOLDEN',
  GENERIC: 'GENERIC',
  REGULAR: 'REGULAR',
};

const CODE_NOT_FOUND = 'Code nicht gefunden (na)';

export const debounce = (func, wait, immediate) => {
  let timeout;
  return function () {
    const context = this,
      args = arguments;
    const later = function () {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
};

export const hasLowerCase = (stringToTest, skipValidation) => {
  if (skipValidation) {
    return true;
  }

  return (
    Number.isNaN(+stringToTest) && stringToTest.toUpperCase() !== stringToTest
  );
};

export const hasSpecialCharacter = (strToTest, skipValidation) => {
  if (skipValidation) {
    return true;
  }

  return /[\s~`!@#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?()\._]/g.test(strToTest);
};

export const getPackageForSubscriptionDiscountCode = async function (
  code,
  setDiscountCode,
  errorCallback
) {
  errorCallback(null);

  if (!code) {
    return true;
  }

  try {
    const response =
      await subscriptionService.validateAndGetProductPackageForDiscountCode(
        code
      );
    if (response?.data) {
      setDiscountCode({ product: response.data, code });
      return response.data;
    }
  } catch (error) {
    const errorMessage = error?.response?.data;
    if (errorMessage) {
      if (
        errorMessage.indexOf('Code [') !== -1 ||
        errorMessage.indexOf('Product Package not found') !== -1
      ) {
        errorCallback(CODE_NOT_FOUND);
      } else {
        errorCallback(errorMessage);
      }
    } else {
      errorCallback(CODE_NOT_FOUND);
    }
  }
  return false;
};

const formatDate = (date) => {
  return Intl.DateTimeFormat('de-DE', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(new Date(date));
};

const subscriptionEndDate = (subscription) => {
  try {
    const date = new Date(
      subscription?.nextPaymentDate || subscription?.endDate
    );
    return formatDate(date);
  } catch (error) {
    return '';
  }
};

export const subscriptionEndDateMessage = (subscription) => {
  const {
    status,
    initialPeriod,
    productPackage: {
      recurring,
      recurringAmount,
      recurringInterval,
      duration,
    } = {},
  } = subscription;

  switch (status) {
    case 'ACTIVE':
      switch (recurring) {
        case false:
          if (duration === 99999) {
            return <></>;
          } else {
            return (
              <ul className="pl-20">
                <li>
                  Deine Mitgliedschaft läuft bis zum{' '}
                  {subscriptionEndDate(subscription)}.
                </li>
                <li>Danach endet deine Mitgliedschaft automatisch.</li>
                <li>Du kannst jederzeit vorzeitig zum Monatsende kündigen.</li>
              </ul>
            );
          }
        case true:
          return (
            <ul className="pl-20">
              {initialPeriod ? (
                <>
                  <li>
                    Deine Mitgliedschaft läuft bis zum{' '}
                    {subscriptionEndDate(subscription)}.
                  </li>
                  <li>
                    {parseInt(recurringInterval, 10) === 1
                      ? `Danach zahlst du monatlich ${recurringAmount} Euro.`
                      : `Danach zahlst du ${recurringAmount} Euro / ${recurringInterval} Monate.`}
                  </li>
                </>
              ) : (
                <>
                  <li>
                    {parseInt(recurringInterval, 10) === 1
                      ? `Du zahlst monatlich ${recurringAmount} Euro.`
                      : `Du zahlst ${recurringAmount} Euro / ${recurringInterval} Monate.`}
                  </li>
                </>
              )}
              <li>Du kannst jederzeit vorzeitig zum Monatsende kündigen.</li>
            </ul>
          );
      }
    case 'CANCELED':
      return (
        <ul className="pl-20">
          {subscription?.cancelledDate && (
            <li>
              Du hast am {formatDate(subscription?.cancelledDate)} gekündigt.
            </li>
          )}
          <li>
            Deine aktuelle Mitgliedschaft läuft noch bis zum{' '}
            {subscriptionEndDate(subscription)}.
          </li>
        </ul>
      );
    case 'EXPIRED':
      return (
        <ul className="pl-20">
          <li>
            Deine Mitgliedschaft ist am {subscriptionEndDate(subscription)}{' '}
            abgelaufen.
          </li>
          <li>
            Du kannst jederzeit eine neue Mitgliedschaft durch klick auf
            „Mitgliedschaft starten“ erneuern.
          </li>
        </ul>
      );
    case 'PENDING':
      return 'Zahlung steht aus.';
    case 'SUSPENDED':
      return 'Der Zahlungsprozess wurde abgebrochen. Bitte Zahlungsmethode prüfen und erneut registrieren.';
    case 'COMPLETED':
      return 'Abgeschlossen.';
    case 'REFUND':
      return 'Deine Bezahlung wurde erstattet, die Mitgliedschaft ist daher momentan inaktiv.';
    // TODO check if the other statuses should be handled ('CREATED', 'REFUSED', 'FAILED', 'PAYMENT_CANCELED')
    default:
      return '';
  }
};

export const setErrorMessageForEditEmail = (
  errorMessage,
  setInlineMessage,
  setToastMessage
) => {
  switch (errorMessage) {
    case EmailErrors.EMAIL_EXISTS_en:
      setInlineMessage('email', {
        message: EmailErrors.EMAIL_EXISTS_de,
      });
      break;
    case EmailErrors.CONSUMER_NOT_FOUND_en:
      setInlineMessage('email', {
        message: EmailErrors.CONSUMER_NOT_FOUND_de,
      });
      break;
    case EmailErrors.NOT_SUPPORTED_OPERATION_en:
      setInlineMessage('email', {
        message: EmailErrors.NOT_SUPPORTED_OPERATION_de,
      });
      break;
    case EmailErrors.EMAIL_NOT_FOUND_en:
      setToastMessage(EmailErrors.EMAIL_NOT_FOUND_de, 'error');
      break;
    case EmailErrors.CANNOT_REMOVE_PRIMARY_EMAIL_en:
      setToastMessage(EmailErrors.CANNOT_REMOVE_PRIMARY_EMAIL_de, 'error');
      break;
    case EmailErrors.CANNOT_REMOVE_FACEBOOK_EMAIL_en:
      setToastMessage(EmailErrors.CANNOT_REMOVE_FACEBOOK_EMAIL_de, 'error');
      break;
    case EmailErrors.EMAIL_NOT_CONFIRMED_en:
      setToastMessage(EmailErrors.EMAIL_NOT_CONFIRMED_de, 'error');
      break;
    case EmailErrors.EMAIL_ALREADY_PRIMARY_en:
      setToastMessage(EmailErrors.EMAIL_ALREADY_PRIMARY_de, 'error');
      break;
    default:
      setToastMessage(errorMessage, 'error');
  }
};

export const toggleCouponFavorite = (coupons, id) =>
  coupons.map((coupon) =>
    coupon.id === id ? { ...coupon, isFavourite: !coupon.isFavourite } : coupon
  );

export const loadScript = (scriptUrl, callback) => {
  // TODO check if this func is needed
  const script = document.createElement('script');
  script.src = scriptUrl;

  script.async = true;
  script.onload = () => callback();

  document.body.appendChild(script);
};

const fallbackCopyTextToClipboard = (text) => {
  var textArea = document.createElement('textarea');
  textArea.value = text;

  // Avoid scrolling to bottom
  textArea.style.top = '0';
  textArea.style.left = '0';
  textArea.style.position = 'fixed';

  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    var successful = document.execCommand('copy');
  } catch (err) {
    console.error('Fallback: Oops, unable to copy', err);
  }

  document.body.removeChild(textArea);
};

export const copyTextToClipboard = (text) => {
  if (!navigator.clipboard) {
    fallbackCopyTextToClipboard(text);
    return;
  }
  navigator.clipboard.writeText(text).then(null, function (err) {
    console.error('Async: Could not copy text: ', err);
  });
};

export const paymentOptions = [
  // 'PAYPAL',
  'CREDITCARD',
  // 'MASTERCARD',
  // 'VISA',
  'SEPA',
  // 'INVOICE',
  // 'GOOGLE_PAY',
  // 'APPLE_PAY',
];

export const setWithExpiry = (key, value, ttl) => {
  const now = new Date();

  // `item` is an object which contains the original value
  // as well as the time when it's supposed to expire
  const item = {
    value: value,
    expiry: now.getTime() + ttl,
  };
  localStorage.setItem(key, JSON.stringify(item));
};

export const getParsedDate = (originalDate) => {
  const date = new Date(originalDate);
  return Intl.DateTimeFormat('de-DE', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date);
};

export const getWithExpiry = (key) => {
  const itemStr = localStorage.getItem(key);
  // if the item doesn't exist, return null
  if (!itemStr) {
    return null;
  }
  const item = JSON.parse(itemStr);
  const now = new Date();
  // compare the expiry time of the item with the current time
  if (now.getTime() > item.expiry) {
    // If the item is expired, delete the item from storage
    // and return null
    localStorage.removeItem(key);
    return null;
  }
  return item.value;
};

export const getJWTPayload = (jwtToken) =>
  jwt.decode(jwtToken, { complete: true }).payload;

export const getJWTCookieExpiration = (jwtToken) =>
  new Date(getJWTPayload(jwtToken).exp * 1000).toUTCString();

export const getDatePlusNMonths = (months) => {
  const now = new Date();
  return new Date(now.setMonth(now.getMonth() + months));
};

const isObject = (object) => {
  return object != null && typeof object === 'object';
};

export const deepEqualObjects = (object1, object2) => {
  const keys1 = Object.keys(object1);
  const keys2 = Object.keys(object2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    const val1 = object1[key];
    const val2 = object2[key];
    const areObjects = isObject(val1) && isObject(val2);
    if (
      (areObjects && !deepEqualObjects(val1, val2)) ||
      (!areObjects && val1 !== val2)
    ) {
      return false;
    }
  }

  return true;
};

export const getCouponAltText = (coupon) => {
  let discountDescription;

  if (coupon.discountType === "AMOUNT") {
    discountDescription = `${coupon.discountValue} Euro`;
  } else if (coupon.discountType === "PERCENTAGE") {
    discountDescription = `${coupon.discountValue} Prozent`;
  } else {
    discountDescription = coupon?.discountType?.toLowerCase();
  }

  return `${coupon.brandSlug} ${discountDescription} coupon`;
};

// Remove the hook from this utility function
export const displayCouponCode = async (brandSlug, router = null) => {
  // Only try to use router if it's provided
  console.log('clicked')
  if (router) {
    router.push(`/brand/${brandSlug}`);
  } else {
    // Fallback to window.location if router isn't available
    window.location.href = `/brand/${brandSlug}`;
  }
  window.scrollTo(0, 0);
};