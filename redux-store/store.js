import { configureStore } from '@reduxjs/toolkit';
import { thunk } from 'redux-thunk';
import { createWrapper } from 'next-redux-wrapper';
import rootReducer from './rootReducer';

const makeStore = () => 
  configureStore({
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) => 
      getDefaultMiddleware().concat(thunk),
    devTools: process.env.NODE_ENV !== 'production',
  });

export const wrapper = createWrapper(makeStore);