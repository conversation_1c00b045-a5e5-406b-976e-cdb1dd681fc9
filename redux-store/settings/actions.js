import * as actionTypes from './types';
import * as SubscriptionService from 'services/subscription.service';
import * as ConsumerService from 'services/consumer.service';
import * as AdyenService from 'services/adyen.service';

import * as MESSAGES from 'util/settingsMessages';

export const editUser = (newData, setUpdateStatus) => (dispatch) => {
  // TODO use async/await in a try..catch block here

  ConsumerService.editUser(newData)
    .then(() => {
      setUpdateStatus({
        type: 'success',
        message: MESSAGES.ProfileUpdatedSuccess,
      });
    })
    .catch((error) => {
      console.error({ error });
      setUpdateStatus({
        type: 'danger',
        message: `${MESSAGES.ProfileUpdatedFail}`,
      });
    })
    .finally(() => {
      dispatch({ type: actionTypes.SET_SHOW_EDIT_USER_FORM, payload: false });
    });
};

export const getUserSubscription = () => async (dispatch) => {
  try {
    const subscription = await SubscriptionService.getSubscription();
    dispatch({
      type: actionTypes.SET_SUBSCRIPTION_DATA,
      payload: subscription.data,
    });
  } catch (error) {
    console.error(error);
  }
};

export const checkUserSubscription = () => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SUBSCRIPTION_PROCESSING,
    payload: true,
  });
  try {
    const result = await SubscriptionService.checkValidSubscription();
    dispatch({
      type: actionTypes.SET_VALID_SUBSCRIPTION,
      payload: result.data,
    });
  } catch (error) {
    dispatch({
      type: actionTypes.SET_VALID_SUBSCRIPTION,
      payload: false,
    });
  }
};

export const cancelUserSubscription = (setUpdateStatus) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_CANCEL_SUBSCRIPTION_MODAL,
    payload: false,
  });
  try {
    await SubscriptionService.cancelSubscription();
    setUpdateStatus({
      type: 'success',
      message: `Abonnement gekündigt`,
    });
    dispatch({
      type: actionTypes.SET_VALID_SUBSCRIPTION,
      payload: false,
    });
  } catch (error) {
    setUpdateStatus({
      type: 'danger',
      message: `Kündigung der Mitgliedschaft fehlgeschlagen.`,
    });
  }
  dispatch({ type: actionTypes.SET_MODAL, payload: false });
  dispatch({
    type: actionTypes.SET_SUBSCRIPTION_PROCESSING,
    payload: false,
  });
};

export const deleteAccount = (setUpdateStatus, callbackFn) => (dispatch) => {
  // TODO use async/await in a try..catch block here

  ConsumerService.deleteUser()
    .then(() => {
      dispatch({ type: actionTypes.SET_MODAL, payload: false });
      setUpdateStatus({
        type: 'success',
        message: MESSAGES.ProfileDeleteSuccess,
      });
      callbackFn();
    })
    .catch((error) => {
      setUpdateStatus({
        type: 'danger',
        message: error.message,
      });
    });
};

export const getProductPackage = () =>
  async function (dispatch) {
    try {
      const subscriptionPackage = await AdyenService.getSubscriptionPackage();

      dispatch({
        type: actionTypes.GET_SUBSCRIPTION_PACKAGE_SUCCESS,
        payload: subscriptionPackage.data,
      });
    } catch (error) {
      dispatch({
        type: actionTypes.GET_SUBSCRIPTION_PACKAGE_ERROR,
      });
    }
  };
