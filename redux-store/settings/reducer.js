import {
  SET_SUBSCRIPTION_PROCESSING,
  SET_VALID_SUBSCRIPTION,
  SET_MODAL,
  SET_CANCEL_SUBSCRIPTION_MODAL,
  SET_SUBSCRIPTION_DATA,
  SET_SHOW_EDIT_USER_FORM,
  GET_SUBSCRIPTION_PACKAGE_SUCCESS,
  GET_SUBSCRIPTION_PACKAGE_ERROR,
} from './types';

const ACCENT_COLOR = { BLACK: 'black', WHITE: 'white' };

const initialState = {
  subscriptionProcessing: false,
  hasValidSub: false,
  subscriptionData: {},
  modal: false,
  modalCancelSubscrtiption: false,
  showEditUserForm: false,
  useCustomColors: undefined,
  useCustomTheme: false,
  homePageTitle: null,
  homePageBodyText: null,
  accentBackgroundColor: '#81e9f0',
  accentColor: ACCENT_COLOR.BLACK,
};

const settingsReducer = (state = initialState, { type, payload }) => {
  switch (type) {
    case SET_SUBSCRIPTION_PROCESSING:
      return { ...state, subscriptionProcessing: payload };
    case SET_VALID_SUBSCRIPTION:
      return {
        ...state,
        hasValidSub: payload,
        subscriptionProcessing: false,
      };
    case SET_MODAL:
      return {
        ...state,
        modal: payload,
      };
    case SET_CANCEL_SUBSCRIPTION_MODAL:
      return {
        ...state,
        modalCancelSubscrtiption: payload,
      };
    case SET_SUBSCRIPTION_DATA:
      return {
        ...state,
        subscriptionData: payload,
      };
    case SET_SHOW_EDIT_USER_FORM:
      return {
        ...state,
        showEditUserForm: payload,
      };
    case GET_SUBSCRIPTION_PACKAGE_SUCCESS:
      return {
        ...state,
        useCustomColors: payload.useCustomColours,
        useCustomTheme: payload.useCustomTheme,
        homePageTitle: payload.homePageTitle,
        homePageBodyText: payload.homePageBodyText,
        accentBackgroundColor: payload.accentBackgroundColor?.toLowerCase(),
        accentColor: payload.accentColor?.toLowerCase(),
      };
    case GET_SUBSCRIPTION_PACKAGE_ERROR:
      return {
        ...state,
        useCustomColors: initialState.useCustomColours,
        useCustomTheme: initialState.useCustomTheme,
        homePageTitle: initialState.homePageTitle,
        homePageBodyText: initialState.homePageBodyText,
        accentBackgroundColor: initialState.accentBackgroundColor,
        accentColor: initialState.accentColor,
      };
    default:
      return state;
  }
};

export default settingsReducer;
