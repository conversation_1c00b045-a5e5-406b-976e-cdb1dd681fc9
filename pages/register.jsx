import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { GrClose } from 'react-icons/gr';
import { Container } from 'reactstrap';
import Row from 'reactstrap/lib/Row';
import Col from 'reactstrap/lib/Col';

import {
  Layout,
  Section,
  Stepper,
  ImageBanner,
  ModalGeneric,
} from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { RegisterStepOne, RegisterStepTwo, RegisterStepThree } from 'sections';
import RegisterCompleted from './register_completed';
import {
  createUser,
  editUser,
  getConsumer,
  setUserProductPackage,
  setUserRegistrationCode,
} from 'services/consumer.service';
import { isAuth } from 'services/auth.service';
import {
  checkValidSubscription,
  getSubscription,
} from 'services/subscription.service';
import * as nodeHelpers from 'util/nodeHelpers';
import styles from '../styles/sass/_registerPage.module.scss';
import MediaOutlets from '../components/MediaOutlets/MediaOutlets';
import StepIndicator from '../components/StepIndicator/StepIndicator';

const STEP_LABELS = [
  'Mitgliedschaft',
  'Konto erstellen',
  'Zahlungsdetails',
  'Fertig',
];

const filterEditableFields = (data) => {
  const fieldsForEditUser = [
    'firstName',
    'surname',
    'dateOfBirth',
    'gender',
    'houseNumber',
    'place',
    'postalCode',
    'street',
  ];
  // Filter non-editable fields
  return Object.keys(data).reduce(
    (acc, key) =>
      fieldsForEditUser.includes(key) ? { ...acc, [key]: data[key] } : acc,
    {}
  );
};

export async function getServerSideProps(context) {
  let customHeaders = null;
  try {
    customHeaders = await nodeHelpers.getCustomHeadersFromReq(
      context.req,
      context.res
    );
  } catch (error) {
    nodeHelpers.logout(context.res);
    return {};
  }

  try {
    const [hasValidSubscriptionResponse, subscriptionDataResponse] =
      await Promise.allSettled([
        checkValidSubscription(customHeaders),
        getSubscription(customHeaders),
      ]);

    if (
      hasValidSubscriptionResponse.value?.data ||
      subscriptionDataResponse.value?.data?.status === 'CANCELED'
    ) {
      console.log('redirect');
      context.res.writeHead(302, { Location: '/' });
      context.res.end();
    }
  } catch (error) {
  } finally {
    return { props: {} };
  }
}

export default function Register({
  specialAudience, // 'corporate' | 'club' | undefined
}) {
  const router = useRouter();
  const { activated } = router.query;

  const { addMessage } = useAPIError();

  const [currentStep, setCurrentStep] = useState(activated ? 2 : 0);
  const [userDetails, setUserDetails] = useState();
  const [productInfo, setProductInfo] = useState();
  const [modalOpen, setModalOpen] = useState(false);

  const toggle = () => setModalOpen((modalOpen) => !modalOpen);

  const [stepOneData, setStepOneData] = useState({});
  const [stepTwoData, setStepTwoData] = useState({});

  const formData = useRef({});

  useEffect(() => {
    const urlSearchParams = new URLSearchParams(window.location.search)    
    const data = urlSearchParams.get('activated')
    if (data) {
      hideLoginElements();
      setCurrentStep(2);
    }
    return () => {
      showLoginElements();
    };
  }, []);

  const registerUser = (data) => {
    const {
      acceptTerms,
      acceptTerms2,
      registrationCode,
      productPackageId,
      ...userFormData
    } = data;

    const payload = {
      ...userFormData,
      ...(registrationCode ? { registrationCode } : { productPackageId }),
      dateOfBirth: new Date(userFormData.dateOfBirth).toISOString(),
    };

    return createUser(payload);
  };

  const updateUser = (data) => {
    const { registrationCode, productPackageId } = data;
    const editUserData = filterEditableFields(data);

    return Promise.allSettled([
      editUser({
        ...editUserData,
        dateOfBirth: new Date(editUserData.dateOfBirth).toISOString(),
      }),
      registrationCode
        ? setUserRegistrationCode(registrationCode)
        : setUserProductPackage(productPackageId),
    ]);
  };

  const hideLoginElements = () => {
    localStorage.setItem('hideRegister', true);
    localStorage.setItem('firstLogin', true);
  };

  const showLoginElements = () => {
    localStorage.removeItem('hideRegister');
    localStorage.removeItem('firstLogin');
  };

  const toStepOne = (data) => {
    const { registrationCode, productPackageId, ...stepTwoData } = data;
    setStepTwoData(stepTwoData);
    setCurrentStep(0);
  };

  const toStepTwo = (data) => {
    setStepOneData(data);
    setCurrentStep(1);
  };

  const toStepThree = async (data) => {
    if (isAuth()) {
      const responses = await updateUser(data);
      const failedRequests = responses.filter(
        (response) => response.status !== 'fulfilled'
      );
      if (!failedRequests.length) {
        setStepTwoData((stepTwoData) => ({
          ...stepTwoData,
          ...filterEditableFields(data),
        }));
        setCurrentStep(2);
      } else {
        failedRequests.forEach((response) => {
          addMessage(response.reason?.response?.data, 'error');
        });
      }
    } else {
      console.log('aaaaaaaaaa')
      await registerUser(data);
      setModalOpen(true);
    }
  };

  const stepSections = [
    <RegisterStepOne
      stepOneData={stepOneData}
      toStepTwo={toStepTwo}
      setProductInfo={setProductInfo}
      specialAudience={specialAudience}
    />,
    <RegisterStepTwo
      stepTwoData={{ ...stepTwoData, ...stepOneData }}
      toStepOne={toStepOne}
      toStepThree={toStepThree}
    />,
    <RegisterStepThree
      formData={formData}
      stepOneData={stepOneData}
      hasUserData={isAuth()}
      toStepTwo={() => setCurrentStep(1)}
      toStepFour={() => {
        setCurrentStep(3);
      }}
    />,
    <RegisterCompleted onContinue={() => router.push('/')} />,
  ];

  useEffect(() => {
    if (isAuth()) {
      getConsumer().then((response) =>
        setUserDetails({
          ...response.data,
          acceptTerms: '1',
          acceptTerms2: '1',
        })
      );
    }

    window.addEventListener('beforeunload', showLoginElements);

    return () => {
      window.removeEventListener('beforeunload', showLoginElements);
    };
  }, []);

  useEffect(() => {
    if (userDetails) {
      const { emails, productPackage, ...simpleUserDetails } = userDetails;
      setStepOneData({
        productPackageId: productPackage?.productPackageId,
        registrationCode: simpleUserDetails.registrationCode,
      });
      setStepTwoData({ ...simpleUserDetails, email: emails[0].email });
    }
  }, [userDetails]);

  useEffect(() => {
    if (activated) {
      hideLoginElements();
      setCurrentStep(2);
    }

    return () => {
      showLoginElements();
    };
  }, [activated]);

  return (
    <>
      <Head>
        <title>Captain Coupon:: Register</title>
      </Head>
      <Layout register customContainerFluidClass="container-footer-bottom">
        <div>
          <ModalGeneric modal={modalOpen} className={styles.regModal}>
            <div className="reg-side-title">
              <h1 className="text-lg-center pad-t-3 pad-b-2">
                Bitte bestätige deine E-Mail Adresse!
              </h1>
              <p>
                Wir haben dir eine E-Mail zur Bestätigung deiner E-Mail Adresse
                gesendet. Bitte klicke auf den darin enthaltenen Link um mit der
                Registrierung fortzufahren. Bitte schau auch im Spam-Ordner
                nach, solltest du die E-Mail nicht direkt finden.
              </p>
            </div>
            <button
              className="reg-close-btn"
              onClick={() => {
                toggle();
                setTimeout(() => router.push('/'), 1000);
              }}
            >
              <GrClose className="reg-close" />
            </button>
          </ModalGeneric>
        </div>
        <div className="mb-4">
          {!specialAudience && (
            <>
              <div className="container d-none d-sm-block w-100">
                <Stepper steps={STEP_LABELS} currentStep={currentStep} />
              </div>
              <div className="container d-block d-sm-none w-100">
                <div className="row">
                  <StepIndicator
                    currentStep={currentStep + 1}
                    maxStep={STEP_LABELS.length}
                  />
                </div>
              </div>
            </>
          )}
        </div>

        {stepSections[currentStep]}
        {currentStep === 0 ? (
          <Container className={styles.bottomContainer}>
            {productInfo?.map((product) => (
              <Row className="mb-1">
                <Col>
                  <p className={styles.additionalInformation}>{product.info}</p>
                </Col>
              </Row>
            ))}

            {specialAudience && currentStep == 0 && (
              <Row>
                <Col>
                  <MediaOutlets />
                </Col>
              </Row>
            )}
          </Container>
        ) : null}
      </Layout>
    </>
  );
}
