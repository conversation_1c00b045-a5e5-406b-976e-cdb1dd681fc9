import { Layout, Section } from '../components';
import Head from 'next/head';

import { useRouter } from 'next/router';
import * as ERROR_CODES from '../components/LoginModal/ErrorCodes';
import { useEffect, useState } from 'react';
import LoginErrors from '../components/LoginModal/LoginErrors';
import { RegisterErrorMessages } from '../components/RegisterForm/RegisterErrorMessages';
import { hasLowerCase, hasSpecial<PERSON>haracter } from 'util/helpers';
import { useForm } from 'react-hook-form';
import { saveNewPassword } from 'services/auth.service';

const Reset = ({ code, userId }) => {
  const router = useRouter();
  const [username, setUsername] = useState('');
  const [loginStatus, setLoginStatus] = useState();
  useEffect(() => {
    setUsername(localStorage.getItem('resetMail'));
  });
  const { register, handleSubmit, errors, control } = useForm();
  const [pass, setThePass] = useState();

  const handleResetPassword = async (data) => {
    const { email, newPassword } = data;
    router;
    try {
      saveNewPassword(email, code, newPassword);
      localStorage.removeItem('resetMail');
      setLoginStatus(ERROR_CODES.ResetOk);
      setTimeout(async () => {
        await router.push('/');
      }, 3000);
    } catch (err) {
      setLoginStatus(err.code);
    }
  };
  return (
    <>
      <Head>
        <title>Confirm your account</title>
      </Head>
      <Layout home>
        <Section background="#fff">
          <div className="col-sm-12 mar-tb-5 layout-custom-header">
            <div className="header-image-wrap">
              <img
                src="/headers/Captaincoupon_Header_Password-Update.jpg"
                alt="Bunter Papagei von der Seite gesehen"
              />
            </div>
          </div>

          <div className="col-sm-12 mar-b-4">
            <h1 className="sidebar-header mar-b-4 text-center">
              Passwort zurücksetzen
            </h1>
            <form
              className="container"
              onSubmit={handleSubmit(handleResetPassword)}
            >
              <div className="row">
                <div className="col-sm-12">
                  <div className="form-group">
                    <input
                      type="email"
                      name="email"
                      className="form-control"
                      placeholder="E-Mail-Adresse"
                      ref={register({
                        required: true,
                      })}
                      defaultValue={username}
                    />
                    {errors.email && (
                      <p className="inline-error">Dies ist ein Pflichtfeld</p>
                    )}
                  </div>
                </div>
                <div className="col-sm-12"></div>
                <div className="col-sm-12">
                  <div className="form-group">
                    <input
                      type="password"
                      name="newPassword"
                      className="form-control"
                      placeholder="Neues Passwort"
                      onChange={(e) => setThePass(e.target.value)}
                      ref={register({
                        required: RegisterErrorMessages.passRequired,
                        maxLength: 16,

                        minLength: {
                          value: 8,
                          message: RegisterErrorMessages.passMinLength,
                        },
                        validate: {
                          testSpecialCharacter: hasSpecialCharacter,
                          testLowerCase: hasLowerCase,
                        },
                      })}
                    />
                    {errors.newPassword && (
                      <p className="inline-error">
                        {errors.newPassword.message}
                        {RegisterErrorMessages[errors['newPassword'].type]}
                      </p>
                    )}
                  </div>
                </div>

                <div className="col-sm-12">
                  <div className="form-group">
                    <input
                      type="password"
                      name="confirm_password"
                      className="form-control"
                      placeholder="Passwort wiederholen"
                      ref={register({
                        validate: (value) => value === pass,
                      })}
                    />
                    {errors.confirm_password && (
                      <p className="inline-error">
                        {RegisterErrorMessages.confirmPass}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <div className="row pad-t-1">
                <div className="col-12">
                  <LoginErrors
                    status={loginStatus}
                    currentUsername={username}
                  />
                </div>
              </div>
              <div className="row pad-t-1 mar-b-5">
                <div className="col-6">
                  <button className="btn btn-dark">Passwort speichern</button>
                </div>
              </div>
            </form>
          </div>
        </Section>
      </Layout>
    </>
  );
};

export async function getServerSideProps(context) {
  const { req, res, query } = context;
  if (!query.code || !query.userId) {
    res.writeHead(302, {
      Location: '/',
    });
    res.end();
    return { props: {} };
  }
  return {
    props: {
      code: query.code,
      userId: query.userId,
    },
  };
}
export default Reset;
