import Head from 'next/head';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useState } from 'react';
import * as CategoryService from 'services/categories.service';
import * as CouponService from 'services/coupons.service';
import { toggleCouponFavorite } from 'util/helpers';
import {
  Card,
  Layout,
  Like,
  LimitOverlay,
  ModalSearchBlock,
  Pagination,
  SearchBlock,
  Section,
  SelectorCarousel,
} from '../components';
import useAPIError from '../components/APIErrorNotification/useAPIError';
import { displayCouponCode } from '../components/Card/Card';
import { getOverlayMessage } from '../components/LimitOverlay/LimitOverlay';
import NewsletterSection from '../sections/NewsletterSection';
import { getCouponAltText } from '../util/helpers';

const UserMerkliste = () => {
  const [categories, setCategories] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeCategory, setActiveCategory] = useState(-1);

  const rowsPerPage = 20;
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState();

  const [categoryFilters, setCategoryFilters] = useState([]);

  const [coupons, setCoupons] = useState([]);

  const [userQuery, setUserQuery] = useState('');
  const getCategories = async () => {
    setCategories(await CategoryService.getCategories({ pageSize: 120 }));
  };

  const { addMessage } = useAPIError();

  const getCoupons = async (options) => {
    const { data, total } = await CouponService.getFavouriteCoupons(options);
    setCoupons(data);
    setTotal(total);
  };

  const onCategorySelect = useCallback(
    ({ id }) => {
      if (id && id !== -1) {
        setCategoryFilters([
          {
            field: 'category',
            operator: 'EQUAL',
            value: id,
          },
        ]);
        setActiveCategory(id);
        setCurrentPage(1);
      } else {
        setActiveCategory(id);
        setCategoryFilters([]);
      }
    },
    [setCategoryFilters, setActiveCategory, setCurrentPage]
  );

  const categorySelectorValues = useMemo(
    () => [
      { id: -1, label: 'Alle', href: '/rabatte' },
      ...(categories || []).map((category) => ({
        id: category.id,
        label: category.name,
        href: `/rabatte/${category.id}`,
      })),
    ],
    [categories]
  );

  useEffect(() => {
    let categoriesFilter = [];
    if (categoryFilters.length || activeCategory === '-1') {
      categoriesFilter = categoryFilters;
    }
    getCoupons({
      pageSize: rowsPerPage,
      page: currentPage - 1,
      sort: [
        {
          dir: 'ASC',
          field: 'brandName',
        },
      ],
      filters: [...categoriesFilter],
      search: userQuery,
    });

    return () => {};
  }, [total, currentPage, categoryFilters, activeCategory, userQuery]);

  useEffect(() => {
    getCategories();
    return () => {};
  }, []);

  return (
    <>
      <Head>
        <title>CaptainCoupon Merkliste</title>
      </Head>
      <Layout home>
        <Section
          className="pad-b-8 pad-sm-t-5 pad-t-2 "
          background="#ebebeb"
          customPadding={true}
        >
          <div className="col-sm-12">
            <div className="row align-items-center">
              <h1 className="col-9 main-header text-left">Meine Merkliste</h1>
              <div className="col-3 filter-and-sort-section">
                <div className="search-block compact">
                  <SearchBlock
                    className="shorter d-none d-lg-flex"
                    handleChange={setUserQuery}
                  ></SearchBlock>
                  <ModalSearchBlock
                    className="shorter d-block d-lg-none"
                    handleChange={setUserQuery}
                  ></ModalSearchBlock>
                </div>
              </div>
              <div className="col-12">
                <SelectorCarousel
                  className="mt-2 mb-2"
                  options={categorySelectorValues}
                  selectedId={activeCategory}
                  onSelect={onCategorySelect}
                />
              </div>
            </div>
          </div>

          <div className="col-sm-12">
            {loading && <p>loading....</p>}
            {coupons?.length === 0 ? (
              <div className="not-found">
                <h1>
                  Leider haben wir für deine Suche keine Ergebnisse gefunden.
                </h1>
              </div>
            ) : (
              <>
                <div className="row">
                  {coupons.map((item, index) => (
                    <Card
                      className="col-6 col-lg-3 mb-4 white-card "
                      key={index}
                    >
                      <Card.Image
                        url={`/brand/${item.brandSlug}`}
                        src={item.compressedImage || item.image}
                        alt={getCouponAltText(item)}
                      >
                        <Like
                          isLiked={item.isFavourite}
                          id={item.id}
                          onSuccess={() =>
                            setCoupons((coupons) =>
                              toggleCouponFavorite(coupons, item.id)
                            )
                          }
                          onError={(status, data) => {
                            addMessage(data, 'error');
                          }}
                        />
                        <LimitOverlay message={getOverlayMessage(item)} />
                      </Card.Image>
                      <Card.Discount
                        type={item.discountType}
                        shortDescription={item.shortDescription}
                      >
                        {item.discountValue}
                      </Card.Discount>
                      <Card.ClientLogo src={item?.brandLogo} alt={`Logo der Brand ${item.brandSlug} gutschein`} />
                      <Card.Description>
                        Mindestbestellwert:{' '}
                        {item.amountCondition !== 0 ? (
                          <b>{item.amountCondition.toLocaleString('de-DE')}</b>
                        ) : (
                          <b>keiner</b>
                        )}
                        <br />
                        Gültig bis:{' '}
                        <b>
                          {Intl.DateTimeFormat('de-DE', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                          }).format(new Date(item.validationDate))}
                        </b>
                        <br />
                        <Link href={`/brand/${item.brandSlug}`}>
                          <a>Gutscheinbedingungen</a>
                        </Link>
                      </Card.Description>
                      <Card.CouponButton
                        coupon={item}
                        getCouponCode={() => displayCouponCode(item.brandSlug)}
                      >
                        <p>{item.code ? 'Code kopieren' : 'Gutscheincode'}</p>
                      </Card.CouponButton>
                    </Card>
                  ))}
                </div>
                <Pagination
                  count={total || 0}
                  rowsPerPage={rowsPerPage}
                  currentPage={currentPage}
                  onChangePage={(number) => {
                    setCurrentPage(number);
                    executeScroll();
                  }}
                ></Pagination>
              </>
            )}
          </div>
        </Section>
        <NewsletterSection />
      </Layout>
    </>
  );
};

export default UserMerkliste;
