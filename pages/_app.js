import React from 'react';
import Router from 'next/router';
import Script from 'next/script'; // Import next/script instead of using Head for scripts
import { Provider } from 'react-redux';

import { wrapper } from '../redux-store/store';

import APIErrorProvider from '../components/APIErrorNotification/APIErrorProvider';
import { APIErrorNotification } from '../components/APIErrorNotification/APIErrorNotification';
import Theme from '../components/Theme/Theme';
import FontLoader from '../components/FontLoader/FontLoader';
import { montserrat } from '../styles/fonts';
import { GTMPageView } from '../lib/gtm';

import '../styles/global.scss';
import { AnimatePresence } from 'framer-motion';

// Add polyfill for Symbol.observable to fix Redux/DevTools inconsistency
if (typeof window !== 'undefined') {
  const root = window;
  const Symbol = root.Symbol || (root.Symbol = {});
  if (!Symbol.observable) {
    Symbol.observable = Symbol('observable') || '@@observable';
  }
}

Router.events.on('routeChangeComplete', (url) => {
  GTMPageView(url);
});

function App({ Component, pageProps, router }) {
  const { store, props } = wrapper.useWrappedStore(pageProps);

  return (
    <Provider store={store}>
      <FontLoader />
      <div className={montserrat.className}>
        <APIErrorProvider>
          <APIErrorNotification></APIErrorNotification>
          <AnimatePresence exitBeforeEnter>
            <Theme>
              <Component {...props} key={router.pathname} />
            </Theme>
          </AnimatePresence>

          <Script
            id="hs-script-loader"
            src="//js-eu1.hs-scripts.com/145120089.js"
            strategy="afterInteractive"
          />
        </APIErrorProvider>
      </div>
    </Provider>
  );
}

export default App;