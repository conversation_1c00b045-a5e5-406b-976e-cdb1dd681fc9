import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  getLocalStorageItem,
  removeLocalStorageItem,
} from 'util/localStorageManagement';

import { Layout, Section } from 'components';
import * as MESSAGES from 'util/settingsMessages';

const RegisterFinal = () => {
  const [subscriptionStatus, setSubscriptionStatus] = useState({
    type: '',
    msg: '',
  });
  const [directAccessError, setDirectAccessError] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const successfulRegister = getLocalStorageItem('isRegisterSuccessful');
    if (successfulRegister) {
      setSubscriptionStatus({
        type: 'success',
        msg: MESSAGES.SubscriptionStartSuccess,
      });
      removeLocalStorageItem('registerData');
    } else {
      setSubscriptionStatus({
        type: 'danger',
        msg: `Direct access to page NOT allowed.`,
      });
      removeLocalStorageItem('registerData');
      // router.push('/');
      setDirectAccessError(true);
    }
  }, []);

  // if (directAccessError) {
  //   return (
  //     <div className={`alert alert-${subscriptionStatus.type} text-center p-3`}>
  //       {subscriptionStatus.msg}
  //     </div>
  //   );
  // }

  return (
    <>
      <Head>
        <title>captain coupon:: register-final</title>
      </Head>
      <Layout register customContainerFluidClass="container-footer-bottom">
        <Section background="#fff" className={'pad-t-3'}>
          <div className="col-sm-12 text-center">
            <h1 className="main-header text-md-center text-lg-center text-left">
              Coupons volle Kraft voraus!
            </h1>
            <p className="text-n pad-tb-3">
              Du hast es fast geschafft! Bitte öffne deine E-Mails und bestätige
              deinen Account, damit Du keine Deals deiner Lieblingsmarken mehr
              verpasst!
            </p>

            <div
              className={`alert alert-${subscriptionStatus.type} text-center p-3`}
            >
              {subscriptionStatus.msg}
            </div>
          </div>
        </Section>
      </Layout>
    </>
  );
};

export default RegisterFinal;
