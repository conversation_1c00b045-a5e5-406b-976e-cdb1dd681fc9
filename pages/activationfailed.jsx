import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

import { Section, Layout } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { resendConfirmationCodeUserId } from 'services/auth.service';

const activationFailed = () => {
  const router = useRouter();
  const userId = router.query.userId;
  const { addMessage } = useAPIError();

  return (
    <>
      <Head>
        <title>captain coupon::Activation Failed</title>
      </Head>
      <Layout
        home
        customContainerFluidClass="container-footer-bottom gray-background"
      >
        <Section background="#ebebeb" customPadding={true}>
          <div className="col-sm-12 layout-custom-header">
            <div className="header-image-wrap">
              <img
                src="/headers/Captaincoupon_Header_Quit-Newsletter.jpg"
                alt="CaptainCoupon-Markenbotschafter mit kritischem Blick, verschr<PERSON><PERSON><PERSON> Armen und Papagei auf der Schulter"
              />
            </div>
          </div>
        </Section>
        <Section
          className="pad-b-8 pad-t-4"
          background="#ebebeb"
          customPadding={true}
        >
          <div className="col-sm-12">
            <div className="row">
              <h1 className="col-12 main-header text-left">
                Link über Bord. Dieser Link funktioniert leider nicht mehr.
              </h1>
            </div>
          </div>
          <div className="col-sm-12">
            <div className="pad-t-4">
              <h1>
                Leider ist der dir zu gesendete Link bereits älter als 24
                Stunden und demnach nicht mehr gültig. Drücke unten auf “Neuen
                Link schicken” um erneut eine Bestätigungs-Mail zu erhalten und
                deine Anmeldung zu verifizieren.
              </h1>
            </div>
          </div>
          <div className="col-sm-12">
            <button
              type="button"
              className="btn btn-dark activation-failed-btn"
              onClick={() => {
                resendConfirmationCodeUserId(userId)
                  .then((response) => {
                    if (response.status === 200) {
                      addMessage('E-Mail erneut gesendet', 'success');
                      setTimeout(() => router.push('/'), 1000);
                    } else {
                      addMessage(response.data, 'error');
                    }
                  })
                  .catch((error) => addMessage(error.message, 'error'));
              }}
            >
              Neuen Link schicken
            </button>
          </div>
        </Section>
      </Layout>
    </>
  );
};

export default activationFailed;
