// import { Error as Err } from 'next/error';
import ErrorPage from 'next/error';

const urlToLowerCase = (url = '') => {
  const [path, ...query] = url.split('?');
  return `${path.toLocaleLowerCase()}${
    query.length ? ['', ...query].join('?') : ''
  }`;
};

const Error = ({ statusCode }) => {
  return <ErrorPage statusCode={statusCode} />;
};

Error.getInitialProps = async ({ req, res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;

  if (req.url !== urlToLowerCase(req.url) && statusCode === 404) {
    res.writeHead(302, {
      Location: urlToLowerCase(req.url),
    });
    res.end();
    return;
  } else {
    return { statusCode, reqUrl: req.url };
  }
};

export default Error;
