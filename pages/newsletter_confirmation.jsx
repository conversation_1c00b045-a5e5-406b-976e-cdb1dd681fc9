import Head from 'next/head';

import { Layout, Section } from '../components';

export default () => {
  return (
    <>
      <Head>
        <title>Newsletter confirmation</title>
      </Head>
      <Layout home>
        <Section background="#fff">
          <div className="col-sm-12 mar-t-5 layout-custom-header">
            <div className="header-image-wrap">
              <img
                src="/headers/Captaincoupon_Header_Newsletter-Confirmation.jpg"
                alt="CaptainCoupon-Markenbotschafter Gerhard Lim<PERSON> hat einen Papagei auf der Schulter, lacht und zeigt zwei ausgestreckte Daumen"
              />
            </div>
          </div>
        </Section>

        <Section
          background="#fff"
          className={'pad-t-4 pad-md-t-5 pad-lg-b-5'}
          customPadding={true}
        >
          <div className="col-sm-12 text-center">
            <h1 className="main-header text-left">
              Willkommen an Bord! Deine Newsletter-Anmeldung war erfolgreich.
            </h1>
          </div>
        </Section>

        <Section
          background="#fff"
          className={'pad-t-4 pad-md-t-5 pad-lg-b-5'}
          customPadding={true}
        >
          <div className="col-sm-12 pad-tb-1">
            <h2>
              Herzlichen Glückwunsch - Du verpasst ab jetzt keinen Gutscheincode
              mehr und behältst immer den Überblick über die neusten Rabatte,
              Angebote und Gutscheincode-Specials von CaptainCoupon.
            </h2>
          </div>
        </Section>
      </Layout>
    </>
  );
};
