import cookie from 'cookie';

export default (request, response) => {
  const cookieName = request.body.cookieName;
  const cookieValue = request.body.cookieValue || '';
  const cookieExpires = request.body.cookieExpires || 0;

  response.setHeader(
    'Set-Cookie',
    cookie.serialize(cookieName, cookieValue, {
      httpOnly: true,
      secure: process.env.NODE_ENV !== 'development',
      sameSite: 'strict',
      expires: new Date(cookieExpires),
      path: '/',
    })
  );
  response.statusCode = 200;
  response.json({ success: true });
};
