import Head from 'next/head';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Layout, Section } from '../components';
import NewsletterSection from '../sections/NewsletterSection';
import Collapsable from '../components/Collapsable/Collapsable';
import * as CommonService from 'services/common.service';

export async function getServerSideProps(context) {
  try {
    const headerImage = await CommonService.getPageHeaderImg('FAQ');
    return {
      props: {
        headerImage,
      },
    };
  } catch (err) {
    return {
      props: {
        headerImage: '/headers/CaptainCoupon_Header_FAQ_new.jpg',
      },
    };
  }
}

const Faq = ({ headerImage }) => {
  const { useCustomTheme } = useSelector((state) => state.settings);

  if (useCustomTheme) {
    return <Error statusCode={404} title={`Nicht gefunden!`} />;
  }

  const [faqItems, setFaqItems] = useState([
    {
      title: 'Was ist CaptainCoupon?',
      content: `Captain<PERSON>oupon ist dein Gutscheincode-Portal mit der 100%-Gutscheincode-Gültigkeits-Garantie, dass dir dabei hilft, die besten Rabatte, Gutscheincodes und Aktionen der beliebtesten Lieblingsbrands und Onlineshops schnell und einfach zu finden. Viele unserer Gutscheine haben wir exklusiv ausgehandelt und diese sind daher ausschleißlich bei captaincoupon.de zu finden. Das beste jedoch: Wir haben alle Gutscheincodes von Hand geprüft. Du kannst dir also immer sicher sein, dass alle Gutscheine gültig sind und somit funktionieren. Das alles dank unserer 100%-Gutscheincode-Gültigkeits-Garantie.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Wie kann ich mir ein interessantes Angebot merken?',
      content: `Wenn du Rabatte gefunden hast, die du gerne zu einem späteren Zeitpunkt im Onlineshop oder in der Filiale einlösen möchtest, 
      kannst du ganz einfach unsere Merkfunktion nutzen. Wichtig ist dabei, dass du dich bereits bei CaptainCoupon registriert und eingeloggt 
      hast. Um dir ein Angebot zu merken, klicke auf das Sternsymbol, das sich auf den Coupons bzw. auf der Gutscheinseite befindet. 
      In deinem Account findest du eine Liste mit allen Angeboten und Brands, die du bereits deiner Liste hinzugefügt hast.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Wie löse ich einen Gutscheincode ein?',
      content: `Sobald du den passenden Gutschein gefunden hast, klickst du auf „Gutschein einlösen“, um den Code anzuzeigen. 
        Kopiere nun den angezeigten Code. Wenn du auf den Link zum jeweiligen Online Shop klickst, wirst du direkt dorthin weitergeleitet. 
        Den Gutscheincode kannst du im Warenkorb des Onlineshops eingeben und einlösen.
        Bei Filialgutscheinen oder Coupons zum Ausdrucken zeigst du den Gutschein beim Bezahlen an der Kasse vor. 
        Der Rabatt wird dann sofort abgezogen.`,
      subitems: null,
      show: false,
    },
    {
      title:
        'Muss ich mich als Nutzer anmelden / registrieren, um Gutscheincodes von CaptainCoupon erhalten zu können?',
      content: `Ja, um den Gutscheincode deiner Wahl angezeigt zu bekommen, ist es notwendig, dass du dich bei CaptainCoupon registrierst und Mitglied wirst. Wenn Du bereits Mitglied bist, musst du dich nur in deinem Konto anmelden, um Zugriff auf unsere Gutscheincodes zu erhalten.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Kann ich die Online-Gutscheine auch in Ladengeschäften einlösen?',
      content: `Ja. Wenn es sich um einen Filialgutscheinen handelt, kannst du den ausgewählten Gutschein einfach als PDF speichern und
        ausdrucken. Wenn du den Gutschein beim Bezahlen an der Kasse zeigst, wird der Rabatt sofort abgezogen.`,
      subitems: null,
      show: false,
    },
    {
      title:
        'Wie werde ich über neue Gutscheine und Aktionen bei CaptainCoupon informiert?',
      content: `In unserem Newsletter informieren wir regelmäßig über die neuesten und besten Angebote, über Specials, beliebte Kategorien 
        sowie aktuelle Gewinnspiele. Für den Newsletter kannst du dich bei deiner Registrierung oder direkt auf der Startseite anmelden.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Warum kann ich meinen Gutschein nicht anwenden?',
      content: `Wenn bei der Einlösung deines Gutscheincodes im Onlineshop ein Fehler auftritt, kann dies verschiedene Gründe haben. Am besten prüfst du zunächst die folgenden Punkte, da häufig hier die Fehlerquelle liegt.`,
      subitems: [
        {
          title: 'Hast du den Gutschein schon einmal verwendet?',
          content:
            'Als Erstes solltest du testen, ob du den Gutschein nicht schon einmal verwendet hast. Kontrolliere hierfür deine E-Mails, ob du den gleichen Code bereits benutzt hast.',
        },
        {
          title:
            'Sind in deinem Einkaufswagen alle Gutscheinbedingungen erfüllt?',
          content:
            'Bei den Gutscheinen ist es wichtig, dass du alle nötigen Gutscheinbedingungen erfüllst. Besonders attraktive Gutscheine beinhalten meist einen Mindestbestellwert, den du erreichen musst, um die Vorteile zu erhalten. Außerdem gelten die Rabatte zum Teil ausschließlich für bestimmte Produktgruppen. Kontrolliere daher am besten noch einmal deinen Warenkorbinhalt.',
        },
        {
          title: 'Hast du den Gutscheincode fehlerfrei eingegeben?',
          content:
            'Wenn der Gutscheincode nicht akzeptiert wird, hängt dies auch oft mit einer fehlerhaften Eingabe zusammen. Auch wenn du den Gutschein kopiert hast, kann es sein, dass ein Zeichen abgeschnitten wurde oder sich weitere Inhalte in die Zwischenablage eingeschlichen haben. Kopiere den Gutscheincode einfach noch einmal oder gib den Code manuell ein.',
        },
      ],
      show: false,
    },
    {
      title: 'Hast du den Gutschein schon einmal verwendet?',
      content: `Als Erstes solltest du testen, ob du den Gutschein nicht schon einmal verwendet hast. Kontrolliere hierfür deine 
        E-Mails, ob du den gleichen Code bereits benutzt hast.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Sind in deinem Einkaufswagen alle Gutscheinbedingungen erfüllt?',
      content: `Bei den Gutscheinen ist es wichtig, dass du alle nötigen Gutscheinbedingungen erfüllst. Besonders attraktive Gutscheine beinhalten 
        meist einen Mindestbestellwert, den du erreichen musst, um die Vorteile zu erhalten. Außerdem gelten die Rabatte zum Teil ausschließlich 
        für bestimmte Produktgruppen. Kontrolliere daher am besten noch einmal deinen Warenkorbinhalt.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Hast du den Gutscheincode fehlerfrei eingegeben? ',
      content: `Wenn der Gutscheincode nicht akzeptiert wird, hängt dies auch oft mit einer fehlerhaften Eingabe zusammen. Auch wenn du den Gutschein 
        kopiert hast, kann es sein, dass ein Zeichen abgeschnitten wurde oder sich weitere Inhalte in die Zwischenablage eingeschlichen haben. 
        Kopiere den Gutscheincode einfach noch einmal oder gib den Code manuell ein.`,
      subitems: null,
      show: false,
    },
    {
      title:
        'Muss ich etwas bezahlen, um die Gutscheine von CaptainCoupon nutzen zu können?',
      content: `Ja, CaptainCoupon ist ein zahlungspflichtiges Gutscheincode-Portal mit einer 100% Gutscheincode-Gültigkeitsgarantie. Mit einem Mitgliedsbeitrag von nur 1 EUR monatlich erhältst Du Zugriff auf alle Gutscheincodes von CaptainCoupon und musst nie wieder zum Normalpreis einkaufen.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Welche Vorteile bringt mir die Newsletter-Anmeldung?',
      content: `Unser Newsletter informiert dich wöchentlich über die besten Angebote und Aktionen sowie aktuelle Gewinnspiele. 
        Mit dem Newsletter Abonnement stellst du sicher, dass du die neusten Angebote deiner Lieblingsmarken nicht mehr verpasst.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Wie oft bekomme ich einen Newsletter zugeschickt?',
      content: `Unseren Newsletter erhältst du höchstens einmal pro Woche. Wenn wir einen Newsletter-Versand durchführen, dann immer Freitagnachmittags zwischen 15:00 und 17:00 Uhr. Nachdem wir einen Newsletter versendet haben ist er in der Regel innerhalb weniger Minuten in deinem Postfach.`,
      subitems: null,
      show: false,
    },
    {
      title: 'Wie kann ich den Newsletter wieder abbestellen?',
      content: `Um unseren Newsletter wieder abzubestellen, klicke einfach auf den Link "hier vom Newsletter abmelden", 
        welchen du in jeder Ausgabe unseres Newsletters am Ende des Newsletters findest.`,
      subitems: null,
      show: false,
    },
    {
      title:
        'Haben Gutscheincodes von CaptainCoupon Gutscheinbedingungen. Wenn ja, was sollte ich beachten?',
      content: `Jeder Gutscheincode muss gesetzliche Anforderungen erfüllen, diese werden bei uns in den Gutscheinbedingungen geregelt. Die Gutscheinbedingungen unterscheiden sich je nach Marke und Art des Angebotes. Die individuellen Gutscheinbedingungen findest du deswegen immer auf der BrandPage jeder Marke bzw. auf den einzelnen Coupons. Zu beachten ist das Gültigkeitsdatum des Angebotes, denn meistens sind Gutscheincodes zeitlich befristet. Außerdem solltest du überprüfen, ob es einen Mindestbestellwert gibt und ob der Rabatt und Gutscheincode ausschließlich für bestimmte Produktgruppen oder andere individuelle Spezifikationen hat, wie z.B. das er nur für Neukunden gültig ist, oder nur für eine bestimmte Produktkategorie gilt.`,
      subitems: null,
      show: false,
    },
  ]);

  const toggle = (index) => {
    faqItems[index].show = !faqItems[index].show;
    setFaqItems([...faqItems]);
  };

  useEffect(() => {}, [faqItems]);
  return (
    <>
      <Head>
        <title>Häufig gestellte Fragen</title>
      </Head>
      <Layout home>
        <Section background="#fff">
          <div className="col-sm-12 mar-t-5 layout-custom-header">
            <div className="header-image-wrap">
              <img
                src={headerImage}
                alt="CaptainCoupon-Markenbotschafter Gerhard Limone tippt konzentriert auf einem Smartphone"
                loading="lazy"
              />
            </div>
          </div>
        </Section>
        <Section
          background="#fff"
          className={'pad-md-t-5 pad-t-4'}
          customPadding={true}
        >
          <div className="col-sm-12">
            <h1 className="main-header text-left">Häufig gestellte Fragen</h1>
            <section className="pad-t-1 pad-md-t-4 pad-b-9">
              {faqItems.map((item, index) => {
                return (
                  <Collapsable
                    title={item.title}
                    isOpen={faqItems[index].show}
                    toggle={() => toggle(index)}
                    key={`card_index_${index}`}
                  >
                    {item}
                  </Collapsable>
                );
              })}
            </section>
          </div>
        </Section>

        {/*	newsletter block  */}
        <NewsletterSection></NewsletterSection>
      </Layout>
    </>
  );
};
export default Faq;
