import { useLayoutEffect } from 'react';
import Head from 'next/head';

import { Layout, Section } from '../components';
import { logoutUser } from 'services/auth.service';

export default () => {
  useLayoutEffect(() => {
    logoutUser();
  }, []);

  return (
    <>
      <Head>
        <title>Logout</title>
      </Head>
      <Layout home customContainerFluidClass="container-footer-bottom">
        <Section background="#fff">
          <div className="col-sm-12 mar-t-5">
            <img
              src="/headers/CaptainCoupon-imprint.png"
              alt="Vier nebeneinander schwebende rosa Luftballons an Schnüren, von denen der zweite die Form eines Sparschweins hat"
            />
          </div>
        </Section>

        <Section background="#fff" className={'pad-t-5'}>
          <div className="col-sm-12 text-center">
            <h1 className="main-header text-lg-center text-md-left text-left">
              Abgemeldet
            </h1>
          </div>
        </Section>

        <Section background="#fff" className={'pad-t-4 pad-b-7'}></Section>
      </Layout>
    </>
  );
};
