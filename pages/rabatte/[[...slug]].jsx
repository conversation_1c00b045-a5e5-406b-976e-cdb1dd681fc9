import Head from 'next/head';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import * as CategoryService from 'services/categories.service';
import * as CouponService from 'services/coupons.service';
import { toggleCouponFavorite } from 'util/helpers';
import {
  Card,
  Layout,
  Like,
  LimitOverlay,
  ModalSearchBlock,
  Pagination,
  SearchBlock,
  Section,
  SelectorCarousel,
} from '../../components';
import useAPIError from '../../components/APIErrorNotification/useAPIError';
import { displayCouponCode } from '../../components/Card/Card';
import { getOverlayMessage } from '../../components/LimitOverlay/LimitOverlay';
import NewsletterSection from '../../sections/NewsletterSection';
import { debounce, getCouponAltText } from '../../util/helpers';

function isNumber(str) {
  if (typeof str != "string") return false; // we only process strings!  
  return !isNaN(str) && !isNaN(parseFloat(str));
}

export async function getServerSideProps(context) {
  const { slug } = context.params;
  if (slug?.length && slug[0] === 'kategorien') {
    context.res.writeHead(302, { Location: '/kategorien' });
    context.res.end();
  } else {
    let categories;
    let categoriesError = null;

    try {
      categories = await CategoryService.getCategories({
        pageSize: 120,
      });
    } catch (err) {
      categoriesError = err;
    }

    return {
      props: {
        categories,
        categoriesError,
      },
    };
  }
}

const Rabatte = ({ categories, categoriesError }) => {
  const router = useRouter();
  let { slug } = router.query;

  const executeScroll = () => window.scrollTo(0, myRef.current.offsetTop);
  const myRef = useRef(null);

  const [loading, setLoading] = useState(false);
  const [activeCategory, setActiveCategory] = useState();
  const [activeCategoryName, setActiveCategoryName] = useState(
    () => 'Unsere Rabatte'
  );

  const rowsPerPage = 20;
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState();

  const [categoryFilters, setCategoryFilters] = useState([]);

  const [coupons, setCoupons] = useState();

  const { addMessage } = useAPIError();

  const [userQuery, setUserQuery] = useState('');
  const [debouncedUserQuery, setDebouncedUserQuery] = useState(userQuery);

  const applyUserQueryDebounced = useCallback(
    debounce((searchText) => setDebouncedUserQuery(searchText), 1000),
    [setDebouncedUserQuery]
  );

  useEffect(
    () => applyUserQueryDebounced(userQuery),
    [userQuery, applyUserQueryDebounced]
  );

  const getCoupons = async (options) => {
    const { data, total } = await CouponService.getCoupons(options);
    setCoupons(data);
    setTotal(total);
  };

  const handleCategoryFilter = (data) => {
    const categoryId = parseInt(data.id);
    const hasSlug = data.slug && (data.slug.length > 0);
    if (categoryId && categoryId !== -1) {
      setCategoryFilters([
        {
          field: hasSlug ? 'category.slug' : 'category.id',
          operator: 'EQUAL',
          value: hasSlug ? data.slug : categoryId,
        },
      ]);
      setActiveCategory(categoryId);
      setCurrentPage(1);
      applyCurrentCategoryState(categories, categoryId);
    } else {
      setActiveCategory(categoryId);
      setCategoryFilters([]);
      setActiveCategoryName('Unsere Rabatte');
    }
  };

  const onCategorySelect = useCallback(
    async (item) => {
      setUserQuery('');
      const target = item.id !== -1 ? `/rabatte/${item.slug || item.id}` : '/rabatte';
      await router.push(target);
      handleCategoryFilter(item);
    },
    [handleCategoryFilter]
  );

  const applyCurrentCategoryState = (categories, id) => {
    if (!categories) return false;
    categories
      .filter((item) => item.id === +id)
      .map((item) => {
        setActiveCategoryName(item.name);
      });
  };

  useEffect(() => {
    let categoriesFilter = [];

    const hasCategoryIdInUrl = slug?.length;
    const hasSlug = !isNumber(slug?.[0]);

    let categoryId;
    if(hasSlug) {
      categoryId = categories.find((category) => category.slug === slug?.[0])?.id;
    }

    const hasSearchQuery = debouncedUserQuery?.length;
    if (hasCategoryIdInUrl && !hasSearchQuery) {
      categoriesFilter = [
        {
          field: hasSlug ? 'category.slug' : 'category.id',
          operator: 'EQUAL',
          value: slug[0],
        },
      ];
      setActiveCategory(categoryId || slug[0]);
      applyCurrentCategoryState(categories, categoryId || slug[0]);
    } else {
      setActiveCategory(-1);
      setActiveCategoryName('Unsere Rabatte');
    }

    const reachBoost = router.query?.boost === '';

    getCoupons({
      pageSize: rowsPerPage,
      page: currentPage - 1,
      sort: [{ dir: 'DESC', field: 'timeAdded' }],
      filters: [
        ...categoriesFilter, 
        {
          field: 'status',
          operator: 'EQUAL',
          value: 1,
        }
      ],
      search: debouncedUserQuery,
      boost: reachBoost,
    });

    return () => {};
  }, [
    total,
    currentPage,
    categoryFilters,
    activeCategory,
    debouncedUserQuery,
    slug,
  ]);

  const categorySelectorValues = useMemo(
    () => [
      { id: -1, label: 'Alle', href: '/rabatte' },
      ...categories.map((category) => ({
        id: category.id,
        label: category.name,
        href: `/rabatte/${category.slug || category.id}`,
        slug: category.slug
      })),
    ],
    [categories]
  );

  console.log(coupons)

  return (
    <>
      <Head>
        <title>CaptainCoupon Gutscheincodes</title>
      </Head>
      <Layout home>
        <Section className="pad-b-8 pad-sm-t-5 pad-t-2" customPadding={true}>
          <div className="col-sm-12">
            <div className="row align-items-center">
              <h1 className="col-9 main-header" ref={myRef}>
                {activeCategoryName}
              </h1>
              <div className="col-3">
                <div className="search-block compact">
                  <SearchBlock
                    className="shorter d-none d-lg-flex"
                    searchText={userQuery}
                    handleChange={setUserQuery}
                  ></SearchBlock>
                  <ModalSearchBlock
                    className="shorter d-block d-lg-none"
                    searchText={userQuery}
                    handleChange={setUserQuery}
                  ></ModalSearchBlock>
                </div>
              </div>
              <div className="col-12">
                <SelectorCarousel
                  className="mt-2 mb-2"
                  options={categorySelectorValues}
                  selectedId={activeCategory}
                  onSelect={onCategorySelect}
                />
              </div>
            </div>
          </div>

          <div className="col-sm-12">
            {loading && <p>loading....</p>}
            {coupons?.length === 0 ? (
              <div className="not-found">
                <h1>
                  Leider haben wir für deine Suche keine Ergebnisse gefunden.
                </h1>
              </div>
            ) : (
              <>
                <div className="row">
                  {coupons?.map((item, index) => {
                    return (
                      <Card className="col-6 col-lg-3 white-card" cardClassName="border-grey" key={item.id}>
                        <Card.Image
                          url={`/brand/${item.brandSlug}`}
                          src={item.compressedImage || item.image}
                          alt={getCouponAltText(item)}
                        >
                          <Like
                            isLiked={item.isFavourite}
                            id={item.id}
                            onSuccess={() =>
                              setCoupons((coupons) =>
                                toggleCouponFavorite(coupons, item.id)
                              )
                            }
                            onError={(status, data) => {
                              addMessage(data, 'error');
                            }}
                          />
                          <LimitOverlay message={getOverlayMessage(item)} className="border-rounded-top-14" />
                        </Card.Image>
                        <Card.Discount
                          type={item.discountType}
                          shortDescription={item.shortDescription}
                        >
                          {item.discountValue}
                        </Card.Discount>
                        <Card.ClientLogo src={item.compressedBrandLogo || item?.brandLogo} alt={`Logo der Marke ${item.brandSlug} gutschein`} />
                        <Card.Description>
                          Gültig bis:{' '}
                          <b>
                            {Intl.DateTimeFormat('de-DE', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                            }).format(new Date(item.validationDate))}
                          </b>
                          <br />
                        </Card.Description>
                        <Card.CouponButton
                          coupon={item}
                          getCouponCode={() =>
                            displayCouponCode(item.brandSlug)
                          }
                        >
                          <p>
                            {item.code ? 'Code kopieren' : 'Zum Gutscheincode'}
                          </p>
                        </Card.CouponButton>
                      </Card>
                    );
                  })}
                </div>
                <Pagination
                  count={total || 0}
                  rowsPerPage={rowsPerPage}
                  currentPage={currentPage}
                  onChangePage={(number) => {
                    setCurrentPage(number);
                    executeScroll();
                  }}
                ></Pagination>
              </>
            )}
          </div>
        </Section>

        <NewsletterSection headerText="Erhalte&nbsp;die besten Angebote direkt per Mail!" />
      </Layout>
    </>
  );
};

export default Rabatte;
