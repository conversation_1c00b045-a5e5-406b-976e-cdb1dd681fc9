import * as BrandService from 'services/brands.service';
import * as CategoryService from 'services/categories.service';

const STATIC_PAGE_PATHS = [
  '',
  'marken',
  'rabatte',
  'kategorien',
  'register',
  'agb',
  'datenschutz',
  'faq',
  'impressum',
  'so_funktioniert',
];

const Sitemap = () => {};

const fetchBrandUrls = async (webPageBaseUrl) => {
  const brands = await BrandService.getBrands({
    pageSize: 10000,
  });
  return brands.map((item) => `${webPageBaseUrl}/brand/${item.slug}`);
};

const fetchCategoryUrls = async (webPageBaseUrl) => {
  const categories = await CategoryService.getCategories();
  return categories.map((item) => `${webPageBaseUrl}/rabatte/${item.slug || item.id}`);
};

const buildUrlXmlElementsString = (items) => {
  return items
    .map(
      (url) => `
        <url>
            <loc>${url}</loc>
        </url>
    `
    )
    .join('');
};

export const getServerSideProps = async ({ res }) => {
  const webPageBaseUrl = {
    development: 'http://localhost:3000',
    production: 'https://captaincoupon.de',
  }[process.env.NODE_ENV];

  const staticPageUrls = STATIC_PAGE_PATHS.map(
    (staticPagePath) => `${webPageBaseUrl}/${staticPagePath}`
  );

  const brandUrls = await fetchBrandUrls(webPageBaseUrl);
  const categoryUrls = await fetchCategoryUrls(webPageBaseUrl);

  const urlXmlElementsString = buildUrlXmlElementsString([
    ...staticPageUrls,
    ...brandUrls,
    ...categoryUrls,
  ]);

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${urlXmlElementsString}
    </urlset>
  `;

  res.setHeader('Content-Type', 'text/xml');
  res.write(sitemap);
  res.end();

  return {
    props: {},
  };
};

export default Sitemap;
