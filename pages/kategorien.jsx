import Head from 'next/head';
import * as BrandService from 'services/brands.service';
import * as nodeHelpers from 'util/nodeHelpers';
import { Layout } from '../components';
import BrandsCarouselSection from '../sections/BrandsCarouselSection';
import CategoriesSection from '../sections/CategoriesSection';
import NewsletterSection from '../sections/NewsletterSection';

export async function getServerSideProps(context) {
  let customHeaders = null;
  try {
    customHeaders = await nodeHelpers.getCustomHeadersFromReq(
      context.req,
      context.res
    );
  } catch (error) {
    nodeHelpers.logout(context.res);
    return {};
  }

  const allBrands = await BrandService.getBrands({
    page: 0,
    pageSize: 24,
    sort: [
      {
        field: 'id',
        dir: 'ASC',
      },
    ],
    search: '',
  });

  return {
    props: {
      allBrands,
    },
  };
}

export default function Kategorien({ allBrands }) {
  return (
    <>
      <Head>
        <title>Captain<PERSON><PERSON><PERSON><PERSON></title>
      </Head>
      <Layout home>
        <CategoriesSection
          all="all"
          sectionBg="#f5f5f5"
          className="categories-section-kategorien pad-sm-t-5 pad-t-2"
        />
        {allBrands && allBrands.length > 0 && (
          <BrandsCarouselSection
            brands={allBrands}
            className="pad-lg-tb-10 pad-tb-5"
          />
        )}
        <NewsletterSection />
      </Layout>
    </>
  );
}
