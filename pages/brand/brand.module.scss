@import '../../styles/sass/variables';

.bannerLinkWrapper {
  @media screen and (max-width: 480px) {
    padding-bottom: 2rem;
  }

  a {
    height: 27px;
    font-size: 16px;
  }
}

.noCoupons {
  @media (min-width: 576px) {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.brandLogoWrapper { 
  border-radius: 0px 15px 15px 0px;
  border: 1px solid #AFAFAF;
  border-left: none;
}

.descriptionVideo {
  min-height: 550px;
  height: max-content;
  padding-top: 20px;
  padding-bottom: 20px;
}

@media screen and (max-width: 991px) {
  .brandLogoWrapper {
    border-radius: 15px;
    border: 1px solid #AFAFAF !important;
  }

  .descriptionVideo {
    min-height: auto;
  }
}

@media screen and (max-width: 991px) {
  .headerImageWrap {
    display: none;
  }

  .brandHeaderImagesWrap {
    margin-bottom: 35px;
  }
}

.brandHeaderImagesWrap {
  padding-left: 15px;
  padding-right: 15px;
  margin-top: 50px;
  margin-bottom: 50px;

  @media screen and (max-width: $handheld-breakpoint) {
    margin-top: 28px;
    margin-bottom: 16px;
    padding-left: 5px;
    padding-right: 5px;
  }
}


.headerImageWrap {
  position: relative;
  width: 100%;
  height: 340px;
  overflow: hidden;
  border-radius: 15px 0px 0px 15px;
  border: 1px solid #AFAFAF;
  border-right: none;

  @media (max-width: 479px) {
    height: 150px;
  }

  @media (max-width: 575.98px) {
    margin-top: 0px;
  }

  @media screen and (max-width: $handheld-breakpoint) {
    display: none;
  }

  :global(img) {
    min-height: 150px;
    object-fit: cover;
    @media (min-width: 480px) {
      position: absolute;
      min-width: 100%;
      height: auto;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      min-height: 100%;
      max-width: none;
    }
  }
}