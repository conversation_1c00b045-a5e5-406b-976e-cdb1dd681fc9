.banner-link-wrapper {

  @media screen and (max-width: 480px) {
    padding-bottom: 2rem;
  }

  a {
    height: 27px;
    font-size: 16px;
  }
}

.no-coupons {
  @media (min-width: 576px) {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.brand-logo-wrapper { 
  border-radius: 0px 15px 15px 0px;
  border: 1px solid #AFAFAF;
  border-left: none;
}

.description-video {
  min-height: 550px;
  height: max-content;
  padding-top: 20px;
  padding-bottom: 20px;
}

@media screen and (max-width: 991px) {
  .brand-logo-wrapper {
    border-radius: 15px;
    border: 1px solid #AFAFAF !important;
  }

  .description-video {
    min-height: auto;
  }
}

@media screen and (max-width: 991px) {

  .brand-header-images-wrap {
    margin-bottom: 35px;
  }
}
