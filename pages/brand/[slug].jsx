import {
  Card,
  Centered,
  CouponCardHorizontal,
  Layout,
  Like,
  PlayVideoModal,
  Section,
} from 'components';
import NewsletterSection from 'sections/NewsletterSection';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { displayCouponCode } from 'components/Card/Card';
import LimitOverlay, {
  getOverlayMessage,
} from 'components/LimitOverlay/LimitOverlay';
import VideoBlock, {
  getVideoType,
  isVideoTypeAccepted,
  toValidEmbedVideoUrl,
} from 'components/VideoBlock/VideoBlock';
import Error from 'next/error';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { AiFillCloseCircle } from 'react-icons/ai/index';
import { FaRegPlayCircle } from 'react-icons/fa/';
import { useSelector } from 'react-redux';
import VizSensor from 'react-visibility-sensor';
import { Modal } from 'reactstrap';
import * as BrandService from 'services/brands.service';
import * as CouponService from 'services/coupons.service';
import { toggleCouponFavorite } from 'util/helpers';
import * as nodeHelpers from 'util/nodeHelpers';
import RedemptionInstructions from './RedemptionInstructions';
import styles from './brand.module.scss';
import { getCouponAltText } from '../../util/helpers';

export async function getServerSideProps(context) {
  let customHeaders = null;
  try {
    customHeaders = await nodeHelpers.getCustomHeadersFromReq(
      context.req,
      context.res
    );
  } catch (error) {
    nodeHelpers.logout(context.res);
    return {};
  }

  try {
    const brandResponse = await BrandService.getBrandBySlug(
      context.query.slug,
      context.query.boost,
      customHeaders
    );
    return { props: { brand: brandResponse.data } };
  } catch (error) {
    // Check if the response object is available
    if (context.res) {
      // Set HTTP status code to 301 for redirection
      context.res.writeHead(301, { Location: '/404' });
      context.res.end();
    }

    // Return empty props as the response will redirect
    return { props: {} };
  }
}

function removeExtraDoubleSpaces(str) {
  // Use a regular expression to replace multiple spaces with a single space
  return str.replace(/\s{2,}/g, ' ');
}

const Brand = ({ brand }) => {

  const [modal, setModal] = useState(false);
  const [policiesAcceptenceModal, setPoliciesAcceptenceModal] = useState(false);
  const [allreadyLoaded, setAllReadyLoaded] = useState(false);
  const [coupons, setCoupons] = useState([]);
  const [relatedCoupons, setRelatedCoupons] = useState(false);

  const { useCustomTheme } = useSelector((state) => state.settings);

  const section3Video = useRef(null);
  const rowsPerPage = 20;
  const router = useRouter();
  let { slug } = router.query;
  const { addMessage } = useAPIError();

  const reachBoost = router.query?.boost === '';

  const getCoupons = async (slug) => {
    const response = await CouponService.getCoupons({
      pageSize: rowsPerPage,
      boost: reachBoost,
      filters: [
        {
          field: 'brand',
          operator: 'EQUAL',
          value: +slug,
        },
      ],
    });
    setCoupons(response.data);
  };

  const getRelatedCoupons = async (brandId) => {
    setRelatedCoupons(
      await CouponService.getRelatedCoupons({ brandId, reachBoost })
    );
  };

  useEffect(() => {
    if (brand && brand.id) {
      getCoupons(brand.id);
      getRelatedCoupons(brand.id);
    }
    return () => {};
  }, [slug, brand]);

  if (!brand) {
    return (
      <Error
        statusCode={404}
        title={`Marke mit slug = [${slug}] nicht gefunden!`}
      />
    );
  } else if (brand?.public) {
    const {
      metaTitle,
      metaDescription,
      image,
      compressedImage,
      logo,
      compressedLogo,
      name,
      websiteLink,
      websiteLinkTitle,
      s1Media,
      s1IsHidden,
      s1Title,
      s1Description,
      s1Link,
      s2IsHidden,
      s2BrandHeadline,
      s2BrandDescription,
      s3IsHidden,
      s3MediaLink,
      s1ShowLearnMoreButton,
      isBrandInstructionUpdated,
      step1,
      step2,
      step3,
      deactivateBrandPageRecommendation,
      enableBanner,
      bannerText,
      bannerRedirectUrl,
      brandBannerBackgroundColor,
      brandBannerTextColor,
      disableNewsletter,
      hasFutureCoupon
    } = brand.public;
    const brandLogo = compressedLogo || logo;

    const brandCountdownEnabled = brand.public?.isCountDownEnabled;

    function createMarkup(value) {
      return {
        __html: value?.toString(),
      };
    }

    const toggleVideoModal = (e) => {
      e.stopPropagation();
      setModal(!modal);
      if (!isVideoTypeAccepted(s1Media)) {
        setPoliciesAcceptenceModal(true);
      }
    };

    const playVideoOnAccept = (isAccepted) => {
      setPoliciesAcceptenceModal(false);
      if (!isAccepted) {
        setModal(false);
      }
    };

    const hasLiveCoupon = coupons?.some((coupon) => coupon.status == "ACTIVE");

    return (
      <>
        <Head>
          <title>
            {removeExtraDoubleSpaces(metaTitle)}
          </title>
          <meta name="description" content={removeExtraDoubleSpaces(metaDescription)} />
        </Head>
        <Layout home>
          
          {enableBanner && (
            <div className={styles.bannerLinkWrapper}>
              <a
                className='w-100 d-flex justify-content-center align-items-center position-absolute'
                style={{
                  right: "0", 
                  textDecoration: "none",
                  color: brandBannerTextColor || "white",
                  backgroundColor: brandBannerBackgroundColor || "black",
                }}
                href={bannerRedirectUrl}
              >
                {bannerText}
              </a>
            </div>
          )}

          <div className={`container ${styles.brandHeaderImagesWrap}`}>
            <div className="row no-gutters flex-lg-row-reverse">
              <Centered className={`col-lg-4 col-sm-12 bg-white pad-lg-tb-1 pad-sm-tb-4 ${styles.brandLogoWrapper}`}>
                <img
                  id={`brandLogo-${brandLogo}`}
                  src={brandLogo}
                  className="brand-page-logo"
                  alt={
                    name
                      ? `Logo der Marke ${name} gutschein"`
                      : 'Logo der Marke auf dieser Seite'
                  }
                />
              </Centered>
              <div className={`col-lg-8 col-sm-12 ${styles.headerImageWrap}`}>
                <img
                  src={compressedImage || image}
                  alt={
                    name
                      ? `Produktfoto der Marke ${name} rabattcode"`
                      : 'Produktfoto der Marke auf dieser Seite rabattcode'
                  }
                />
              </div>
            </div>
          </div>

          <div className="container pad-lr-10">
            <div className="row">
              {coupons && (
                <div
                  className={`${
                    useCustomTheme ? 'col-lg-12' : 'col-lg-8'
                  } col-12`}
                  id="coupons"
                >
                  {!hasLiveCoupon && (
                    <NewsletterSection
                      headerText="COMING SOON"
                      brandInfo={brand}
                      brandPageFullWidth={false}
                    />
                  )}

                  {!!coupons.length &&
                    coupons.map((coupon) => {
                      return (
                        <CouponCardHorizontal 
                          key={coupon.id} 
                          coupon={coupon}
                          brandCountDownEnabled={brandCountdownEnabled && coupon.isCountDownEnabled} 
                        />
                      );
                    })}

                </div>
              )}

              {!useCustomTheme && (
                <RedemptionInstructions
                  className="d-none d-lg-block"
                  slug={router.query.slug}
                  name={name}
                  isBrandInstructionUpdated={isBrandInstructionUpdated}
                  step1={step1}
                  step2={step2}
                  step3={step3}
                />
              )}

              <div className="col-12" id="link">
                <h1 className="headline-section">
                  Einzulösen auf:
                  <br />
                  <a
                    href={websiteLink}
                    target="_blank"
                    className="headline-link"
                  >
                    {websiteLinkTitle || websiteLink}
                  </a>
                </h1>
              </div>
            </div>
          </div>
          
          {hasLiveCoupon && !disableNewsletter && (
            <NewsletterSection
              headerText=""
              brandInfo={brand}
            />
          )}

          {!s1IsHidden && (
            <Section background="#F0F0F0" className="section1">
              <div
                className={`col-lg-6 col-sm-12 d-flex align-content-center justify-content-center justify-content-lg-start flex-wrap px-4 ${styles.descriptionVideo}`}
              >
                <h1 className="main-header text-left">{s1Title}</h1>
                <div
                  className="pad-tb-3"
                  style={{ wordBreak: 'break-word' }}
                  dangerouslySetInnerHTML={createMarkup(s1Description)}
                ></div>
                {s1ShowLearnMoreButton && (
                  <a className="btn btn-dark" href={s1Link} target="_blank">
                    Mehr erfahren
                  </a>
                )}
              </div>
              <div
                className="col-lg-6 col-sm-12 video-placeholder-container"
                onClick={toggleVideoModal}
              >
                <FaRegPlayCircle className="video-placeolder-play" />
                <div className="video-placeolder-overlay"></div>
                <iframe
                  src={`${toValidEmbedVideoUrl(s1Media)}`}
                  frameBorder="0"
                  width="1360px"
                  height="720px"
                  allow=" autoPlay; encrypted-media; gyroscope; picture-in-picture"
                ></iframe>
              </div>
              <Modal
                isOpen={modal}
                toggle={toggleVideoModal}
                className={`modal-90w ${modal && 'custom-video-size'} `}
              >
                <AiFillCloseCircle
                  onClick={toggleVideoModal}
                  data-play="play"
                  className="closeVideoModal"
                ></AiFillCloseCircle>
                <div className="embed-responsive embed-responsive-16by9">
                  <iframe
                    src={
                      isVideoTypeAccepted(toValidEmbedVideoUrl(s1Media))
                        ? `${toValidEmbedVideoUrl(s1Media)}?autoplay=1`
                        : toValidEmbedVideoUrl(s1Media)
                    }
                    frameBorder="0"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="embed-responsive-item"
                  ></iframe>
                </div>
              </Modal>
              {policiesAcceptenceModal && (
                <PlayVideoModal
                  videoType={getVideoType(s1Media)}
                  playVideoOnAccept={playVideoOnAccept}
                />
              )}
            </Section>
          )}

          {!s2IsHidden && (
            <Section background="#fff" className="pad-t-5">
              <div className="col-sm-12 text-center px-4">
                <h1 className="main-header text-left text-md-center">{s2BrandHeadline}</h1>
                <div
                  className="pad-t-3 pad-b-2 text-left text-md-center"
                  style={{ wordWrap: 'break-word' }}
                  dangerouslySetInnerHTML={createMarkup(s2BrandDescription)}
                ></div>
              </div>
            </Section>
          )}
          {!s3IsHidden && (
            <div className="fullWidthContainer pad-t-3 pad-b-4">
              {s3MediaLink?.includes('drive.google.com') ? (
                <VizSensor
                  partialVisibility
                  onChange={(isVisible) => {
                    if (isVisible && allreadyLoaded === false) {
                      section3Video.current.src = `${s3MediaLink}`;
                      https: setAllReadyLoaded(true);
                    }
                  }}
                >
                  <iframe
                    width="100%"
                    height="750"
                    frameBorder="0"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="fullWidthContainer__inner"
                    ref={section3Video}
                  ></iframe>
                </VizSensor>
              ) : (
                <VideoBlock
                  videoUrl={`${toValidEmbedVideoUrl(s3MediaLink)}?autoplay=1`}
                ></VideoBlock>
              )}
            </div>
          )}

          {!useCustomTheme && (
            <div className="container pad-lr-10 d-block d-lg-none pt-4 pb-4">
              <div className="row">
                <RedemptionInstructions
                  slug={router.query.slug}
                  name={name}
                  isBrandInstructionUpdated={isBrandInstructionUpdated}
                  step1={step1}
                  step2={step2}
                  step3={step3}
                />
              </div>
            </div>
          )}

          {!deactivateBrandPageRecommendation && (
            <Section background="#F8F8F8" className="pad-tb-4 pad-lr-10">
              <h1 className="col-sm-12 main-header">
                Das könnte dich auch interessieren
              </h1>
              <div className="col-sm-12">
                {relatedCoupons && (
                  <div className="row">
                    {relatedCoupons.map((item, index) => {
                      return (
                        <Card
                          className="col-6 col-lg-3 coupon-card-grid mb-4 white-card"
                          cardClassName="border-grey"
                          key={index}
                        >
                          <Card.Image
                            url={`/brand/${item.brandSlug}`}
                            src={item.compressedImage || item.image}
                            alt={getCouponAltText(item)}
                          >
                            <Like
                              isLiked={item.isFavourite}
                              id={item.id}
                              onSuccess={() =>
                                setRelatedCoupons((coupons) =>
                                  toggleCouponFavorite(coupons, item.id)
                                )
                              }
                              onError={(status, data) => {
                                addMessage(data, 'error');
                              }}
                            />
                            <LimitOverlay message={getOverlayMessage(item)} className="border-rounded-top-14" />
                          </Card.Image>
                          <Card.Discount
                            type={item.discountType}
                            shortDescription={item.shortDescription}
                          >
                            {item.discountValue}
                          </Card.Discount>
                          <Card.ClientLogo src={item?.brandLogo} alt={`Logo der Brand ${item.brandSlug} gutschein`} />
                          <Card.Description>
                            Gültig bis:{' '}
                            <b>
                              {Intl.DateTimeFormat('de-DE', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                              }).format(new Date(item.validationDate))}
                            </b>
                            <br />
                          </Card.Description>
                          <Card.CouponButton
                            coupon={item}
                            getCouponCode={() =>
                              displayCouponCode(item.brandSlug)
                            }
                          >
                            <p>{item.code ? 'Code kopieren' : 'Gutscheincode'}</p>
                          </Card.CouponButton>
                        </Card>
                      );
                    })}
                  </div>
                )}
              </div>
            </Section>
          )}
        </Layout>
      </>
    );
  }
};

export default Brand;