import { ListBlock } from 'components';

const RedemptionInstructions = ({
  className = '',
  slug,
  name,
  isBrandInstructionUpdated,
  step1,
  step2,
  step3,
}) => {
  return (
    <div
      className={`RedemptionInstructions col-12 col-lg-4 ${className}`}
      id="steps"
    >
      {slug !== 'erwin-mueller' ? (
        <>
          <h3 className="sidebar-header">So löst du den Rabatt ein:</h3>
          <ListBlock>
            <ListBlock.Number>1</ListBlock.Number>
            <ListBlock.Content>
              <b>
                {isBrandInstructionUpdated
                  ? step1
                  : `Suche dir bei CaptainCoupon deinen passenden ${name} Gutschein aus und achte dabei auf die Gutscheinbedingungen.`
                }
              </b>
            </ListBlock.Content>
          </ListBlock>
          <ListBlock>
            <ListBlock.Number>2</ListBlock.Number>
            <ListBlock.Content>
              <b>
                {isBrandInstructionUpdated
                  ? step2
                  : `Klicke auf “Gutscheincode”, um den ${name} Rabattcode anzuzeigen und kopiere den Code.`}
              </b>
            </ListBlock.Content>
          </ListBlock>
          <ListBlock>
            <ListBlock.Number>3</ListBlock.Number>
            <ListBlock.Content>
              <b>
                {isBrandInstructionUpdated
                  ? step3
                  : `Klicke auf den Link zum ${name} Online Shop und gib den ${name} Gutscheincode bei deinem Einkauf im Warenkorb an.`}
              </b>
            </ListBlock.Content>
          </ListBlock>
        </>
      ) : (
        <>
          <h3 className="sidebar-header">So löst du den Rabatt ein:</h3>
          <ListBlock>
            <ListBlock.Number>1</ListBlock.Number>
            <ListBlock.Content>
              <b>
                Suche dir bei CaptainCoupon deinen passenden Gutschein aus.
                Achte dabei auf die Gutscheinbedingungen und klicke dann auf
                “Gutscheincode”, um den Code anzuzeigen.
              </b>
            </ListBlock.Content>
          </ListBlock>
          <ListBlock>
            <ListBlock.Number>2</ListBlock.Number>
            <ListBlock.Content>
              <b>Kopiere den Gutscheincode.</b>
            </ListBlock.Content>
          </ListBlock>
          <ListBlock>
            <ListBlock.Number>3</ListBlock.Number>
            <ListBlock.Content>
              <b>
                Klicke auf den Link zum Online Shop und gebe den Gutscheincode
                bei deinem Einkauf an.
              </b>
            </ListBlock.Content>
          </ListBlock>
        </>
      )}
    </div>
  );
};

export default RedemptionInstructions;
