import Head from 'next/head';
import { Layout } from '../components/';
import BrandsSection from '../sections/BrandsSection';
import CategoriesSection from '../sections/CategoriesSection';
import NewsletterSection from '../sections/NewsletterSection';
import styles from '../styles/sass/_section.module.scss';

export default function Marken() {
  return (
    <>
      <Head>
        <title>CaptainCoupon Marken</title>
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <Layout home>
        <BrandsSection
          showBtn={false}
          className="pad-md-b-9 pad-b-4 pad-sm-t-5 pad-t-2 brands-overview"
          pageSize={-1}
        ></BrandsSection>
        <CategoriesSection
          sectionBg="#f5f5f5"
          className={`pad-md-t-9 pad-t-4 ${styles.categoriesSectionMarken}`}
        ></CategoriesSection>

        <NewsletterSection headerText="Erhalte&nbsp;die besten Angebote direkt per Mail!" />
      </Layout>
    </>
  );
}
