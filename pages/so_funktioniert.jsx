import Head from 'next/head';
import Link from 'next/link';
import Error from 'next/error';
import { useSelector } from 'react-redux';
import { useRef, useState } from 'react';
import { Modal } from 'reactstrap';
import { FaRegPlayCircle } from 'react-icons/fa/';
import { AiFillCloseCircle } from 'react-icons/ai/index';
import { Layout, Section, PlayVideoModal } from '../components';
import NewsletterSection from '../sections/NewsletterSection';
import HowDoesItWork from '../sections/HowDoesItWork';
import * as CommonService from 'services/common.service';
import {
  isVideoTypeAccepted,
  getVideoType,
} from '../components/VideoBlock/VideoBlock';
export async function getServerSideProps(context) {
  try {
    const headerImage = await CommonService.getPageHeaderImg('About');
    return {
      props: {
        headerImage,
      },
    };
  } catch (err) {
    return {
      props: {
        headerImage: '/headers/CaptainCoupon_Header_About_new.jpg',
      },
    };
  }
}

const SoFunkcioniert = ({ headerImage }) => {
  const section3Video = useRef(null);
  const [allreadyLoaded, setAllReadyLoaded] = useState(false);
  const [modal, setModal] = useState(false);
  const [policiesAcceptenceModal, setPoliciesAcceptenceModal] = useState(false);
  const { useCustomTheme } = useSelector((state) => state.settings);
  const videoUrl = 'https://player.vimeo.com/video/453945073';

  const toggleVideoModal = (e) => {
    e.stopPropagation();
    setModal(!modal);
    if (!isVideoTypeAccepted(videoUrl)) {
      setPoliciesAcceptenceModal(true);
    }
  };

  const playVideoOnAccept = (isAccepted) => {
    setPoliciesAcceptenceModal(false);
    if (!isAccepted) {
      setModal(false);
    } else {
      document.getElementById('SFVideo').src += '?autoplay=1';
    }
  };

  if (useCustomTheme) {
    return <Error statusCode={404} title={`Nicht gefunden!`} />;
  }

  return (
    <>
      <Head>
        <title>So funktioniert’s</title>
      </Head>
      <Layout home>
        <Section background="#fff">
          <div className="col-sm-12 layout-custom-header">
            <div className="header-image-wrap">
              <img
                src={headerImage}
                alt="CaptainCoupon-Markenbotschafter Gerhard Limone lacht erfreut und schaut in ein Laptop, das er in der Hand hält"
              />
            </div>
          </div>
        </Section>
        <Section
          background="#fff"
          className={'pad-t-4 pad-md-t-5 pad-lg-b-5 section1-about'}
          customPadding={true}
        >
          <div className="col-sm-12 text-center">
            <h1 className="main-header text-center">
              Mit CaptainCoupon nie wieder zum Normalpreis einkaufen!
            </h1>
            <p className="text-n section1-about-content">
              Das Gutscheincode-Portal CaptainCoupon hilft dir dabei, Gutschein-
              und Promocodes für die besten Rabatte, Aktionen und Deals vieler
              Marken zu finden. Unsere Gutscheine und Coupons sind dank der 100%
              Gutscheincode-Gültigkeitsgarantie immer gültig. Bei CaptainCoupon
              kannst du dir also sicher sein, dass der von dir ausgewählte
              Gutschein funktioniert und du bei deinem nächsten Einkauf
              garantiert Geld sparst.
              <br />
              <br />
              Auf CaptainCoupon findest du Gutscheincodes zur Einlösung in
              Onlineshops und Gutscheincodes, die du im Ladengeschäft an der
              Kasse einlösen kannst.
            </p>
          </div>
        </Section>
        <HowDoesItWork />

        <Section
          background="#81e9f0"
          containerClass={'container-lg video-section-landing'}
          customPadding={true}
          className="brand-ambasador"
        >
          <div className="container padding-side-25">
            <div className="row">
              <div className="col-lg-6 col-sm-12 align-content-center flex-wrap pad-tb-10 pad-lg-r-3 text-center text-lg-left padding-side-25">
                <h1 className="video-section-h1 text-center text-lg-left">
                  Gerhard Limone als exklusiver Markenbotschafter von
                  CaptainCoupon{' '}
                </h1>
                <p className="text-n text-lg-left text-center pr-lg-5 pad-tb-2">
                  In humorvollen und exklusiven Unboxing-Videos präsentiert
                  euch, die bereits jetzt schon kultige Kunstfigur Gerhard
                  Limone viele Produkte, Marken und Gutscheincodes auf
                  CaptainCoupon.de.
                </p>
              </div>
            </div>
          </div>
          <div
            className="col-lg-6 col-sm-12 video-placeholder-container"
            onClick={toggleVideoModal}
          >
            <FaRegPlayCircle className="video-placeolder-play" />
            <div className="video-placeolder-overlay"></div>
            <iframe
              src={videoUrl}
              frameBorder="0"
              width="1360px"
              height="720px"
              allow=" autoplay; encrypted-media; gyroscope; picture-in-picture"
            ></iframe>
          </div>
          <Modal
            isOpen={modal}
            toggle={toggleVideoModal}
            className={`modal-90w ${modal && 'custom-video-size'} `}
          >
            <AiFillCloseCircle
              onClick={toggleVideoModal}
              data-play="play"
              className="closeVideoModal"
            ></AiFillCloseCircle>
            <div className="embed-responsive embed-responsive-16by9">
              <iframe
                id="SFVideo"
                src={
                  isVideoTypeAccepted(videoUrl)
                    ? `${videoUrl}?autoplay=1`
                    : videoUrl
                }
                frameBorder="0"
                allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                className="embed-responsive-item"
              ></iframe>
            </div>
          </Modal>
          {policiesAcceptenceModal && (
            <PlayVideoModal
              videoType={getVideoType(videoUrl)}
              playVideoOnAccept={playVideoOnAccept}
            />
          )}
        </Section>
        <Section
          background="#fff"
          className="pad-t-10 pad-lg-b-8 pad-md-b-0 pad-b-8"
          customPadding={true}
        >
          <div className="col-sm-12 text-center">
            <h1 className="main-header text-center">Über CaptainCoupon</h1>
            <p className="text-n pad-t-3 pad-b-1 pad-lg-b-3">
              CaptainCoupon ist Deutschlands erstes Gutscheincode-Portal mit der
              100% Gutscheincode-Gültigkeitsgarantie. Auf CaptainCoupon findest
              du nur gültige Gutscheincodes, Rabatte und Coupons für die
              beliebtesten Marken und Online-Shops. Egal, ob Du bei deinem
              nächsten paar Socken von Snocks, deiner Traumküche von Marquardt
              Küchen, oder bei deinem nächsten Einkauf bei Reifendirekt.de –
              Europas größtem Reifenhändler - einkaufen möchtest, solltest du
              unbedingt vorher bei CaptainCoupon vorbeischauen und dir einen
              gültigen Gutscheincode holen, um viel Geld bei deinem Einkauf zu
              sparen.
              <br />
              <br />
              Dank unserer 100% Gutscheincode-Gültigkeitsgarantie kannst du dir
              immer sicher sein, dass der ausgewählte Rabatt oder Promo-Code
              einwandfrei funktioniert und du dich nie wieder über einen
              ungültigen Gutscheincode ärgern musst.
              <br />
              <br />
              <b>Beachte bitte folgendes:</b>
              <br />
              <br />
              Gutscheincodes zur Einlösung im E-Commerce kannst du ganz einfach
              in die Zwischenablage kopieren, nachdem du auf „Gutscheincode“
              geklickt hast und dann im Online-Shop unseres Partners einlösen.
              Achte dafür auf das Feld „Gutscheincode einlösen“ oder das Feld
              „Hast du einen Rabatt?“, während des Bezahlvorgangs im
              Online-Shop, wo du den Coupon einlösen möchtest. Handelt es sich
              um einen Gutschein für ein Ladengeschäft, kannst du den Gutschein
              auf dem Handy vorzeigen, als PDF downloaden und auf dem Gerät
              deiner Wahl speichern oder einfach ausdrucken und dann an der
              Kasse des Ladengeschäfts zur Einlösung vorzeigen.
              <br />
              <br />
              Bei CaptainCoupon findest du nur gültige Gutscheincodes und
              Rabatte aus den Bereichen Mobilität, Haus & Wohnen, Freizeit &
              Hobbies, Reisen, Essen & Trinken, Entertainment & Bildung, Mode,
              Technik & Mobilfunk, Baby & Kind, Sport, Gesundheit sowie Beauty &
              Parfum.
              <br />
              <br />
              Bei CaptaiCoupon findest du Gutscheincodes aus den Kategorien:
              Mobilität, Haus & Wohnen, Freizeit & Hobbies, Reisen, Essen &
              Trinken, Entertainment & Bildung, Mode, Technik & Mobilfunk, Baby
              & Kind, Sport, Gesundheit sowie Beauty & Parfum.
              <br />
              <br />
              <b>Möchtest du keinen Deal und Gutscheincode mehr verpassen? </b>
              <br />
              <br />
              Und mit unserem kostenlosen Newsletter sorgen wir dafür, dass du
              kein Angebot deiner Lieblingsmarken und Online-Shops mehr
              verpasst. Sobald du dich für unseren Newsletter angemeldet hast,
              informieren wir dich höchstens einmal wöchentlich über die besten
              und neusten Rabatte und Gutscheincodes. Mit unserem Newsletter
              bleibst Du immer auf dem Laufenden und verlierst niemals den
              Überblick über die besten Gutscheincodes und dein Sparpotential.
            </p>
          </div>
        </Section>
        {/*	video block  */}

        {/* <div className="fullWidthContainer">
        <VideoBlock videoUrl="https://player.vimeo.com/video/458539796"></VideoBlock>  
      </div> 

      //commented out because it should be temporary hidden
      */}

        {/*	newsletter block  */}
        <NewsletterSection headerText="Erhalte&nbsp;die besten Angebote direkt per Mail!" />
      </Layout>
    </>
  );
};
export default SoFunkcioniert;
