import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import * as SettingsActions from 'redux-store/settings/actions';
import { SET_MODAL } from 'redux-store/settings/types';
import { getStoredPayment } from 'services/adyen.service';
import * as ConsumerService from 'services/consumer.service';
import { getParsedDate } from 'util/helpers';
import {
  CancelSubscriptionModal,
  DeleteAccountModal,
  EmailInfoBox,
  Layout,
  Loading,
  ModalGeneric,
  PaymentErrorModal,
  Section,
  StoredPaymentInfoBox,
  SubscriptionInfoBox,
  SubscriptionPaymentDialog,
  UserInfoBox,
} from 'components';

const Settings = () => {
  const router = useRouter();
  const [startSubModal, setStartSubModal] = useState(false);
  const [currentUserFromDb, setCurrentUserFromDb] = useState(false); //TODO improve logic when working on subscription
  const [allFormData, setAllFormData] = useState();
  const [updateStatus, setUpdateStatus] = useState({
    type: '',
    message: '',
  });
  const [storedPayment, setStoredPayment] = useState();
  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);

  const {
    hasValidSub,
    modal,
    modalCancelSubscrtiption,
    subscriptionProcessing,
    subscriptionData,
    showEditUserForm,
  } = useSelector((state) => state.settings);
  const dispatch = useDispatch();

  useEffect(() => {
    if (!!Object.entries(updateStatus).length) {
      const timer = setTimeout(() => {
        setUpdateStatus({});
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [updateStatus]);

  const initData = async () => {
    try {
      const consumer = await ConsumerService.getConsumer();
      setCurrentUserFromDb(consumer.data);

      const { dateOfBirth, ...consumerData } = consumer.data;

      setAllFormData({
        ...consumerData,
        dateOfBirth: dateOfBirth
          ? dateOfBirth.length === 24
            ? dateOfBirth
            : dateOfBirth + 'T10:00:00.000Z'
          : '',
      });
    } catch (error) {
      await router.push('/');
    }
  };

  useEffect(() => {
    initData();
    getStoredPayment().then((response) => setStoredPayment(response.data));
    dispatch(SettingsActions.checkUserSubscription());
    dispatch(SettingsActions.getUserSubscription());
  }, []);

  const processChildForm = async (data) => {
    const oldUserAttributes = localStorage.getItem('userAttributes');
    localStorage.setItem(
      'userAttributes',
      JSON.stringify({ ...oldUserAttributes, ...data })
    );

    const { registrationCode, ...newDataForConsumer } = data;
    setAllFormData({ ...allFormData, ...newDataForConsumer });
    dispatch(SettingsActions.editUser(newDataForConsumer, setUpdateStatus));
  };

  const updateUserEmails = async () => {
    const consumerData = await ConsumerService.getConsumer();
    const newData = {
      ...allFormData,
      emails: consumerData?.data?.emails,
    };
    setAllFormData(newData);
  };

  const deleteAccount = () => {
    dispatch(
      SettingsActions.deleteAccount(setUpdateStatus, () =>
        setTimeout(async () => {
          await router.push('/logout');
        }, 1000)
      )
    );
  };

  const cancelSubscription = async () => {
    await dispatch(SettingsActions.cancelUserSubscription(setUpdateStatus));
    dispatch(SettingsActions.getUserSubscription());
  };

  const onPaymentError = () => {
    setShowPaymentErrorModal(true);
  };

  const displaySettings = (
    <>
      <Section
        background="#fff"
        className="pad-sm-t-5 pad-t-2 pad-lg-b-5"
        customPadding={true}
      >
        <h1 className="main-header col-sm-12 pad-b-2 text-left">
          Willkommen in den Einstellungen!
        </h1>
        <p className="col-sm-12 text-n settings-description">
          Hier hast du die Möglichkeit deine Profildaten sowie deine
          Mitgliedschaft zu bearbeiten oder anzupassen.
        </p>
        <div className="container pad-b-8">
          {updateStatus?.message && (
            <div
              className={`alert alert-${updateStatus.type} text-center p-3 mar-b-0`}
            >
              {updateStatus.message}
            </div>
          )}
          <div className="row">
            <div className="col-sm-12 col-md-6 pad-b-2 pad-t-2">
              <div className="container">
                <div className="row">
                  <UserInfoBox
                    userData={allFormData}
                    getParsedDate={getParsedDate}
                    processChildForm={processChildForm}
                    showEditUserForm={showEditUserForm}
                    dispatch={dispatch}
                  />

                  <EmailInfoBox
                    userData={allFormData}
                    updateUserEmails={updateUserEmails}
                  />
                </div>
              </div>
            </div>

            <div className="col-sm-12 col-md-6 pad-b-3 second-column">
              <div className="container">
                <div className="row">
                  <SubscriptionInfoBox
                    setStartSubModal={setStartSubModal}
                    subscriptionProcessing={subscriptionProcessing}
                    subscriptionData={subscriptionData}
                    hasValidSub={hasValidSub}
                    dispatch={dispatch}
                  />

                  <StoredPaymentInfoBox
                    hasValidSub={hasValidSub}
                    storedPayment={storedPayment}
                  />

                  <div
                    className={`${
                      hasValidSub && `lower-box`
                    }  gray-box neutral-box col-sm-12 mar-t-2 second-row`}
                  >
                    <div className="full-height-container d-flex flex-column h-10">
                      <div className="row mt-auto">
                        <div className="col-sm-12 text-right">
                          <button
                            type="button"
                            className="btn btn-dark group-button"
                            onClick={() => {
                              dispatch({ type: SET_MODAL, payload: true });
                            }}
                          >
                            Konto löschen
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Section>
    </>
  );

  return (
    <>
      <Head>
        <title>CaptainCoupon Einstellungen</title>
      </Head>
      <Layout register customContainerFluidClass="container-footer-bottom">
        {currentUserFromDb ? (
          <>
            {displaySettings}
            <CancelSubscriptionModal
              cancelSubscriptionHandler={cancelSubscription}
              modal={modalCancelSubscrtiption}
              dispatch={dispatch}
            />
            <DeleteAccountModal
              dispatch={dispatch}
              modal={modal}
              deleteAccount={deleteAccount}
            />
            {/* start subscription modal*/}
            <ModalGeneric
              modal={startSubModal}
              title="Kasse"
              className="cancel-sub-modal"
              displayCloseButton={true}
              onClose={() => setStartSubModal(false)}
            >
              <SubscriptionPaymentDialog
                consumer={currentUserFromDb}
                onSuccess={() => {
                  router.push('/');
                }}
                onPaymentError={onPaymentError}
                onClose={() => setStartSubModal(false)}
              />
            </ModalGeneric>
            <PaymentErrorModal
              show={showPaymentErrorModal}
              onClick={() => setShowPaymentErrorModal(false)}
            />
          </>
        ) : (
          <Loading />
        )}
      </Layout>
    </>
  );
};
export default Settings;
