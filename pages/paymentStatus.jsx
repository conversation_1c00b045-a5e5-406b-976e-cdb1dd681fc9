import { Layout, Section } from '../components';
import Head from 'next/head';

const paymentStatus = (props) => {
  return (
    <>
      <Head>
        <title>paymentStatus </title>
      </Head>
      <Layout home>
        <Section background="#fff" className="pad-tb-5">
          <p className="col-12">paymentStatus for your order</p>
        </Section>
      </Layout>
    </>
  );
};

export async function getServerSideProps({ req, res, query }) {
  console.log('paymentStatus query', query);
  console.log('paymentStatus res', res);
  console.log('paymentStatus req', req);
  return { props: {} };
}
export default paymentStatus;
