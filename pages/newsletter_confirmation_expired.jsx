import Head from 'next/head';
import { Layout, Section } from '../components';
import NewsletterSection from '../sections/NewsletterSection';

export default () => {
  return (
    <>
      <Head>
        <title>Newsletter confirmation expired</title>
      </Head>
      <Layout home>
        <Section background="#fff">
          <div className="col-sm-12 mar-t-5 layout-custom-header">
            <div className="header-image-wrap">
              <img
                src="/headers/Captaincoupon_Header_Newsletter-Confirmation.png"
                alt="CaptainCoupon-Markenbotschafter Gerhard <PERSON> blickt durch ein kleines Fernglas an der Kamera vorbei"
              />
            </div>
          </div>
        </Section>

        <Section
          background="#fff"
          className={'pad-t-4 pad-md-t-5 pad-lg-b-5 '}
          customPadding={true}
        >
          <div className="col-sm-12 pad-tb-1">
            <h2>
              Ahoi und danke für dein Interesse am Newsletter von CaptainCoupon.
              Leider ist der Link zur Newsletter-Anmeldung abgelaufen. Bitte
              trage deine E-Mail Adresse unten erneut ein und wir senden dir
              einen neuen Bestätigungslink zu. Bitte überprüfe auch dein
              SPAM-Postfach.
            </h2>
          </div>
        </Section>
        <NewsletterSection></NewsletterSection>
      </Layout>
    </>
  );
};
