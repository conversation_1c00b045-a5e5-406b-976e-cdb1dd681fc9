import { Section } from 'components';
import Head from 'next/head';
import { Button } from 'reactstrap';
import { Container } from 'reactstrap/lib';
import Benefits from '../components/Benefits/Benefits';

import badges from '../model/badges.json';
import styles from '../styles/sass/_registerPage.module.scss';

const RegisterCompleted = ({ onContinue }) => (
  <>
    <Head>
      <title>captain coupon:: register</title>
    </Head>
    <Section background="#fff" className={'pad-t-3'}>
      <div className="col-sm-12 text-center">
        <h1 className="main-header text-md-center text-lg-center">
          Ahoi und Willkommen als CC+ Mitglied
        </h1>
        <Benefits badges={badges} showHeading={false} />
      </div>
    </Section>
    <Container className="d-flex justify-content-center pb-4">
      <Button
        onClick={onContinue}
        className={styles.nextPrevBtn}
        style={{ width: '100%', marginTop: '12px' }}
      >
        zu den Gutscheincodes
      </Button>
    </Container>
  </>
);

export default RegisterCompleted;
