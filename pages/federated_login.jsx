import { useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

import { storeUserData } from 'services/auth.service';

import { Layout, Section } from 'components';

export async function getServerSideProps({ query }) {
  return {
    props: {
      idToken: query['id_token'] || null,
      refreshToken: query['refresh_token'] || null,
    },
  };
}

export default function LoginFinal({ idToken, refreshToken }) {
  const router = useRouter();

  useEffect(() => {
    if (idToken && refreshToken) {
      storeUserData(
        { idToken, refreshToken },
        ({ justLoggedIn, subscribed }) => {
          if (justLoggedIn) {
            if (!subscribed) {
              localStorage.setItem('showSubscribeModal', true);
            }

            router.push('/');
          }
          return;
        }
      );
    }
  }, []);

  return (
    <>
      <Head>
        <title>Login</title>
      </Head>
      <Layout home customContainerFluidClass="container-footer-bottom">
        <Section background="#fff">
          <div className="col-sm-12 mar-t-5">
            <img
              src="/headers/CaptainCoupon-imprint.png"
              alt="Vier nebeneinander schwebende rosa Luftballons an Schnüren, von denen der zweite die Form eines Sparschweins hat"
            />
          </div>
        </Section>

        <Section background="#fff" className={'pad-t-5'}>
          <div className="col-sm-12 text-center">
            <h1 className="main-header text-lg-center text-md-left text-left"></h1>
          </div>
        </Section>

        <Section background="#fff" className={'pad-t-4 pad-b-7'}></Section>
      </Layout>
    </>
  );
}
