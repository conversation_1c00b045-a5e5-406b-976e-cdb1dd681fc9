/** @type {import('next').NextConfig} */
const path = require('path');

const redirectHost = process.env.NEXT_PUBLIC_REDIRECT_SIGNIN
  ? new URL(process.env.NEXT_PUBLIC_REDIRECT_SIGNIN).hostname
  : '';

const nextConfig = {
  typescript: {
    ignoreBuildErrors: false,
  },
  sassOptions: {
    includePaths: [path.join(__dirname, 'styles')],
  },
  optimizeFonts: true,
  webpack(config) {
    config.resolve.modules.push(__dirname);
    config.resolve.alias = {
      ...config.resolve.alias,
      'redux-store': path.resolve(__dirname, 'redux-store/'),
      'components': path.resolve(__dirname, 'components/'),
      'services': path.resolve(__dirname, 'services/'),
      'util': path.resolve(__dirname, 'util/'),
      'sections': path.resolve(__dirname, 'sections/'),
      'context': path.resolve(__dirname, 'context/')
    };
    return config;
  },
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: `${process.env.NEXT_PUBLIC_REDIRECT_SIGNIN}/uploads/:path*`,
      },
      {
        source: '/media/:path*',
        destination: `${process.env.NEXT_PUBLIC_REDIRECT_SIGNIN}/media/:path*`,
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/traveldeals',
        destination: 'https://traveldeals-gewinnspiel.de',
        permanent: false,
        basePath: false,
      },
    ];
  },
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: redirectHost
      ? [
          {
            protocol: 'https',
            hostname: redirectHost,
            pathname: '/media/**',
          },
        ]
      : [],
  },
};

module.exports = nextConfig;
