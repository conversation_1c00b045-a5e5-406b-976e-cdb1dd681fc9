version: '3'
services:
  nginx-proxy:
    image: jwilder/nginx-proxy
    container_name: nginx-proxy
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./scripts/nginx/certs:/etc/nginx/certs
    depends_on:
      - web
  web:
    container_name: captain-coupon-web
    build: ./Dockerfile
    environment:
      - VIRTUAL_HOST=stg.captaincoupon.de
      - VIRTUAL_PORT=5000
