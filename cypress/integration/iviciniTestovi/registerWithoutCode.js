/// <reference types="Cypress" />

describe('Register First Step', () => {
  before(() => {});
  it('Visit the registration page ', () => {
    cy.visit('/register');
    cy.waitForReact();

    cy.get('h1.main-header').contains('<PERSON>de <PERSON>ch an, um Dir großartige');
    const randomMail = Math.round(Math.random() * 10000);

    cy.scrollTo(0, 600);
    cy.get('input.form-control[name="name"]').type('test-test', {
      force: true,
    });
    cy.get('input.form-control[name="family_name"]')
      .last()
      .type('ivan<PERSON>ski automatic');
    cy.get('select.form-control').select('male');
    cy.get('input.form-control[name="custom:street"]').type('<PERSON><PERSON> Menkov');
    cy.get('input.form-control[name="custom:houseNumber"]').type('1/a 3-9');
    cy.get('input.form-control[name="custom:postalCode"]').type('1000', {
      force: true,
    });
    cy.get('input.form-control[name="custom:place"]')
      .scrollIntoView()
      .type('Skopje', {
        force: true,
      });
    cy.get('input#rdp-form-control-birthdate').type('12/12/1999');
    cy.get('[name="email"]').type('<EMAIL>', {
      force: true,
    });
    cy.get('[name="password"]').type('111111!q');
    cy.get('[name="confirmPass"]').type('111111!q');
    cy.get('#defaultCheck1').check({ force: true });

    cy.get('form#registerForm').submit();
  });

  it('check second page ', () => {
    cy.get('h1.main-header').contains('Wähle deine Zahlungsart');
    cy.get(':nth-child(4) > .form-check-label').click();
    cy.get('.btn[type="submit"]').click();
  });

  it('check third step', (data) => {
    console.log('data', data);
    cy.get('h1.main-header').contains('Bitte überprüfe deine Daten!');

    //     cy.get('button').contains('Anmeldung bestätigenr').click();
  });
});
