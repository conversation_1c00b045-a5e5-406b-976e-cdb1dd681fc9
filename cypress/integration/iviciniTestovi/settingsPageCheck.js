/// <reference types="Cypress" />

describe('Register First Step', () => {
  before(() => {
    cy.visit('/');
  });

  it('open settings page', () => {
    cy.get('[data-cy="mainLoginBtn"]').contains('Anmelden').click();
    cy.get('[data-cy="loginEmail"]').clear().type('ivica.<PERSON><PERSON><PERSON><PERSON>@gmail.com');
    cy.get('[data-cy="loginPassword"]').clear().type('111111!q');
    cy.get('[data-cy="loginSubmitBtn"]').click();
    cy.get('.loading-wrapper ').should('be.visible');
    cy.get('[data-cy="myAccountLink"]')
      .should('be.visible')
      .then((data) => {
        cy.visit('/settings');
      });
  });

  it('end subscription show & close window  ', () => {
    cy.get('h1.main-header').contains('Einstellungen');
    cy.get('button.btn')
      .contains('Mitgliedschaft beenden')
      .click({ force: true });
    cy.get('[data-cy="endSubscriptionTitle"]').should('be.visible');
    cy.wait(1500);
    cy.get('[data-cy="endSubscriptionCancelBtn"]').click();

    cy.get('h1.main-header').should('be.visible');

    // endSubscriptionCompleteBtn
  });

  it('DELETE account show & close window  ', () => {
    cy.get('button.btn').contains('Konto löschen').click({ force: true });
    cy.get('[data-cy="deleteAccountTitle"]').should('be.visible');
    cy.wait(1500);
    cy.get('[data-cy="deleteSubmitBtn"]').click();

    cy.get('[data-cy="deleteAccountMessage"]').should('be.visible');
    cy.get('[data-cy="deleteCancelBtn"]').click();
    cy.get('h1.main-header').should('be.visible');

    //deleteSubmitBtn
    //deleteCancelBtn
  });
});
