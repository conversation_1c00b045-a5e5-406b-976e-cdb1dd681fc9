/// <reference types="Cypress" />

describe('Register First Step', () => {
  before(() => {
    cy.visit('/');
    cy.get('[data-cy="mainLoginBtn"]').contains('Anmelden').click();
  });

  it('Check Inputs are empty', () => {
    cy.get('[data-cy="loginSubmitBtn"]').click();
    cy.get('[data-cy="loginEmail"]')
      .next('.inline-error')
      .contains('Dies ist ein Pflichtfeld');

    cy.get('[data-cy="loginPassword"]')
      .parent()
      .next('.inline-error')
      .contains('Dies ist ein Pflichtfeld');
  });

  it('Check password toggle visibillity', () => {
    cy.get('[data-cy="loginPassword"]').as('passwordInput');
    cy.get('@passwordInput').type('password');
    cy.get('[data-cy="togglePasswordVisibility"]').click();
    cy.get('@passwordInput').should('have.attr', 'type', 'text');
  });

  it('Wrong email or password should fail', () => {
    cy.get('[data-cy="loginEmail"]').type(
      '<EMAIL>'
    );
    cy.get('[data-cy="loginPassword"]').type('sadasdasdasd');
    cy.get('[data-cy="loginSubmitBtn"]')
      .click()
      .then((data) => {
        cy.get('[data-cy="loginErrorMessage"]').contains(
          'E-Mail Adresse und Passwort stimmen nicht überein. Bitte'
        );
      });
  });
});
