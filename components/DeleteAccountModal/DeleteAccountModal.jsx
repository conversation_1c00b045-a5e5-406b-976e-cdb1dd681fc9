import { useRef, useState } from 'react';
import { GrClose } from 'react-icons/gr';
import { SET_MODAL } from 'redux-store/settings/types';
import { ModalGeneric } from 'components';
import styles from './DeleteAccountModal.module.scss';

const DeleteAccountModal = ({ modal, deleteAccount, dispatch, ...props }) => {
  const deleteConfirm = useRef();
  const [errorMessage, showMessage] = useState(false);
  return (
    <ModalGeneric modal={modal} className={styles.deleteAccModal}>
      <button
        className="close float-right"
        onClick={() => {
          dispatch({ type: SET_MODAL, payload: false });
        }}
      >
        <GrClose></GrClose>
      </button>
      <div className="row">
        <div className={`col ${styles.sideImageWrap} d-none d-lg-block`}></div>

        <div className="col custom-modal-padding pad-tb-2 pad-l-3 pad-r-3">
          <h1
            className={`text-left text-lg-center pad-t-3 pad-b-4 ${styles.deleteAccHeading}`}
            data-cy="deleteAccountTitle"
          >
            Willst du dein Konto bei CaptainCoupon wirklich löschen?
          </h1>
          <div className="pad-b-3">
            Bist du sicher, dass du nicht mehr von unseren Gutscheincodes
            profitieren willst und du dein Konto bei CaptainCoupon wirklich
            löschen möchtest?
            <br />
            <br />
            Noch ist es nicht zu spät – Bleib an Bord!
          </div>
          <div>
            <p>
              Bitte trage <b>DELETE</b> in folgendes Feld:
            </p>
            <input
              type="text"
              ref={deleteConfirm}
              className="form-control mar-tb-2"
            />
            {errorMessage && (
              <div
                className="alert alert-warning"
                data-cy="deleteAccountMessage"
              >
                ↑ Bitte trage <b>DELETE</b> in folgendes Feld ↑
              </div>
            )}
          </div>
          <button
            onClick={() => {
              if (deleteConfirm.current.value.toLowerCase() === 'delete') {
                deleteAccount();
              } else {
                showMessage(true);
                deleteConfirm.current.select();
              }
            }}
            className="btn btn-red float-right btn-tall"
            data-cy="deleteSubmitBtn"
          >
            Konto löschen
          </button>
          <button
            onClick={() => dispatch({ type: SET_MODAL, payload: false })}
            className="btn btn-dark btn-tall"
            data-cy="deleteCancelBtn"
          >
            abbrechen
          </button>
        </div>
      </div>
    </ModalGeneric>
  );
};

export default DeleteAccountModal;