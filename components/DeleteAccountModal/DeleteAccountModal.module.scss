@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');

.modalIcon {
  max-height: 85px;
  padding-bottom: 10px;
}

.sideImageWrap {
  position: relative;
  background: transparent
    url('/headers/captaincoupon_setting-delete-account.jpg') top center
    no-repeat;
  background-size: cover;
  margin-top: -16px;
  margin-bottom: -17px;
  margin-left: -1px;
}

.modalDialog {
  :global(button.close) {
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 100;
  }
}

.deleteAccModal {
  max-width: 1140px;
}

.deleteAccModal .modalBody {
  margin-left: -1px;
}

.deleteAccHeading {
  font-size: 40px;
  line-height: 43px;
  font-family: Montserrat;
  font-weight: 900;
}

@include media-breakpoint-down(md) {
  .deleteAccHeading {
    font-size: 30px;
    line-height: 35px;
  }
}