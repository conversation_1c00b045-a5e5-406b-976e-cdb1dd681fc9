import styles from './ListBlock.module.scss';

export default function ListBlock({ className = '', children }) {
  return <div className={`${styles.listBlock} ${className}`}>{children}</div>;
}

ListBlock.Number = ({ children }) => (
  <span className={styles.listBlock__number}>{children}</span>
);

ListBlock.Content = ({ children }) => (
  <div className={styles.listBlock__content}>{children}</div>
);