import styles from './Steps.module.scss';

export default function Steps({ className = '', children }) {
  return (
    <div className={`${styles.steps} ${className}`}>
      <div className={styles.steps__children}>{children}</div>
    </div>
  );
}

Steps.Step = function ({
  active = false,
  className = '',
  clickH<PERSON><PERSON>,
  children,
}) {
  return (
    <div
      className={`${styles.step} ${active ? styles.active : ''} ${className}`}
      onClick={clickHandler}
    >
      {children}
    </div>
  );
};

Steps.Number = function ({ className = '', children }) {
  return <div className={`${styles.number} ${className}`}>{children}</div>;
};

Steps.Label = function ({ className = '', children }) {
  return <label className={`${styles.label} ${className}`}>{children}</label>;
};