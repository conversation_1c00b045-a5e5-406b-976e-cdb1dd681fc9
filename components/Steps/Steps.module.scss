@import '../../styles/sass/mixins';

.steps {
  &__children {
    display: flex;
    position: relative;
    &:after {
      content: '';
      position: absolute;
      width: 100%;
      height: 2px;
      background-color: #000;
      top: 25px;
      z-index: 0;
    }
  }
  .step {
    flex: 2;
    text-align: center;
    .number {
      border: 3px solid #000;
      width: 50px;
      height: 50px;
      border-radius: 100%;
      text-align: center;
      line-height: 44px;
      font-size: 25px;
      font-weight: 900;
      margin: 0 auto;
      background-color: #fff;
      position: relative;
      z-index: 1;
    }
    .label {
      font-size: 14px;
      padding-top: 10px;
      font-weight: 300;

      @include mediaMobile {
        display: none;
      }
    }
    &.active {
      .number {
        background-color: #000;
        color: #fff;
      }
    }
    &:first-child {
      flex: 1;
      text-align: left;
      .number {
        margin-left: 0;
        margin-right: auto;
      }
    }
    &:nth-child(2) {
      .number {
        margin-left: 35%;
        margin-right: auto;
      }
      .label {
        margin-left: -20%;
        margin-right: auto;
      }
      @include mediaTablet {
        .number {
          margin-left: 40%;
          margin-right: auto;
        }
        .label {
          margin-left: auto;
          margin-right: 0;
        }
      }

      @include mediaLargeDesktop {
        .number {
          margin-left: 45%;
          margin-right: auto;
        }
        .label {
          margin-left: auto;
          margin-right: -5%;
        }
      }
    }
    &:nth-child(3) {
      .number {
        margin-left: auto;
        margin-right: 35%;
      }
      .label {
        margin-left: auto;
        margin-right: -15%;
      }

      @include mediaTablet {
        .number {
          margin-left: auto;
          margin-right: 40%;
        }
        .label {
          margin-left: 0;
          margin-right: auto;
        }
      }

      @include mediaLargeDesktop {
        .number {
          margin-left: auto;
          margin-right: 45%;
        }
        .label {
          margin-left: 0;
          margin-right: 5%;
        }
      }
    }
    &:last-child {
      flex: 1;
      text-align: right;
      .number {
        margin-right: 0;
        margin-left: auto;
      }
    }
  }
}