@import '../../styles/sass/mixins';

.Steps {
  &__children {
    display: flex;
    position: relative;
    &:after {
      content: '';
      position: absolute;
      width: 100%;
      height: 2px;
      background-color: #000;
      top: 25px;
      z-index: 0;
    }
  }
  .Step {
    flex: 2;
    text-align: center;
    .Number {
      border: 3px solid #000;
      width: 50px;
      height: 50px;
      border-radius: 100%;
      text-align: center;
      line-height: 44px;
      font-size: 25px;
      font-weight: 900;
      margin: 0 auto;
      background-color: #fff;
      position: relative;
      z-index: 1;
    }
    .Label {
      font-size: 14px;
      padding-top: 10px;
      font-weight: 300;

      @include mediaMobile {
        display: none;
      }
    }
    &.active {
      .Number {
        background-color: #000;
        color: #fff;
      }
    }
    &:first-child {
      flex: 1;
      text-align: left;
      .Number {
        margin-left: 0;
        margin-right: auto;
      }
    }
    &:nth-child(2) {
      .Number {
        margin-left: 35%;
        margin-right: auto;
      }
      .Label {
        margin-left: -20%;
        margin-right: auto;
      }
      @include mediaTablet {
        .Number {
          margin-left: 40%;
          margin-right: auto;
        }
        .Label {
          margin-left: auto;
          margin-right: 0;
        }
      }

      @include mediaLargeDesktop {
        .Number {
          margin-left: 45%;
          margin-right: auto;
        }
        .Label {
          margin-left: auto;
          margin-right: -5%;
        }
      }
    }
    &:nth-child(3) {
      .Number {
        margin-left: auto;
        margin-right: 35%;
      }
      .Label {
        margin-left: auto;
        margin-right: -15%;
      }

      @include mediaTablet {
        .Number {
          margin-left: auto;
          margin-right: 40%;
        }
        .Label {
          margin-left: 0;
          margin-right: auto;
        }
      }

      @include mediaLargeDesktop {
        .Number {
          margin-left: auto;
          margin-right: 45%;
        }
        .Label {
          margin-left: 0;
          margin-right: 5%;
        }
      }
    }
    &:last-child {
      flex: 1;
      text-align: right;
      .Number {
        margin-right: 0;
        margin-left: auto;
      }
    }
  }
}
