.registerCardTop {
  width: 50%;
  margin-top: 30px;
}

.registerCardFinal {
  width: 50%;
  margin-top: 30px;
}

.codeInfo {
  display: flex;
  flex-direction: row;

  img {
    margin-right: 9px;
  }

  span {
    font-size: 15px;
    font-weight: 900;
    letter-spacing: 0px;
    line-height: 1.2;
  }
}

.codeInfoText {
  font-size: 12px;
  letter-spacing: 0px;
}

.couponCodeInput {
  font-size: 16px;
  text-align: left;
  color: #000000;
  letter-spacing: 0px;
}

.couponCodeInput::placeholder {
  font-size: 16px/19px;
  text-align: left;
  color: #000000;
  letter-spacing: 0px;
}

.badgeCardTitle {
  font-size: 25px;
  color: #000000;
  font-weight: 600;
  letter-spacing: 0px;
}

.badgeCardText {
  font-size: 26px;
  color: #000000;
  letter-spacing: 0px;
}

.cardOverlay {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 100;
  cursor: pointer;
}

.prodPackageContainer {
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: stretch;
}

.prodPackage {
  padding: 15px 15px;
  box-sizing: border-box;
}