import { useState, useCallback, useEffect } from 'react';
import { Container, Row, Col, Input } from 'reactstrap';
import { ProdPackageCard } from 'components';
import * as adyenService from 'services/adyen.service';
import * as subscriptionService from 'services/subscription.service';
import { debounce, getPackageForSubscriptionDiscountCode } from 'util/helpers';
import { useRouter } from 'next/router';

import styles from './PackageSelect.module.scss';

export default function PackageSelect({
  selected,
  onSelect,
  onClickSelected,
  setProductInfo,
  onProductFromCouponCodeLoad = () => undefined,
  refProp,
  couponCodeInputLabel,
  hideDefaultOffers,
}) {
  const router = useRouter();

  const [registrationCode, setRegistrationCode] = useState(
    selected?.registrationCode
  );
  const [registrationCodeError, setRegistrationCodeError] = useState(false);
  const [products, setProducts] = useState([]);
  const [productFromCoupon, setProductFromCoupon] = useState(null);

  const providedRegistrationCode = router.query.registrationCode;
  useEffect(() => {
    if (!providedRegistrationCode || registrationCode) {
      return;
    }
    setRegistrationCode(providedRegistrationCode);
    debouncedCouponCheck(providedRegistrationCode);
  }, [providedRegistrationCode, setRegistrationCode]);

  const setProductsAdditionalInfo = (products) => {
    if (hideDefaultOffers && !productFromCoupon) {
      setProductInfo(null);
      return;
    }
    setProductInfo(
      products
        .filter((product) => product.additionalInformation)
        .map((product) => ({
          info: '*'.repeat(product.position) + product.additionalInformation,
        }))
    );
  };

  const onProductFetchFromCodeSuccess = useCallback(
    (data) => {
      setProductFromCoupon(data.product);
      onProductFromCouponCodeLoad(data.product);
      onSelect({
        productPackageId: data.product.productPackageId,
        registrationCode: data.code,
      });
      if (refProp) {
        const headerHeight = 100; // WORKAROUND: we apply a static offset to compensate for the header height
        const productsContainerPosInViewport =
          refProp.current.getBoundingClientRect().top;
        const isProductsContainerOffScreen =
          productsContainerPosInViewport < headerHeight;
        if (isProductsContainerOffScreen) {
          const scrollTargetPosY =
            productsContainerPosInViewport +
            document.documentElement.scrollTop -
            headerHeight;
          window.scrollTo({ top: scrollTargetPosY, behaviour: 'smooth' });
        }
      }
    },
    [registrationCode]
  );

  const debouncedCouponCheck = useCallback(
    debounce(async function (couponCode) {
      // reset selected product
      onSelect({});
      setProductFromCoupon(null);

      if (couponCode) {
        getPackageForSubscriptionDiscountCode(
          couponCode,
          onProductFetchFromCodeSuccess,
          setRegistrationCodeError
        );
      } else {
        setRegistrationCodeError(null);
        onProductFromCouponCodeLoad(null);
      }
    }, 1000),
    []
  );

  const onUnselectFromCoupon = () => {
    onSelect({});
    setRegistrationCode('');
    setProductFromCoupon(null);
    onProductFromCouponCodeLoad(null);
  };

  useEffect(() => {
    adyenService.getSubscriptionPackagesTop().then((response) => {
      setProducts(response.data);
      if (selected?.registrationCode) {
        subscriptionService
          .getProductPackageForDiscountCode(selected.registrationCode)
          .then((response) => {
            if (response?.data) {
              onProductFetchFromCodeSuccess({
                product: response.data,
                code: selected.registrationCode,
              });
            }
          })
          .catch((error) => {
            const errorMessage = error?.response?.data;
            if (errorMessage) {
              setRegistrationCodeError(errorMessage);
            }
            onProductFromCouponCodeLoad(null);
          });
      } else {
        setProductsAdditionalInfo(response.data);
      }
    });
  }, []);

  useEffect(() => {
    if (productFromCoupon) {
      setProductsAdditionalInfo([{ ...productFromCoupon, position: 1 }]);
    } else {
      setProductsAdditionalInfo(products);
    }
  }, [productFromCoupon]);

  const isAnyProductSelected = !!selected?.productPackageId;
  const showDefaultOffers = !productFromCoupon && !hideDefaultOffers;
  const showAnyOffer = showDefaultOffers || productFromCoupon;

  return (
    <div ref={refProp}>
      {showAnyOffer && (
        <Container className="pt-2 pb-2">
          <Row className="justify-content-center pb-4">
            {productFromCoupon && (
              <ProdPackageCard
                key={productFromCoupon?.productPackageId}
                product={productFromCoupon}
                selectedFromCoupon
                onClickSelected={onClickSelected}
                onUnselectFromCoupon={onUnselectFromCoupon}
              />
            )}
            {showDefaultOffers && (
              <div className={styles.prodPackageContainer}>
                {products.map((product) => {
                  const isProductSelected =
                    product.productPackageId === selected?.productPackageId;
                  const isOtherProductSelected =
                    isAnyProductSelected && !isProductSelected;
                  return (
                    <ProdPackageCard
                      key={product.productPackageId}
                      product={product}
                      selected={isProductSelected}
                      sentBack={isOtherProductSelected}
                      onClick={(productPackageId) =>
                        onSelect({ productPackageId })
                      }
                      onClickSelected={onClickSelected}
                      className={styles.prodPackage}
                    />
                  );
                })}
              </div>
            )}
          </Row>
        </Container>
      )}

      <Container>
        <Row className="pb-5">
          <Col xs={12} md={6}>
            <Row className="pl-3 pr-3 pt-3">
              <div className={`${styles.codeInfo} align-items-center`}>
                <img
                  src="/icons/fast-forward-double-right-arrows.svg"
                  alt="Ein doppelter „Vorspulen-Pfeil nach rechts"
                />
                <span>{couponCodeInputLabel}</span>
              </div>
            </Row>
          </Col>
          <Col className="pb-3 pt-3" xs={12} md={6}>
            <Input
              type="text"
              id="registrationCode"
              value={registrationCode}
              onChange={(event) => {
                setRegistrationCode(event.target.value);
                debouncedCouponCheck(event.target.value);
              }}
              placeholder="Gutscheincode"
              className={styles.couponCodeInput}
            />
            {registrationCodeError && (
              <p className="inline-error text-left pad-t-1">
                {registrationCodeError}
              </p>
            )}
          </Col>
        </Row>
      </Container>
    </div>
  );
}