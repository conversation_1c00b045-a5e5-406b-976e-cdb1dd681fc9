import { useSelector } from 'react-redux';

function Theme(props) {
  const { useCustomColors, accentBackgroundColor, accentColor } = useSelector(
    (state) => state.settings
  );

  const rgbaColorSetAlpha = (color, alpha) =>
    color.replace(/[\d\.]+\)$/g, `${alpha})`);

  const ACCENT_COLOR = { BLACK: 'black', WHITE: 'white' };

  const negativeAccent = (accentColor) =>
    accentColor === ACCENT_COLOR.WHITE
      ? ACCENT_COLOR.BLACK
      : ACCENT_COLOR.WHITE;

  return (
    <div className="app-theme">
      {props.children}
      {useCustomColors ? (
        <style jsx global>{`
          .Header {
            background-color: ${accentBackgroundColor};
          }
          .Header .Navigation__link {
            color: ${accentColor};
          }
          .Header .UtilitiesMenu__link {
            color: ${accentColor};
          }
          a.so_funktionierts-link {
            color: ${accentColor};
          }
          a.so_funktionierts-link:hover {
            color: ${accentColor};
          }
          .Header .btn-dark {
            color: ${negativeAccent(accentColor)} !important;
            background-color: ${accentColor};
            border-color: ${accentColor};
          }
          .Header .btn-dark:hover {
            color: ${accentColor} !important;
            border-color: ${negativeAccent(accentColor)};
            background-color: ${negativeAccent(accentColor)} !important;
          }
          .Header .main-logo.captain-coupon-logo {
            filter: ${accentColor === ACCENT_COLOR.WHITE ? 'invert(1)' : ''};
          }
          .Header .logos-container .collaboration-symbol {
            color: ${accentColor};
          }

          .main-page-content
            .container
            > .row
            > .container
            > .row
            > .InlineItem
            a:hover {
            color: ${accentBackgroundColor};
          }

          .main-page-content .main-section {
            background-color: ${accentBackgroundColor} !important;
          }
          .main-page-content .section1 {
            background-color: ${accentBackgroundColor} !important;
          }
          .main-page-content .section1 .main-header {
            color: ${accentColor};
          }
          .main-page-content .section1 span {
            color: ${accentColor} !important;
          }
          .main-page-content .section1 strong {
            color: ${accentColor} !important;
          }
          .main-page-content .section1 a {
            color: ${accentColor} !important;
          }
          .main-page-content .main-header.landing-page-header {
            color: ${accentColor};
          }
          .main-page-content .subtitle-landing-page {
            color: ${accentColor};
          }
          .main-page-content .guarantee-section {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor};
          }
          .main-page-content .guarantee-section .main-header {
            color: ${accentColor};
          }
          .main-page-content .guarantee-section .btn-dark {
            color: ${negativeAccent(accentColor)};
            background-color: ${accentColor};
            border-color: ${accentColor};
          }
          .main-page-content .guarantee-section .btn-dark:hover {
            border-color: ${accentBackgroundColor};
            color: ${accentColor};
            background-color: ${accentBackgroundColor} !important;
          }
          .main-page-content .newsletter-section {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor};
          }
          .main-page-content .newsletter-section .main-header {
            color: ${accentColor};
          }
          .main-page-content .newsletter-section a {
            color: ${accentColor};
          }

          .main-page-content .btn.btn-dark:hover {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor};
            border-color: ${accentBackgroundColor};
          }

          .Card p:hover {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor};
          }

          .CardHorizontal__coupon-code-btn .rabatte-button-brand:hover {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor} !important;
          }
          .CardHorizontal__zum-shop-btn {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor} !important;
          }
          .CardHorizontal__zum-shop-btn:focus {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor} !important;
            border-color: ${accentBackgroundColor} !important;
            box-shadow: 0 0 0 0.2rem
              ${rgbaColorSetAlpha(accentBackgroundColor, '0.25')} !important;
          }
          .CardHorizontal .copy-btn:hover {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor} !important;
          }
          .CardHorizontal .copy-btn .rabatte-button-brand {
            background-color: inherit !important;
            color: inherit !important;
          }

          .Footer {
            background-color: ${accentBackgroundColor};
            color: ${accentColor};
          }
          .Footer a {
            color: ${accentColor};
          }

          .brand-ambasador {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor} !important;
          }
          .brand-ambasador .video-section-h1 {
            color: ${accentColor};
          }
          .brand-ambasador a {
            color: ${accentBackgroundColor};
            background-color: ${accentColor};
            border-color: ${accentColor};
          }
          .brand-ambasador a:hover {
            border-color: ${accentColor};
            color: ${accentColor};
            background-color: ${accentBackgroundColor} !important;
          }

          .login-modal-form .container .btn-main {
            background-color: ${accentBackgroundColor};
            color: ${accentColor};
          }
          .login-modal-form .container .btn-main:hover {
            background-color: ${negativeAccent(accentColor)};
            color: ${accentColor};
          }
          .login-modal-form .login-btn:hover {
            border-color: ${accentBackgroundColor};
            background-color: ${accentBackgroundColor};
            color: ${accentColor};
          }
          .form-control:focus {
            border-color: ${accentBackgroundColor};
          }
          .forgot-password-modal .btn-dark:hover {
            background-color: ${accentBackgroundColor};
            color: ${accentColor};
            border-color: ${accentColor};
          }

          .modal-content.vimeo-modal .btn-main {
            background-color: ${accentBackgroundColor};
            color: ${accentColor};
            border-color: ${accentColor};
          }
          .modal-content.vimeo-modal .btn-main:hover {
            background-color: ${negativeAccent(accentColor)};
            color: ${accentColor};
            border-color: ${accentColor};
          }

          .modal-content.cookie-modal .btn-main {
            background-color: ${accentBackgroundColor};
            color: ${accentColor};
            border-color: ${accentColor};
          }
          .modal-content.cookie-modal .btn-main:hover {
            background-color: ${negativeAccent(accentColor)};
            color: ${accentColor};
            border-color: ${accentColor};
          }

          .btn-cancel-subs-modal:hover {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor} !important;
            border-color: ${accentBackgroundColor} !important;
          }

          .delete-acc-modal .btn-dark:hover {
            background-color: ${accentBackgroundColor} !important;
            color: ${accentColor} !important;
            border-color: ${accentBackgroundColor};
          }
        `}</style>
      ) : null}
    </div>
  );
}

export default Theme;
