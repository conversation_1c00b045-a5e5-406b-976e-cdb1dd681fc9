@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.mediaOutlets {
  width: 100%;
  .logos {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;

    .logo {
      img {
        max-width: 180px;
        max-height: 90px;
      }
    }

    @include media-breakpoint-down(lg) {
      .logo {
        img {
          max-width: 140px;
          max-height: 70px;
        }
      }
    }

    @include media-breakpoint-down(md) {
      .logo {
        width: 33%;

        &:nth-child(3n + 1) {
          text-align: left;
        }

        &:nth-child(3n + 2) {
          text-align: center;
        }

        &:nth-child(3n + 3) {
          text-align: right;
        }

        img {
          max-width: 160px;
          max-height: 80px;
        }
      }
    }
  }

  @include media-breakpoint-down(xs) {
    .logos .logo {
      width: 50%;
      text-align: center !important;

      &:nth-child(2n + 1) {
        text-align: left;
      }

      &:nth-child(2n + 2) {
        text-align: right;
      }

      img {
        max-width: 120px;
        max-height: 60px;
      }
    }
  }
}