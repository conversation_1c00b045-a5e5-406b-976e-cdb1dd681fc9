import Image from 'next/image';
import styles from './PopularBrand.module.scss';

const PopularBrand = ({ brand }) => {

  // Use default values if brand data is not provided
  const brandName = brand?.name;
  const brandLogo = brand?.logo;
  const discountValue = brand?.highestOffer;
  const description = brand?.shortDescription;

  return (
    <div className={`border-grey ${styles.cardContainer} p-md-3 px-1`}>
      {/* Desktop Layout */}
      <div className={`row no-gutters d-none d-md-flex`}>
        <div className='col-5'>
          <Image
            src={brandLogo}
            alt={`${brandName} logo`}
            width={135}
            height={85}
            sizes="100vw"
            className={styles.brandImage}
            style={{ width: '135px', height: '80px'}}
          />
        </div>
        <div className='col-1'>
          <div className={styles.divider}></div>
        </div>
        <div className={`col-5 ${styles.textContainer} d-flex flex-column`}>
          <div className={styles.discountText}>{discountValue}</div>
          <div className={styles.description}>{description}</div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className={`d-flex d-md-none flex-column ${styles.mobileLayout}`}>
        <div className={styles.mobileImageContainer}>
          <Image
            src={brandLogo}
            alt={`${brandName} logo`}
            width={135}
            height={85}
            sizes="100vw"
            className={styles.brandImage}
            style={{ width: '135px', height: '80px'}}
          />
        </div>
        <div className={styles.mobileDivider}></div>
        <div className={`${styles.mobileTextContainer} justify-content-start`}>
          <div className={`${styles.discountText} mt-1`}>{discountValue}</div>
          <div className={`${styles.description}`}>{description}</div>
        </div>
      </div>
    </div>
  )
}

export default PopularBrand;