import styles from './MembershipOffer.module.scss';

const MembershipOffer = ({}) => {
  return (
    <>
      {' '}
      <div className={styles.membershipOfferContainer}>
        <div className={styles.membershipOfferContent}>
          <div>
            <img
              src="ribbon.png"
              className={styles.membershipOfferRibbonImage}
              alt="Ein türkisches Band mit der weißen Aufschrift „Sonderaktion"
            />
          </div>
          <h2 className="secondary-header--bold ">
            12-monatige Mitgliedschaft
          </h2>
          <p className="text-sm ">im Wert von 12 Euro</p>
          <h2 className="secondary-header--bold">Jetzt für nur 1 Euro*</h2>
          <br />
          <span className="text-s-membership">
            * Die Mitgliedschaft kostet im Rahmen der Sonderaktion einmalig 1
            EUR für die ersten 12 Monate. Danach kostet die Mitgliedschaft
            monatlich 1 EUR und ist immer zum Monatsende kündbar. Eine
            Mitgliedschaft zum Aktionspreis ist nur einmal pro Neukunde
            abschließbar.
          </span>
        </div>
      </div>
      <div style={{ clear: 'both' }}></div>
    </>
  );
};

export default MembershipOffer;