import React, { useState, useEffect } from "react";
import ReactPlayer from "react-player";
import { getLocalStorageItem } from "util/localStorageManagement";
import PlayVideoModal from "../PlayVideoModal/PlayVideoModal";

import styles from "./VideoBlock.module.scss";

export const getVideoType = (url) =>
  url?.includes("youtube.com") || url?.includes("youtu.be")
    ? "youtube"
    : "vimeo";

const vimeoUrlToEmbedFormat = (url) => {
  /* Tested with the folowing formats
  let formats = [
    'https://vimeo.com/209060279',
    'https://player.vimeo.com/video/209060279'
  ];
  */
  function vimeoIdParser(
    url,
    regExp = /^.*((\/\/vimeo.com\/)|(\/\/player.vimeo.com\/video\/))([^#&?]*).*/,
  ) {
    const match = decodeURIComponent(url).match(regExp);
    return (match && match[4]) || false;
  }
  return `https://player.vimeo.com/video/${vimeoIdParser(url)}`;
};

const youtubeUrlToEmbedFormat = (url) => {
  /* Tested with the folowing formats
  let formats = [
    'http://www.youtube.com/watch?v=-wtIMTCHWuI',
    'http://www.youtube.com/v/-wtIMTCHWuI?version=3&autohide=1',
    'http://youtu.be/-wtIMTCHWuI',
    'http://www.youtube.com/oembed?url=http%3A//www.youtube.com/watch?v%3D-wtIMTCHWuI&format=json',
    'http://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=-wtIMTCHWuI&format=json',
    'https://www.youtube.com/embed/-wtIMTCHWuI',
    'http://www.youtube.com/attribution_link?a=JdfC0C9V6ZI&u=%2Fwatch%3Fv%3DEhxJLojIE_o%26feature%3Dshare',
    'http://www.youtube.com/attribution_link?a=JdfC0C9V6ZI&u=/watch?v=EhxJLojIE_o&feature=share',
    'https://www.youtube.com/watch?v=vaqVwyYDWAk&ab_channel=Rammor'
  ];
  */
  function youtubeIdParser(
    url,
    regExp = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/,
  ) {
    var match = decodeURIComponent(url).match(regExp);
    return match && match[1].length == 11 ? match[1] : false;
  }

  return `https://www.youtube.com/embed/${youtubeIdParser(url) || url}`;
};

export const toValidEmbedVideoUrl = (url) => {
  switch (getVideoType(url)) {
    case "youtube":
      return youtubeUrlToEmbedFormat(url);
    case "vimeo":
      return vimeoUrlToEmbedFormat(url);
    default:
      return url;
  }
};

export const isVideoTypeAccepted = (videoUrl) => {
  // next line fixes the localStorage is undefined error
  if (typeof window !== "undefined") {
    switch (getVideoType(videoUrl)) {
      case "vimeo":
        return !!getLocalStorageItem("isVimeoPlayAccepted");
      case "youtube":
        return !!getLocalStorageItem("isYoutubePlayAccepted");
      default:
        return false;
    }
  } else return false;
};

export default function VideoBlock({ videoUrl, className = "", children }) {
  const [modal, setModal] = useState(false);
  const [play, setPlay] = useState(false);
  const videoType = getVideoType(videoUrl);
  const [thumbnail, setThumbnail] = useState("");

  if (!videoUrl) {
    return <>invalid videoUrl </>;
  }

  const playVideoOnAccept = (isAccepted) => {
    setModal(false);
    if (isAccepted) {
      setPlay(true);
    }
  };

  const toggleVideoModal = (e) => {
    if (!isVideoTypeAccepted(videoUrl)) {
      setModal(true);
    }
  };

  const extractVimeoId = (url) => {
    const match = url.match(/video\/(\d+)/);
    return match ? match[1] : null;
  };

  const fetchVimeoThumbnail = async (videoUrl) => {
    const videoId = extractVimeoId(videoUrl);
    if (!videoId) return "";

    const response = await fetch(
      `https://vimeo.com/api/v2/video/${videoId}.json`,
    );
    const data = await response.json();
    const thumbUrl = data[0].thumbnail_large; // Adjust this if you need a different size initially
    const modifiedUrl = thumbUrl.replace(/d_.*$/, "d_1200x600");
    return modifiedUrl;
  };

  const previewImage = async () => {
    if (!modal && !play && videoType === "vimeo") {
      const url = await fetchVimeoThumbnail(videoUrl);
      setThumbnail(url);
    }
  };

  useEffect(() => {
    previewImage();
  }, [videoType, videoUrl, modal, play]);

  return (
    <>
      <div className={`${styles.videoBlock} ${className}`}>
        {
          <ReactPlayer
            url={
              videoType === "vimeo"
                ? `${videoUrl}?autoplay=1&title=0&playsinline=1`
                : `${youtubeUrlToEmbedFormat(
                    videoUrl,
                  )}?enablejsapi=1&version=3&playerapiid=ytplayer&autoplay=1`
            }
            playing={isVideoTypeAccepted(videoUrl)}
            width="100%"
            height="100%"
            playIcon={
              <button className={styles.playBtn} onClick={toggleVideoModal}>
                <img
                  src="/icons/play-button.png"
                  alt="„Play-Symbol in weißem Kreis"
                />
              </button>
            }
            light={!modal && !play && Boolean(thumbnail.length) && thumbnail}
            config={{
              vimeo: {
                playerOptions: {
                  frameborder: "0",
                  controls: "1",
                },
              },
              youtube: {
                playerVars: {
                  frameborder: "0",
                },
              },
            }}
            className={styles.iframeWrapper}
          ></ReactPlayer>
        }
        {modal && (
          <PlayVideoModal
            videoType={videoType}
            playVideoOnAccept={playVideoOnAccept}
          />
        )}
        {children}
      </div>
    </>
  );
}