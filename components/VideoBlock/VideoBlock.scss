.VideoBlock {
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;

  .iframe-wrapper {
    position: relative;
    height: auto !important;
    padding-bottom: 53%;
    max-width: 1400px;
    @media (min-width: 992px) {
      margin-left: auto;
      margin-right: auto;
    }
    iframe {
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      max-height: 87vh;
      @media (max-width: 991.98px) {
        height: 100%;
      }
      @media (min-width: 992px) {
        max-height: 80%;
      }
    }
  }

  .react-player__preview {
    position: absolute;
    &:hover {
      opacity: 0.7;
    }
  }

  .play-btn {
    background: transparent;
    border: none;
    &:focus {
      outline: none;
    }
    > img {
      height: 200px;
      width: 200px;

      @media (min-width: 575px) and (max-width: 992px) {
        height: 100px;
        width: 100px;
      }

      @media (max-width: 574.98px) {
        height: 70px;
        width: 70px;
      }

      &:focus {
        outline: none;
      }
    }
  }
}
