@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.LandingPageMediaOutlets {
  $smallest-viewport-width: 430px;

  .main-header {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 16px;

    @include media-breakpoint-down(xs) {
      font-size: 14px;
      line-height: 16px;
      margin-bottom: 8px;
    }
  }

  .logos {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;

    .logo {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      img {
        max-width: 132px;
        max-height: 52px;

        @media screen and (max-width: 991px) {
          max-width: 88px;
          max-height: 34px;
        }

        // @media screen and (max-width: $smallest-viewport-width) {
        //   max-width: 50px;
        //   max-height: 28px;
        // }

        @media screen and (max-width: $smallest-viewport-width) {
          max-width: 50px;
          max-height: 28px;
        }
      }
    }
  }
}
