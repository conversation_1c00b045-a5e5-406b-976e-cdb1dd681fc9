import mediaOutlets from '../../model/landing-page-media-outlets.json';
import styles from './LandingPageMediaOutlets.module.scss';

export default function LandingPageMediaOutlets({ className }) {
  return (
    <div className={`${styles.landingPageMediaOutlets} ${className || ''}`}>
      <div className="container padding-side-25">
        <div className="row">
          <div className={styles.logos}>
            {mediaOutlets.map((outlet, index) => (
              <div className={styles.logo} key={index}>
                <img src={outlet.imageSrc} alt={outlet.altText} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}