@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');

.sideImageWrapPaymentError {
  position: relative;
  background: transparent url('/headers/CaptainCoupon_Lightbox_Payment.png')
    center center no-repeat;
  background-size: cover;
  margin-top: -16px;
  margin-bottom: -17px;
  margin-left: -1px;
  min-height: 713px;
  max-height: 100vh;
}

.sideContentWrapPaymentError {
  display: flex;
  align-content: center;
  flex-direction: row;
  flex-wrap: wrap;
}

.modalIcon {
  max-height: 85px;
  padding-bottom: 10px;
}

.modalDialog {
  :global(button.close) {
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 100;
  }
}

.paymentErrorModal {
  max-width: 1140px;

  .modalBody {
    margin-left: -1px;
    background-color: #81e9f0;
  }
}

.paymentErrorModalHeading {
  font-size: 45px;
  line-height: 50px;
  font-family: Montserrat;
  font-weight: 900;

  @include media-breakpoint-down(md) {
    font-size: 30px;
    line-height: 35px;
  }
}