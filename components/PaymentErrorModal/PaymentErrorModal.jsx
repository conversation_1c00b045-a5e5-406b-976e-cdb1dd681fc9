import { ModalGeneric } from 'components';
import { GrClose } from 'react-icons/gr';

import styles from './PaymentErrorModal.module.scss';

const PaymentErrorModal = ({ show, onClose }) => {
  return (
    <ModalGeneric modal={show} className={styles.paymentErrorModal}>
      <button className="close float-right" onClick={onClose}>
        <GrClose></GrClose>
      </button>
      <div className="row">
        <div className={`col-6 ${styles.sideImageWrapPaymentError} d-none d-md-block`}></div>
        <div className={`col-6 ${styles.sideContentWrapPaymentError} pad-tb-8 pad-l-8 pad-r-8`}>
          <h1 className={`text-left text-lg-left pad-t-3 pad-b-4 ${styles.paymentErrorModalHeading}`}>
            Etwas hat mit der Bezahlung nicht geklappt. Bitte versuch es erneut!
          </h1>
        </div>
      </div>
    </ModalGeneric>
  );
};

export default PaymentErrorModal;