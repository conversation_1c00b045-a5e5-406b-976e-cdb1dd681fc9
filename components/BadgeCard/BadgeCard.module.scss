@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.badgeCard {
  .registerCardTop {
    width: 50%;
  }

  @include media-breakpoint-down(sm) {
    .card {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-bottom: 0;
      margin-bottom: 12px;

      .cardImg {
        align-self: center;
        margin: 0;
        padding: 0;
        width: 110px;
      }

      .cardBody {
        padding: 0;

        .customCardSubtitle1 {
          flex: 1;
          margin: 0;
          margin-left: 32px;
          padding-right: 33px;
          font-size: 24px;
          line-height: 31px;
          text-align: left;
        }

        .cardText {
          display: none;
        }
      }
    }
  }

  @include media-breakpoint-down(xs) {
    .card {
      .cardImg {
        width: 55px;
      }

      .cardBody {
        .customCardSubtitle1 {
          margin-left: 16px;
          font-size: 15px;
          line-height: 18px;
        }
      }
    }
  }

  .customCardSubtitle1 {
    font-size: 25px;
    color: #000000;
    font-weight: 900;
    letter-spacing: 0px;
  }
}