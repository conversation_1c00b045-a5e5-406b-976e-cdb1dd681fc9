import { Col, CardImg, CardBody, CardTitle, CardText } from 'reactstrap';
import { Card } from 'components';
import styles from './BadgeCard.module.scss';

const BadgeCard = ({ badge }) => (
  <Col xs={12} sm={12} md={6} lg={4} xl={4}>
    <Card className={`${styles.badgeCard} row`}>
      <CardImg
        src={badge.imageSrc}
        alt="Card image cap"
        className={`${styles.registerCardTop} pt-md-1 pb-md-3`}
      />
      <CardBody className="pt-0 pb-0">
        <CardTitle className={styles.customCardSubtitle1}>
          {badge.title}
        </CardTitle>
        <CardText className={styles.cardText}>
          {badge.text}
        </CardText>
      </CardBody>
    </Card>
  </Col>
);

export default BadgeCard;
