@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.BadgeCard {
  .register-card-top {
    width: 50%;
  }

  @include media-breakpoint-down(sm) {
    .Card {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-bottom: 0;
      margin-bottom: 12px;

      .card-img {
        align-self: center;
        margin: 0;
        padding: 0;
        width: 110px;
      }

      .card-body {
        padding: 0;

        .custom-card-subtitle-1 {
          flex: 1;
          margin: 0;
          margin-left: 32px;
          padding-right: 33px;
          font-size: 24px;
          line-height: 31px;
          text-align: left;
        }

        .card-text {
          display: none;
        }
      }
    }
  }

  @include media-breakpoint-down(xs) {
    .Card {
      .card-img {
        width: 55px;
      }

      .card-body {
        .custom-card-subtitle-1 {
          margin-left: 16px;
          font-size: 15px;
          line-height: 18px;
        }
      }
    }
  }

  .custom-card-subtitle-1 {
    font-size: 25px;
    color: #000000;
    font-weight: 900;
    letter-spacing: 0px;
  }
}
