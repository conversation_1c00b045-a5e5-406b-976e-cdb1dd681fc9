import { useState, useEffect } from "react";
import styles from "./HeroBannerCard.module.scss";
import Link from "next/link";

const HeroBannerCard = ({ bannerData, brand }) => {
  const [isMobile, setIsMobile] = useState(false);


  // Check if we're on client-side before accessing window
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const checkMobile = () => {
        setIsMobile(window.innerWidth <= 768);
      };

      // Initial check
      checkMobile();

      // Add event listener for window resize
      window.addEventListener('resize', checkMobile);

      // Cleanup
      return () => window.removeEventListener('resize', checkMobile);
    }
  }, []);

  // Use banner data if available, otherwise use default values
  // Prefer compressed banners if available, otherwise use regular banners
  const desktopBanner = bannerData?.compressedDesktopBanner || bannerData.desktopBanner;
  const mobileBanner = bannerData?.compressedMobileBanner || bannerData?.mobileBanner || desktopBanner;
  const brandLogo = bannerData?.brandLogo || brand?.logo;
  const brandName = bannerData?.brandName || brand?.name;
  const discountValue = bannerData?.highestOffer || brand?.highestOffer;
  const description = bannerData?.brandShortDescription || brand?.brandShortDescription;
  const link = bannerData?.link || (brand?.slug ? `/brand/${brand.slug}` : "#");

  // Choose banner based on device size
  const bannerSrc = isMobile ? mobileBanner : desktopBanner;

  return (
    <>
      <div className={styles.imageContainer}>
        <Link href={link}>
          <img
            src={bannerSrc}
            alt={`${brandName} banner image`}
            width={1200}
            height={800}
            style={{ width: '100%', height: isMobile ? '195px' : '340px', borderRadius: '10px' }}
          />
        </Link>
        <div className={styles.discountBadge}>{discountValue}</div>
      </div>
      <div className="row px-4 py-1 py-md-2">
        <div className="col-4 pl-md-4">
          <div className={styles.logoContainer}>
            <img
              src={brandLogo}
              alt={`${brandName} logo`}
              width={100}
              height={100}
              className={styles.logo}
            />
          </div>
        </div>
        <div className="col-8 pl-1">
          <div className={styles.description}>
            {description}
          </div>
        </div>
      </div>
    </>
  );
};

export default HeroBannerCard;
