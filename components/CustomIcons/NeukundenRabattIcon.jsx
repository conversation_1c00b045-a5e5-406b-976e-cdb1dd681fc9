import * as React from 'react';

function NeukundenRabattIcon(props) {
  return (
    <div>
      <svg data-name="Ebene 31" viewBox="0 0 218.658 187.03" {...props}>
      <path
        data-name="Pfad 239"
        d="M.049 119.277s-2.8-.677 34.153-31.147S139.167.012 139.167.012L218.657 0 .06 187.03l-.01-56.268z"
      />
      <text
        transform="rotate(-40 205.36 42.298)"
        style={{
          isolation: 'isolate',
        }}
        fontSize={20}
        fill="#fff"
        fontFamily="Montserrat-ExtraBold,Montserrat"
        fontWeight={800}
      >
        {'Neu'}
        <tspan x={42.8} y={0} letterSpacing="-.01em">
          {'k'}
        </tspan>
        <tspan x={56.319} y={0}>
          {'u'}
        </tspan>
        <tspan x={70.159} y={0} letterSpacing=".008em">
          {'n'}
        </tspan>
        <tspan x={84.239} y={0}>
          {'den'}
        </tspan>
        <tspan x={124.959} y={0} letterSpacing="-.006em">
          {'r'}
        </tspan>
        <tspan x={133.699} y={0}>
          {'a'}
        </tspan>
        <tspan x={146.259} y={0} letterSpacing="-.012em">
          {'b'}
        </tspan>
        <tspan x={159.919} y={0}>
          {'a'}
        </tspan>
        <tspan x={172.478} y={0} letterSpacing="-.011em">
          {'t'}
        </tspan>
        <tspan x={181.199} y={0}>
          {'t'}
        </tspan>
      </text>
      </svg>
      <div className='card-text'>
        <p>Neukundenrabatt</p>
      </div>
    </div>
  );
}

export default NeukundenRabattIcon;
