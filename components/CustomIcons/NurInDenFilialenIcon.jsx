import * as React from 'react';

function NurInDenFilialenIcon(props) {
  return (
    <div>
      <svg data-name="Ebene 32" viewBox="0 0 218.658 187.03" {...props}>
      <path
        data-name="Pfad 241"
        d="M.049 119.277s-2.8-.677 34.153-31.147S139.167.012 139.167.012L218.657 0 .06 187.03l-.01-56.268z"
      />
      <text
        transform="rotate(-40 206.484 45.389)"
        style={{
          isolation: 'isolate',
        }}
        fontSize={20}
        fill="#fff"
        fontFamily="Montserrat-ExtraBold,Montserrat"
        fontWeight={800}
        data-name="Nur in den Filialen"
      >
        {'Nur in den '}
        <tspan x={117.178} y={0} letterSpacing="-.01em">
          {'F'}
        </tspan>
        <tspan x={129.818} y={0}>
          {'ilialen'}
        </tspan>
      </text>
      </svg>
      <div className='card-text'>
        <p>Nur in den Filialen</p>
      </div>
    </div>
  );
}

export default NurInDenFilialenIcon;
