import styles from './ModalDialog.module.scss';

const ModalDialog = ({
  imgSrc,
  title,
  description,
  nextButtonLabel,
  onNext,
}) => (
  <div className="row">
    <div
      className={`col-sm-6 col-12 ${styles.sideImageWrap} d-none d-sm-block`}
      style={{
        backgroundImage: `url('${imgSrc}')`,
      }}
    ></div>
    <div className="col-sm-6 col-12 pad-sm-tb-10 pad-tb-4 pad-l-4 pad-r-4 pad-md-l-8 pad-md-r-8">
      <h1
        className={`text-left text-lg-left pad-md-t-4 pad-b-4 ${styles.dialogHeading}`}
        data-cy="dialogTitle"
      >
        {title}
      </h1>
      <div className="pad-b-6">{description}</div>
      <button
        onClick={onNext}
        className={`btn btn-info ${styles.btnInfoBtnTall}`}
        data-cy="subscribe"
      >
        {nextButtonLabel}
      </button>
    </div>
  </div>
);

export default ModalDialog;