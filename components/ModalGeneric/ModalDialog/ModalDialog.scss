@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');

.sideImageWrap {
  position: relative;
  background-color: transparent;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  margin-top: -16px;
  margin-bottom: -17px;
  margin-left: -1px;
}

.modal-body {
  padding: 1rem;
  margin-left: -1px;
}

.dialog-heading {
  font-size: 40px;
  line-height: 43px;
  font-family: Montserrat;
  font-weight: 900;
}

@include media-breakpoint-down(md) {
  .dialog-heading {
    font-size: 30px;
    line-height: 35px;
  }
}

.btn-info.btn-tall {
  height: fit-content;
}
