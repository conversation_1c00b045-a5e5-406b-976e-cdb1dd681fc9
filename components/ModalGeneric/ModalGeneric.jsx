import styles from './ModalGeneric.module.scss';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, ModalBody } from 'reactstrap';
import { GrClose } from 'react-icons/gr';

import React from 'react';

const ModalGeneric = ({
  className,
  children,
  okBtn,
  cancelBtn,
  title,
  modal,
  centered = false,
  displayCloseButton = false,
  onClose = () => {},
  ...props
}) => {
  return (
    <Modal
      isOpen={modal}
      className={className}
      centered={centered}
      backdrop="static"
      {...props}
    >
      {displayCloseButton && (
        <button className="close float-right" onClick={onClose}>
          <GrClose />
        </button>
      )}

      {title && <ModalHeader>{title}</ModalHeader>}
      <ModalBody>
        {children}
        {okBtn}
        {cancelBtn}
      </ModalBody>
    </Modal>
  );
};

export default ModalGeneric;