import styles from '../../styles/sass/_section.module.scss';

const Section = ({
  background,
  className = '',
  children,
  rowClass = '',
  containerClass = '',
  customPadding = false,
  kategorienPageSection,
  ...props
}) => {
  return (
    <section
      style={{ background: background }}
      className={`${styles.section} ${className} row`}
    >
      <div
        className={`${containerClass ? containerClass : 'container'} ${
          customPadding ? styles.paddingSide25 : ''
        }`}
      >
        <div className={`row ${rowClass}`}>{children}</div>
      </div>
    </section>
  );
};

export default Section;