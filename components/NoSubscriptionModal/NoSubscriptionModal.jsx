import { useState, useContext } from 'react';

import {
  ModalGeneric,
  ModalDialog,
  SubscriptionPaymentDialog,
  PaymentErrorModal,
} from 'components';

import * as ConsumerService from 'services/consumer.service';
import MainAppContext from 'context/mainAppContext';

import styles from './NoSubscriptionModal.module.scss';

const NoSubscriptionModal = () => {
  const { showSubscribeModal, setShowSubscribeModal } =
    useContext(MainAppContext);
  const [subscriptionModal, setSubscriptionModal] = useState(false);
  const [userDetails, setUserDetails] = useState(null);
  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);

  const onPaymentError = () => {
    setShowPaymentErrorModal(true);
  };

  return (
    <>
      <ModalGeneric
        modal={showSubscribeModal}
        displayCloseButton={true}
        onClose={() => setShowSubscribeModal(false)}
        className={styles.noSubModal}
      >
        <ModalDialog
          imgSrc="/headers/CaptainCoupon_Lightbox_Email.jpg"
          title="Fast geschafft!"
          description="Dein Konto wurde erfolgreich erstellt. Wähle jetzt nur noch deine
          passende Mitgliedschaft, um dir großartige und immer gültige
          Gutscheincodes zu sichern!"
          nextButtonLabel="Mitgliedschaft starten"
          onNext={() =>
            ConsumerService.getConsumer().then((response) => {
              setUserDetails(response.data);
              setShowSubscribeModal(false);
              setSubscriptionModal(true);
            })
          }
        />
      </ModalGeneric>

      <ModalGeneric
        modal={subscriptionModal}
        title="Kasse"
        className="cancel-sub-modal"
        customPadding={true}
        displayCloseButton={true}
        onClose={() => setSubscriptionModal(false)}
      >
        <SubscriptionPaymentDialog
          consumer={userDetails}
          onSuccess={() => {
            location.reload();
          }}
          onPaymentError={onPaymentError}
          onClose={() => setSubscriptionModal(false)}
        />
      </ModalGeneric>

      <PaymentErrorModal
        show={showPaymentErrorModal}
        onClose={() => setShowPaymentErrorModal(false)}
      />
    </>
  );
};
export default NoSubscriptionModal;