import './Stepper.module.scss';
import { Steps } from 'components';

export default function Stepper({ steps, currentStep }) {
  return (
    <div className="row">
      <div className="col mar-t-4">
        <Steps>
          {steps?.length &&
            steps.map((stepLabel, index) => {
              return (
                <Steps.Step
                  active={currentStep === index}
                  key={`${index}-${stepLabel}`}
                >
                  <Steps.Number>{index + 1}</Steps.Number>
                  <Steps.Label>{stepLabel}</Steps.Label>
                </Steps.Step>
              );
            })}
        </Steps>
      </div>
    </div>
  );
}
