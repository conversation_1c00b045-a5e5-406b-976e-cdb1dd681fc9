import React, { useState } from 'react';
import {
  Carousel,
  CarouselControl,
  CarouselIndicators,
  CarouselItem,
} from 'reactstrap';
import HeroBannerCard from '../HeroBannerCard/HeroBannerCard';

import styles from './HeroBannerCarousel.module.scss';

const HeroBannerCarousel = ({ bannerSlider }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [animating, setAnimating] = useState(false);

  // Extract configuration and banners from the banner slider
  const enableAutoSlide = bannerSlider?.enableAutoSlide !== undefined ? bannerSlider.enableAutoSlide : true;
  const autoSlideTime = bannerSlider?.autoSlideTime || 4000;
  const banners = bannerSlider?.banners || [];

  // Use banners only, no fallback
  const slidesData = banners;

  // Indicators are not needed as per requirements
  const showIndicator = false;

  const next = () => {
    if (animating) return;
    const nextIndex =
      activeIndex === slidesData.length - 1 ? 0 : activeIndex + 1;
    setActiveIndex(nextIndex);
  };

  const previous = () => {
    if (animating) return;
    const nextIndex =
      activeIndex === 0 ? slidesData.length - 1 : activeIndex - 1;
    setActiveIndex(nextIndex);
  };

  const goToIndex = (newIndex) => {
    if (animating) return;
    setActiveIndex(newIndex);
  };

  // If there are no banners, return null
  if (!Array.isArray(slidesData) || slidesData.length === 0) {
    return null;
  }

  const slides = slidesData.map((item, index) => {
    // All items are banners from the API
    return (
      <CarouselItem
        onExiting={() => setAnimating(true)}
        onExited={() => setAnimating(false)}
        className={styles.brandCarouselItem}
        key={index}
      >
        <div className={`card ${styles.heroBannerCard}`}>
          <HeroBannerCard bannerData={item} brand={null} />
        </div>
      </CarouselItem>
    );
  });

  return (
    <Carousel
      activeIndex={activeIndex}
      next={next}
      previous={previous}
      className={`${styles.brandCarousel} col-12 px-1 px-md-3`}
      interval={enableAutoSlide ? autoSlideTime : 0}
    >
      {showIndicator && (
        <CarouselIndicators
          items={slidesData}
          activeIndex={activeIndex}
          onClickHandler={goToIndex}
        />
      )}
      {slides}
      <CarouselControl
        direction="prev"
        directionText="Previous"
        onClickHandler={previous}
        cssModule={{ color: '#000' }}
      />
      <CarouselControl
        direction="next"
        directionText="Next"
        onClickHandler={next}
      />
    </Carousel>
  );
};

export default HeroBannerCarousel;
