.UtilitiesMenu {
  margin: 0 15px;

  &__link {
    font-size: 13px;
    white-space: nowrap;
    padding: 10px 5px;
    text-decoration: none;
    color: #000;
    &:hover {
      text-decoration: none;
      color: #000;
      font-weight: bold;
      cursor: pointer;
    }
    &.active-link {
      font-weight: bold;
      &.font-normal {
        font-weight: 500;
      }
    }
  }
  .desktop-only-link {
    display: block;
  }
  .mobile-only-link {
    display: none !important;
  }
  @media (max-width: 991.98px) {
    .dropdown > .UtilitiesMenu__link {
      border-top: 1px solid #fff;
    }
    .desktop-only-link {
      display: none;
    }
    .mobile-only-link {
      display: block !important;
    }
  }

  .dropdown-menu {
    .UtilitiesMenu__link {
      display: block;
      color: #fff;
      border-bottom: 1px solid #fff;
      margin: 0px 12px;
      padding: 4px 0px 6px 0px;
      text-overflow: ellipsis;
      overflow: hidden;
      &:last-child {
        border-bottom: 0;
      }
    }
  }
  .dropdown-menu.show {
    right: 0;
    left: auto !important; // this is a  hack for boottstrap
  }
}
