.utilitiesMenu {
  margin: 0 15px;

  &__link {
    font-size: 13px;
    white-space: nowrap;
    padding: 10px 5px;
    text-decoration: none;
    color: #000;
    &:hover {
      text-decoration: none;
      color: #000;
      font-weight: bold;
      cursor: pointer;
    }
    &.activeLink {
      font-weight: bold;
      &.fontNormal {
        font-weight: 500;
      }
    }
  }
  .desktopOnlyLink {
    display: block;
  }
  .mobileOnlyLink {
    display: none !important;
  }
  @media (max-width: 991.98px) {
    .dropdown > .utilitiesMenu__link {
      border-top: 1px solid #fff;
    }
    .desktopOnlyLink {
      display: none;
    }
    .mobileOnlyLink {
      display: block !important;
    }
  }

  .dropdownMenu {
    .utilitiesMenu__link {
      display: block;
      color: #fff;
      border-bottom: 1px solid #fff;
      margin: 0px 12px;
      padding: 4px 0px 6px 0px;
      text-overflow: ellipsis;
      overflow: hidden;
      &:last-child {
        border-bottom: 0;
      }
    }
  }
  .dropdownMenuShow {
    right: 0;
    left: auto !important; // this is a hack for bootstrap
  }
}