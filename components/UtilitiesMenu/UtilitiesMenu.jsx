import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { Dropdown, DropdownMenu, DropdownToggle } from 'reactstrap';
import { useSelector } from 'react-redux';
import ArrowDownIcon from '../CustomIcons/ArrowDownIcon';
import { isAuth } from 'services/auth.service';
import ActiveLink from '../ActiveLink/ActiveLink';

import styles from './UtilitiesMenu.module.scss';

const UtilitiesMenu = (props) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const toggle = () => setDropdownOpen((prevState) => !prevState);
  const { asPath } = useRouter();
  const { useCustomTheme } = useSelector((state) => state.settings);

  return (
    <nav className={`${styles.utilitiesMenu} ${props.className}`}>
      <div className="UtilitiesMenu__menu-item">
        {isAuth() && (
          <Dropdown
            isOpen={dropdownOpen}
            toggle={toggle}
            onMouseOver={() => setDropdownOpen(true)}
            onMouseLeave={() => setDropdownOpen(false)}
            onClick={toggle}
          >
            <DropdownToggle tag="span" data-cy="myAccountLink">
              <>
                <span className={styles.desktopOnlyLink}>
                  <ActiveLink 
                    href="/settings" 
                    asPath={asPath} 
                    as="settings"
                    className={`${styles.utilitiesMenu__link} ${styles.fontNormal}`}
                    title="Mein Account"
                  >
                    Mein Account{' '}
                  </ActiveLink>
                </span>
                <span className={`${styles.mobileOnlyLink} ${styles.utilitiesMenu__link}`}>
                  {' '}
                  Mein Account{' '}
                </span>
                <ArrowDownIcon
                  fill="#fff"
                  className={'dropdownExpander d-lg-none d-sm-block'}
                ></ArrowDownIcon>
              </>
            </DropdownToggle>
            <DropdownMenu className={`my-account-dropdown ${styles.dropdownMenu}`}>
              <Link 
                href="/settings"
                className={styles.utilitiesMenu__link}
                data-cy="userSettingsPage"
              >
                Einstellungen
              </Link>
              <Link 
                href="/user_merkliste"
                className={styles.utilitiesMenu__link}
              >
                Merkliste
              </Link>
              {!useCustomTheme && (
                <Link 
                  href="/so_funktioniert"
                  className={styles.utilitiesMenu__link}
                >
                  So funktioniert's
                </Link>
              )}
            </DropdownMenu>
          </Dropdown>
        )}
      </div>
    </nav>
  );
};

export default UtilitiesMenu;