@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
.navigation {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .hamburgerIcon {
    height: 35px;
  }

  .dropdownExpander {
    display: none;
  }

  &__menuPosition {
    position: relative;
  }

  &__menu {
    padding: 0;
    margin: 0px;
    display: flex;
    width: 100%;
    justify-content: flex-start;
  }

  &__menuItem {
    padding: 0;
    margin: 0;
    list-style: none;
    white-space: nowrap;
    margin: 0 0.75rem;

    @media (min-width: 992px) and (max-width: 1199.98px) {
      margin: 0 0.25rem;
    }

    &:first-of-type {
      margin-left: 0;
    }
    &:last-of-type {
      margin-right: 0;
    }

    :global(.dropdown-menu.show) {
      background-color: white;
      transform: none !important;
      border: 0.5px solid #707070;
      top: inherit !important;
      border-radius: 0 15px 15px 15px;
      padding: 10px;
      margin-top: -1px;
      z-index: 0;
    }
  }

  &__menuItemSoFunktionierts {
    display: none;
    @include media-breakpoint-down(md) {
      display: block;
    }
  }

  &__link {
    color: #000;
    font-size: 13px;
    padding: 5px 0;
    text-decoration: none;
    // &:hover {
    //   text-decoration: none;
    //   color: #000;
    //   font-weight: bold;
    //   cursor: pointer;
    //   background-color: #fff;
    //   border-radius: 15px 15px 0 0;
    //   border: 0.5px solid #707070;
    //   border-bottom: 0;
    // }

    &.activeLink {
      font-weight: bold;
    }
    &:before {
      display: block;
      content: attr(title);
      font-weight: bold;
      height: 0;
      overflow: hidden;
      visibility: hidden;
    }
  }

  .dropdown-menu {
    max-width: 300px;
    top: 14px;
    overflow-y: auto;
    &.show {
      transform: none !important;
      top: 25px !important;
    }
    .navigation__link {
      display: block;
      color: #fff;
      border-bottom: 1px solid #fff;
      margin: 0px 12px 0px 12px;
      padding-left: 0px !important;

      padding: 4px 0px 6px 0px;
      text-overflow: ellipsis;
      overflow: hidden;
      &:last-child {
        border-bottom: 0;
      }
    }
  }

  .dropdown.show .dropdown-menu.show {
    width: 222px;
  }

  .UtilitiesMenu .dropdown {
    .UtilitiesMenu__link {
      color: #fff;
      display: block;
      margin: 0 -4px 0px -5px;
      font-size: 13px;
      padding: 5px 0;
    }
    &.show {
      .dropdown-menu.show {
        display: block;
        margin-left: -4px;
        width: 220px;
        border: none;
        padding: 8px 15px 4px 15px;

        .UtilitiesMenu__link {
          margin: 0;
        }
      }
    }
  }
}

@include media-breakpoint-down(md) {
  .navigation {
    padding-left: 10px;

    .dropdownExpander {
      float: right;
      margin-top: 4px;
      fill: #fff;
      font-size: 18px;
    }

    // Hide the regular menu on mobile since we use overlay
    &__menu {
      display: none !important; // Completely hide desktop menu on mobile
    }

    .dropdown-menu.show {
      display: none; // Hide dropdown on mobile, use overlay instead
    }
  }
}

@media (min-width: 992px) {
  .myAccountDropdown.dropdown-menu {
    margin-top: -22px;
  }
}

.gutscheincodeLink {
  max-width: 8rem;
  display: block;
  position: relative;
  width: 8rem;
  text-align: center;
  font-weight: 500;
  z-index: 1;
}

.dropdownOpen {
  text-decoration: none !important;
  color: #000;
  font-weight: bold;
  cursor: pointer;
  background-color: #fff;
  border-radius: 15px 15px 0 0;
  border: 0.5px solid #707070;
  border-bottom: 0 !important;
}

/* Mobile Menu Overlay Styles */
.mobileMenuOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 9999;
  display: flex;
  flex-direction: column;

  @media (min-width: 768px) {
    display: none;
  }
}

.mobileMenuHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background-color: #fff;
  min-height: 60px;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.mobileMenuLogo {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;

  img {
    max-width: 150px;
    height: auto;
    display: block;
  }
}

.mobileMenuClose {
  background: none;
  border: none;
  font-size: 24px;
  color: #000;
  cursor: pointer;
  padding: 5px;
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);

  svg {
    width: 24px;
    height: 24px;
  }
}

.mobileMenuLogin {
  background: #fff;
  border: 1px solid #333;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 800;
  color: #333;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);

  &:hover {
    background: #f8f9fa;
  }
}

.mobileMenuContent {
  flex: 1;
  padding: 0;
  overflow-y: auto;
}

.mobileMenuTabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
  padding: 0 10px;
}

.mobileMenuTab {
  padding: 15px 20px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;

  &.active {
    color: #000;
    font-weight: bold;
    border-bottom-color: #000;
  }
}

.mobileMenuList {
  padding: 10px;
}

.mobileMenuItem {
  display: flex;
  align-items: center;
  padding: 1px;
  margin-bottom: 8px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
  }
}

.mobileMenuIcon {
  width: 56px;
  height: 62px;
  background-color: rgba(173, 245, 249, 0.7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;

  img {
    max-width: 40px;
    max-height: 40px;
    object-fit: contain;
  }

  span {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
}

