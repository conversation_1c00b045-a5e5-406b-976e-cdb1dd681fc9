import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Dropdown, DropdownMenu, DropdownToggle } from 'reactstrap';
import ActiveLink from '../ActiveLink/ActiveLink';
import HamburgerMenuIcon from '../CustomIcons/HamburgerMenuIcon';
import XIcon from '../CustomIcons/XIcon';
import UtilitiesMenu from '../UtilitiesMenu/UtilitiesMenu';
import styles from './Navigation.module.scss';
import { MegaDropDown } from '../../components'

const Navigation = ({
  className = '',
  categories,
  showSoFunktioniertsForNonCustomer,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('kategorien');

  const { useCustomTheme } = useSelector((state) => state.settings);

  const { asPath } = useRouter();
  const closeMenu = (event) => {
    if (!event.target.closest(`.${styles.navigation}`) && !event.target.closest(`.${styles.mobileMenuOverlay}`)) {
      setMobileOpen(false);
    }
  };
  useEffect(() => {
    window.addEventListener('click', closeMenu);
    return () => {
      window.removeEventListener('click', closeMenu);
    };
  }, []);
  // Mobile menu data
  const mobileCategories = [
    { title: 'Essen & Trinken', href: '/kategorie/essen-trinken', icon: '/icons/food.svg' },
    { title: 'Mode & Schmuck', href: '/kategorie/mode-schmuck', icon: '/icons/fashion.svg' },
    { title: 'Haus & Wohnen', href: '/kategorie/haus-wohnen', icon: '/icons/home.svg' },
    { title: 'Gesundheit', href: '/kategorie/gesundheit', icon: '/icons/health.svg' },
    { title: 'Unterhaltung & Bildung', href: '/kategorie/unterhaltung-bildung', icon: '/icons/entertainment.svg' },
    { title: 'Technik & Mobilfunk', href: '/kategorie/technik-mobilfunk', icon: '/icons/tech.svg' },
    { title: 'Finanzen & Versicherung', href: '/kategorie/finanzen-versicherung', icon: '/icons/finance.svg' },
    { title: 'Geschenke & Blumen', href: '/kategorie/geschenke-blumen', icon: '/icons/gifts.svg' },
    { title: 'Tierbedarf', href: '/kategorie/tierbedarf', icon: '/icons/pets.svg' },
    { title: 'Freizeit & Hobbys', href: '/kategorie/freizeit-hobbys', icon: '/icons/hobbies.svg' },
  ];

  const mobileCoupons = [
    { title: '25% von Bettwarenshop.de', href: '/rabatt/bettwarenshop', logo: '/logos/bettwarenshop.png' },
    { title: '22% von SNOCKS', href: '/rabatt/snocks', logo: '/logos/snocks.png' },
    { title: '50€ von Outfittery', href: '/rabatt/outfittery', logo: '/logos/outfittery.png' },
    { title: '25% von OCEANSAPART', href: '/rabatt/oceansapart', logo: '/logos/oceansapart.png' },
    { title: '125€ von HelloFresh', href: '/rabatt/hellofresh', logo: '/logos/hellofresh.png' },
    { title: '20€ von Purelei', href: '/rabatt/purelei', logo: '/logos/purelei.png' },
    { title: '15% von Zalando', href: '/rabatt/zalando', logo: '/logos/zalando.png' },
    { title: '30% von H&M', href: '/rabatt/hm', logo: '/logos/hm.png' },
  ];

  const mobileBrands = [
    { title: 'Amazon', href: '/marke/amazon' },
    { title: 'Zalando', href: '/marke/zalando' },
    { title: 'Otto', href: '/marke/otto' },
    { title: 'About You', href: '/marke/about-you' },
    { title: 'H&M', href: '/marke/hm' },
    { title: 'Nike', href: '/marke/nike' },
    { title: 'Adidas', href: '/marke/adidas' },
    { title: 'MediaMarkt', href: '/marke/mediamarkt' },
  ];

  return (
    <>
      <nav className={`${styles.navigation} ${className}`}>
        <HamburgerMenuIcon
          className={`${styles.hamburgerIcon} d-lg-none d-sm-block`}
          onClick={() => setMobileOpen(!mobileOpen)}
        />
        <div className={styles.navigation__menuPosition}>
          <ul className={`${styles.navigation__menu}`}>
            <li className={styles.navigation__menuItem}>
              <Dropdown
                isOpen={dropdownOpen}
                toggle={() => setDropdownOpen(!dropdownOpen)}
                onMouseOver={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
                onClick={() => setDropdownOpen(!dropdownOpen)}
                className={dropdownOpen ? styles.dropdownOpen: ''}
              >
                <DropdownToggle tag="span">
                  <>
                    <ActiveLink
                      href="/rabatte"
                      asPath={asPath}
                      className={`${styles.navigation__link} ${styles.gutscheincodeLink}`}
                      title="Gutscheincodes"
                    >
                      Gutscheincodes
                    </ActiveLink>
                    <div></div>
                  </>
                </DropdownToggle>
                <DropdownMenu className='container' persist={true} style={{ transform: "none", left: "-1rem", width: "max-content" }}>
                  <MegaDropDown></MegaDropDown>
                </DropdownMenu>
              </Dropdown>
            </li>
            {showSoFunktioniertsForNonCustomer && !useCustomTheme && (
              <li className={`${styles.navigation__menuItem} ${styles.navigation__menuItemSoFunktionierts}`}>
                <ActiveLink
                  href="/so_funktioniert"
                  asPath={asPath}
                  className={styles.navigation__link}
                  title="So funktioniert's"
                >
                  So funktioniert's
                </ActiveLink>
              </li>
            )}
            <UtilitiesMenu className="d-lg-none d-sm-block"></UtilitiesMenu>
          </ul>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {mobileOpen && (
        <div className={styles.mobileMenuOverlay} onClick={(e) => e.stopPropagation()}>
          <div className={styles.mobileMenuHeader}>
            <button
              className={styles.mobileMenuClose}
              onClick={() => setMobileOpen(false)}
            >
              <XIcon />
            </button>
            <div className={styles.mobileMenuLogo}>
              <img src="/CaptainCoupon_logo.svg" alt="Captain Coupon" />
            </div>
            <button
              className={styles.mobileMenuLogin}
              onClick={(e) => {
                e.stopPropagation();
                // Trigger the desktop login button click to use the same modal
                const desktopLoginBtn = document.querySelector('[data-cy="mainLoginBtn"]');
                if (desktopLoginBtn) {
                  desktopLoginBtn.click();
                }
                setMobileOpen(false); // Close mobile menu when login modal opens
              }}
            >
              Login
            </button>
          </div>

          <div className={styles.mobileMenuContent}>
            <div className={styles.mobileMenuTabs}>
              <div
                className={`${styles.mobileMenuTab} ${activeTab === 'kategorien' ? styles.active : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  setActiveTab('kategorien');
                }}
              >
                Kategorien
              </div>
              <div
                className={`${styles.mobileMenuTab} ${activeTab === 'coupons' ? styles.active : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  setActiveTab('coupons');
                }}
              >
                Coupons
              </div>
              <div
                className={`${styles.mobileMenuTab} ${activeTab === 'marken' ? styles.active : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  setActiveTab('marken');
                }}
              >
                Marken
              </div>
            </div>

            <div className={styles.mobileMenuList}>
              {activeTab === 'kategorien' && mobileCategories.map((category, index) => (
                <ActiveLink
                  key={index}
                  href={category.href}
                  asPath={asPath}
                  className={styles.mobileMenuItem}
                  onClick={() => setMobileOpen(false)}
                >
                  <div className={styles.mobileMenuIcon}>
                  </div>
                  <span>{category.title}</span>
                </ActiveLink>
              ))}

              {activeTab === 'coupons' && mobileCoupons.map((coupon, index) => (
                <ActiveLink
                  key={index}
                  href={coupon.href}
                  asPath={asPath}
                  className={styles.mobileMenuItem}
                  onClick={() => setMobileOpen(false)}
                >
                  <div className={styles.mobileMenuIcon}>
                    {coupon.logo ? (
                      <img src={coupon.logo} alt={`${coupon.title} logo`} />
                    ) : (
                      <span>%</span>
                    )}
                  </div>
                  <span>{coupon.title}</span>
                </ActiveLink>
              ))}

              {activeTab === 'marken' && mobileBrands.map((brand, index) => (
                <ActiveLink
                  key={index}
                  href={brand.href}
                  asPath={asPath}
                  className={styles.mobileMenuItem}
                  onClick={() => setMobileOpen(false)}
                >
                  <div className={styles.mobileMenuIcon}>
                    <span>★</span>
                  </div>
                  <span>{brand.title}</span>
                </ActiveLink>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Navigation;