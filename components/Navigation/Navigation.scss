@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
.Navigation {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .hamburgerIcon {
    height: 35px;
  }

  .dropdownExpander {
    display: none;
  }

  &__menu_position {
    position: relative;
  }

  &__menu {
    padding: 0;
    margin: 0px;
    display: flex;
    width: 100%;
    justify-content: flex-start;
  }

  &__menu-item {
    padding: 0;
    margin: 0;
    list-style: none;
    white-space: nowrap;
    margin: 0 0.75rem;

    @media (min-width: 992px) and (max-width: 1199.98px) {
      margin: 0 0.25rem;
    }

    &:first-of-type {
      margin-left: 0;
    }
    &:last-of-type {
      margin-right: 0;
    }
  }

  &__menu-item-so_funktionierts {
    display: none;
    @include media-breakpoint-down(md) {
      display: block;
    }
  }

  &__link {
    color: #000;
    font-size: 13px;
    padding: 10px 0;
    text-decoration: none;
    &:hover {
      text-decoration: none;
      color: #000;
      font-weight: bold;
      cursor: pointer;
    }

    &.active-link {
      font-weight: bold;
    }
    &:before {
      display: block;
      content: attr(title);
      font-weight: bold;
      height: 0;
      overflow: hidden;
      visibility: hidden;
    }
  }

  .dropdown-menu {
    max-width: 300px;
    top: 14px;
    overflow-y: auto;
    &.show {
      transform: none !important;
      top: 25px !important;
    }
    .Navigation__link {
      display: block;
      color: #fff;
      border-bottom: 1px solid #fff;
      margin: 0px 12px 0px 12px;
      padding-left: 0px !important;

      padding: 4px 0px 6px 0px;
      text-overflow: ellipsis;
      overflow: hidden;
      &:last-child {
        border-bottom: 0;
      }
    }
  }

  .dropdown.show .dropdown-menu.show {
    width: 222px;
  }

  .UtilitiesMenu .dropdown {
    .UtilitiesMenu__link {
      color: #fff;
      display: block;
      margin: 0 -4px 0px -5px;
      font-size: 13px;
      padding: 5px 0;
    }
    &.show {
      .dropdown-menu.show {
        display: block;
        margin-left: -4px;
        width: 220px;
        border: none;
        padding: 8px 15px 4px 15px;

        .UtilitiesMenu__link {
          margin: 0;
        }
      }
    }
  }
}

@include media-breakpoint-down(md) {
  .Navigation {
    $parent: &;

    .dropdownExpander {
      float: right;
      margin-top: 4px;
      fill: #fff;
      font-size: 18px;
    }

    &__menu {
      display: none;
      position: absolute;
      left: 0;
      top: 0;
      width: auto;

      &.mobileOpen {
        padding: 5px 0;
        margin: 0;
        display: block;
        background-color: #000;

        #{$parent + '__menu-item'} {
          display: block;
          border-bottom: 1px solid #fff;
          margin: 0 10px;
          padding: 5px 0;
          min-width: 200px;
          &:last-child {
            border-bottom: 0;
          }
        }

        #{$parent + '__link'} {
          color: #fff;
          padding: 0;
        }
      }
    }

    .dropdown-menu.show {
      transform: none !important;
      background-color: #000;
      left: -11px !important;

      .Navigation__link {
        padding: 4px;
      }
    }
  }
}

@media (min-width: 992px) {
  .my-account-dropdown.dropdown-menu {
    margin-top: -22px;
  }
}
