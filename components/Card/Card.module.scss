@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

@import '../../styles/sass/variables';

.card {
  position: relative;
  margin: 15px 0 15px;
  padding-bottom: 15px;
  text-align: center;
  border-radius: 15px;

  @media screen and (max-width: $handheld-breakpoint) {
    padding-bottom: 20px;
    margin: 10px 0 10px;
  }

  &Discount,
  &DiscountFree {
    display: flex;
    background-color: #000;
    color: #fff;
    font-size: 20px;
    font-weight: 900;
    position: absolute;
    top: 0;
    right: 0;
    height: 48px;
    justify-content: center;
    align-items: center;
    z-index: 5;

    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 14px;
      height: 33px;
      padding: 0 8px;
      min-width: auto;
    }
  }

  &Discount {
    min-width: 90px;
    padding: 0 15px;
    border-radius: 0 14px;

    @media screen and (max-width: $handheld-breakpoint) {
      min-width: auto;
    }
  }
  &DiscountFree {
    min-width: 65px;
    padding: 0 0;
    border-radius: 0 14px;

    @media screen and (max-width: $handheld-breakpoint) {
      min-width: auto;
    }
  }
  &ImgWrap {
    margin: auto;
    margin-top: 2px;
    width: 98%;
    height: 212px;
    position: relative;
    overflow: hidden;
    border-radius: 14px;

    @media screen and (max-width: $handheld-breakpoint) {
      max-height: 139px;
    }

    img {
      width: 100%;
      min-width: 100%;
      min-height: 100%;
      object-fit: cover;
    }
  }

  .pdfBtn {
    bottom: 0;
    right: 0;
    border-radius: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    border-color: #000;
    font-size: 14px;
    display: flex;
    justify-content: center;
    position: relative;
    margin-top: 20px;

    a {
      width: auto;
      height: auto;
      background-color: #000;
      padding: 3px 10px;
    }
  }

  &Description {
    text-align: center;
    font-size: 12px;
    line-height: 18px;

    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 10px;
      line-height: 15px;
    }

    h3 {
      font-size: 20px;
      font-weight: 900;
      padding: 15px 0;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  &LogoWrapper {
    height: 50px;
    display: block;
    margin: 26px;
    margin-bottom: 15px;

    @media screen and (max-width: $handheld-breakpoint) {
      height: 24px;
      margin: 17px;
      margin-bottom: 9px;
    }
  }
  &Logo {
    max-height: 50px;
    max-width: 100%;

    @media screen and (max-width: $handheld-breakpoint) {
      max-height: 24px;
    }
  }
  .btnDark {
    margin: 20px 0 0;
  }

  .like {
    position: absolute;
    z-index: 1;
    bottom: 13px;
    right: 13px;

    @media screen and (max-width: $handheld-breakpoint) {
      bottom: 9px;
      right: 9px;
    }
  }

  .noBg & {
    background: transparent;
  }

  .rabbateCode {
    width: 180px;
    height: 35px;
  }

  &CouponCodeBtn {
    text-align: center;
    vertical-align: middle;
    user-select: none;
    border-radius: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    color: #fff;
    border-color: #000;
    line-height: 35px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 900;
    display: inline-block;
    margin: 20px auto 0;

    .couponCode {
      width: 120px;
      background-color: white;
      color: black;
      float: left;
      cursor: auto;
      border: 1px solid #000;
      font-weight: 700;
      font-size: 12px;
      line-height: 1.2;
      min-height: 37px;
      display: flex;
      justify-content: center;
      align-items: center;

      &.stationaryCode {
        line-height: 18px;
        font-size: 12px;
      }
    }

    .copyBtn {
      width: 60px;
      float: left;
      padding: 7px 0 8px 0;
      background-color: #000;
      border: 1px solid #000;
      border-left: 0;
      cursor: pointer;
      display: table;

      &.stationaryCoupon {
        padding: 0 0 1px 0;
      }
      p {
        font-size: 10px;
        line-height: 10px;
        font-weight: 900;
        padding: 0 10%;
        margin-bottom: 0rem;
        color: white;
        display: table-cell;
        vertical-align: middle;
        text-align: center;
      }

      &:hover {
        background-color: #81e9f0;
        color: #000;

        p {
          background-color: #81e9f0;
          color: #000;
        }
      }
    }

    .copyPdfButton {
      width: 180px;
      height: 35px;
      margin-bottom: 0rem;
      background-color: black;

      &:hover {
        background-color: #81e9f0;
        color: #000;
        cursor: pointer;
      }
    }

    p {
      margin-bottom: 0rem;
      background-color: #000;
      border-radius: 50px;

      &:hover {
        background-color: #81e9f0;
        color: #000;
        cursor: pointer;
      }
    }

    a {
      width: 180px;
      height: 35px;
      text-decoration: none;
      background-color: black;
      color: white;
      padding: 8px 23px;

      &:hover {
        background-color: #81e9f0;
        color: #000;
        cursor: pointer;
      }
    }
  }

  .whiteCard & {
    background-color: #fff;
  }

  .whiteCard.couponOverview & {
    margin-bottom: 1.5rem !important;
    @media (max-width: 575.98px) {
      margin: 9px 0 0;
    }
    @media (min-width: 576px) and (max-width: 991.98px) {
      margin-bottom: 0px !important;
    }
  }

  &.categoriesCard {
    border: none !important;

    .cardImgWrap {
      border-radius: 15px !important;
    }
  }

  @media (max-width: 575px) {
    &.categoriesCard {
      padding-bottom: 0px;
      margin-bottom: 0px;
    }
  }

  @media (min-width: 575.98px) and (max-width: 768px) {
    &.categoriesCard {
      padding-bottom: 10px;
      margin-bottom: 0px;
    }
  }

  @media screen and (max-width: $handheld-breakpoint) {
    &.categoriesCard &Description h3 {
      font-size: 12px;
      padding: 6px 0;
      margin-bottom: 0;
    }
  }
}