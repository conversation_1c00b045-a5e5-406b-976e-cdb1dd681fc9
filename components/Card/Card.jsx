'use client';
import { useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './Card.module.scss';

// Remove the hook from this utility function
export const displayCouponCode = async (brandSlug, router = null) => {
  // Only try to use router if it's provided
  if (router) {
    router.push(`/brand/${brandSlug}`);
  } else {
    // Fallback to window.location if router isn't available
    window.location.href = `/brand/${brandSlug}`;
  }
  window.scrollTo(0, 0);
};

const Card = ({
  className = '',
  children,
  cardClassName = '',
  ...restProps
}) => {
  return (
    <div className={className} {...restProps}>
      <div className={`${styles.card} ${cardClassName}`}>{children}</div>
    </div>
  );
};

Card.Image = ({ src, className = '', url = '/', alt = '', eager = false, children }) => {
  const imgRef = useRef();
  
  return (
    <div className={`${styles.cardImgWrap} ${className}`}>
      <Link href={url}>
        <img 
          ref={imgRef}
          alt={alt}
          src={src ? src : `products/${Math.floor(Math.random() * 10)}.jpg`}
          onError={() => (imgRef.current.src = '/coupon_image_placeholder.png')}
          loading={eager ? 'eager' : 'lazy'}
          aria-label={alt}
        />
      </Link>
      {children}
    </div>
  );
};

Card.Discount = ({
  className = '',
  type = 'PERCENTAGE',
  shortDescription = '',
  children,
}) => {
  const imgRef = useRef();
  
  if (type === 'FREE') {
    if (shortDescription) {
      return (
        <div
          className={`${styles.cardDiscountFree} ${className}`}
          style={{ padding: '11px' }}
        >
          {shortDescription}
        </div>
      );
    }
    
    return (
      <div className={`${styles.cardDiscountFree} ${className}`}>
        <img
          ref={imgRef}
          src="/icons/gift.png"
          style={{ height: '100%', padding: '11px' }}
          alt="Free coupon icon"
        />
      </div>
    );
  }
  
  return (
    <div className={`${styles.cardDiscount} ${className}`}>
      {type === 'PERCENTAGE'
        ? `${children}%`
        : `${formatCurrency(children)}€`}
    </div>
  );
};

Card.ClientLogo = ({ src, className, ...props }) => {
  return (
    <div className={styles.cardLogoWrapper}>
      <img
        src={src || 'douglas.png'}
        className={`${styles.cardLogo} ${className || ''}`}
        {...props}
        alt={props.alt || "Brand logo"}
      />
    </div>
  );
};

Card.Description = ({ className = '', children }) => {
  return (
    <div className={`${styles.cardDescription} ${className}`}>
      {children}
    </div>
  );
};

Card.CouponButton = ({
  className,
  coupon = {},
  getCouponCode = () => {},
  children,
}) => {
  // Use the router here where it's valid inside a component
  const router = useRouter();
  
  const handleGetCoupon = () => {
    if (coupon.brandSlug) {
      // Pass the router to the utility function
      displayCouponCode(coupon.brandSlug, router);
    }
    getCouponCode();
  };
  
  return (
    <div
      className={`rabatteButton couponCodeBtn  ${className || ''}`}
      onClick={handleGetCoupon}
    >
      {children}
    </div>
  );
};

const formatCurrency = (value) => {
  return value % 1 !== 0
    ? value.toLocaleString('de-DE', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
    : value;
};

export default Card;
