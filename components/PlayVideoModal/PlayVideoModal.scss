.vimeo-modal {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  z-index: 1050;
  outline: 0;
  width: 498px;

  .modal-body {
    padding: 35px 45px;
    display: flex;
    justify-content: center;
    flex-direction: column;

    .modal-links {
      margin-top: 30px;
      .link1 {
        margin-right: 30px;
      }
      .second-button {
        padding-left: 30px;
        padding-right: 30px;
        border-radius: 50px;
      }
    }
  }

  .modal-title {
    font-weight: 900;
    line-height: 30px;
    font-size: 30px;
    margin-bottom: 10px;
  }

  .modal-text {
    font-size: 16px;
    line-height: 24px;
    font-weight: normal;
    margin-bottom: 10px;
    margin-top: 20px;
  }

  .btn-main {
    border: solid 2px #81e9f0;
    &:hover {
      border: solid 2px #000;
    }
  }

  .mar-b-10 {
    margin-bottom: 10px;
  }

  .mar-r-20 {
    margin-right: 20px;
  }

  .mar-b-12 {
    margin-bottom: 12px;
  }
}

@media (max-width: 575px) {
  .vimeo-modal {
    max-width: 90%;
    overflow: auto;
    .modal-body {
      padding: 25px;
    }
  }
}
