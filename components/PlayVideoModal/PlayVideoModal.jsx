import React from 'react';
import Link from 'next/link';
import { Modal, ModalBody } from 'reactstrap';
import { GrClose } from 'react-icons/gr';

import {
  getLocalStorageItem,
  setLocalStorageItem,
} from 'util/localStorageManagement';

import styles from './PlayVideoModal.module.scss';

const PlayVideoModal = ({ videoType, playVideoOnAccept }) => {
  const onVimeoPlayAccept = () => {
    setLocalStorageItem('isVimeoPlayAccepted', true);
    playVideoOnAccept(true);
  };

  const onYoutubePlayAccept = () => {
    setLocalStorageItem('isYoutubePlayAccepted', true);
    playVideoOnAccept(true);
  };

  const vimeoContent = {
    title: 'Vimeo aktivieren',
    content:
      'Damit Du dir diese und ähnliche Videos von CaptainCoupon über Vimeo anschauen kannst, müssen Cookies von Vimeo akzeptiert werden. Mehr dazu findest du in den',
    type: 'vimeo',
    buttonClick: onVimeoPlayAccept,
  };

  const youtubeContent = {
    title: 'YouTube aktivieren',
    content:
      'Damit Du dir diese und ähnliche Videos von CaptainCoupon über YouTube anschauen kannst, müssen Cookies von YouTube akzeptiert werden. Mehr dazu findest du in den',
    type: 'youtube',
    buttonClick: onYoutubePlayAccept,
  };

  const item = videoType === 'vimeo' ? vimeoContent : youtubeContent;

  const onModalClose = () => {
    playVideoOnAccept(false);
  };

  return (
    <Modal contentClassName={styles.vimeoModal} isOpen={true} backdrop="static">
      <div className="container-fluid">
        <div className="row">
          <button className="close float-right" onClick={onModalClose}>
            <GrClose></GrClose>
          </button>
          <ModalBody className={styles.modalBody}>
            <h4 className={`${styles.modalTitle} text-center ${styles.marB10}`}>{item.title}?</h4>
            <div className={`text-center ${styles.modalText} mar-b-20`}>
              {item.content}{' '}
              {item.type === 'vimeo' ? (
                <a href="https://vimeo.com/privacy">
                  Datenschutzbestimmungen von Vimeo
                </a>
              ) : (
                <a href="https://policies.google.com/privacy">
                  Datenschutzbestimmungen von YouTube
                </a>
              )}
              .
            </div>
            <div className={`${styles.modalLinks} text-center`}>
              <span className={styles.link1}>
                <Link href="/datenschutz">
                  <a>Datenschutz</a>
                </Link>
              </span>
              <button
                className={`btn ${styles.btnMain} ${styles.secondButton}`}
                onClick={item.buttonClick}
              >
                {item.title}
              </button>
            </div>
          </ModalBody>
        </div>
      </div>
    </Modal>
  );
};

export default PlayVideoModal;