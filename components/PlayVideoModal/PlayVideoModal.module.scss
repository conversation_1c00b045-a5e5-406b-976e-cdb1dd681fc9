.vimeoModal {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  z-index: 1050;
  outline: 0;
  width: 498px;

  .modalBody {
    padding: 35px 45px;
    display: flex;
    justify-content: center;
    flex-direction: column;

    .modalLinks {
      margin-top: 30px;
      .link1 {
        margin-right: 30px;
      }
      .secondButton {
        padding-left: 30px;
        padding-right: 30px;
        border-radius: 50px;
      }
    }
  }

  .modalTitle {
    font-weight: 900;
    line-height: 30px;
    font-size: 30px;
    margin-bottom: 10px;
  }

  .modalText {
    font-size: 16px;
    line-height: 24px;
    font-weight: normal;
    margin-bottom: 10px;
    margin-top: 20px;
  }

  .btnMain {
    border: solid 2px #81e9f0;
    &:hover {
      border: solid 2px #000;
    }
  }

  .marB10 {
    margin-bottom: 10px;
  }

  .marR20 {
    margin-right: 20px;
  }

  .marB12 {
    margin-bottom: 12px;
  }
}

@media (max-width: 575px) {
  .vimeoModal {
    max-width: 90%;
    overflow: auto;
    .modalBody {
      padding: 25px;
    }
  }
}