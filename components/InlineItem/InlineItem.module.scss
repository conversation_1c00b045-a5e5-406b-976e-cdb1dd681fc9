@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.inlineItem {
  &:nth-child(-n + 4) a {
    border-top: 1px solid #AFAFAF;
  }
  a {
    color: #000;
    border-bottom: 1px solid #AFAFAF;
    padding: 10px;
    display: block;
    text-decoration: none;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-left: 20px;
    padding-left: 35px;

    position: relative;
    &:hover {
      color: #81e9f0;
    }
  }
}

.inlineItemLabel {
  /* Preserving empty selector for compatibility */
}

.inlineItem svg,
.inlineItem img {
  display: inline-block;
  margin-right: 10px;
  max-width: 16px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
  max-width: 25px;
}

@include media-breakpoint-down(md) {
  .inlineItem {
    &:nth-child(-n + 4) a {
      border-top: 0;
    }

    &:nth-child(9) a,
    &:nth-child(10) a {
      margin-top: 50px;
      border-top: 1px solid #AFAFAF;
    }
  }
}

@include media-breakpoint-down(sm) {
  .inlineItem {
    &:nth-child(9) a,
    &:nth-child(10) a {
      margin-top: 0px;
      border-top: 0;
    }

    &:nth-child(4n) a {
      margin-bottom: 50px;
    }
    &:nth-child(4n + 1) a {
      border-top: 1px solid #AFAFAF;
    }
  }
}