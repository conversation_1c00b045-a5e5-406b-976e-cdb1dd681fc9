'use client';
import React from 'react';
import { GrClose } from 'react-icons/gr';
import { ModalGeneric } from 'components';
import { SET_CANCEL_SUBSCRIPTION_MODAL } from 'redux-store/settings/types';
import styles from './CancelSubscriptionModal.module.scss';

const CancelSubscriptionModal = ({
  modal,
  cancelSubscriptionHandler,
  dispatch,
  ...props
}) => {
  const handleClose = () => {
    dispatch({
      type: SET_CANCEL_SUBSCRIPTION_MODAL,
      payload: false,
    });
  };

  return (
    <ModalGeneric modal={modal} className={styles.cancelSubModal}>
      <button
        className="close float-right"
        onClick={handleClose}
      >
        <GrClose />
      </button>
      <div className="row">
        <div className={`col ${styles.sideImageWrap} d-none d-lg-block`} />

        <div className={`col ${styles.customModalPadding} pad-tb-2 pad-lg-l-5`}>
          <h1
            className={`${styles.cancelSubHeading} text-center pad-t-3 pad-b-4`}
            data-cy="endSubscriptionTitle"
          >
            Willst du deine CC+ Mitgliedschaft wirklich beenden?
          </h1>
          <div className="pad-b-3">
            <div className={styles.modalIcons}>
              <div className="row">
                <div className="col text-center">
                  <img
                    src="/modal-icons/cancel-modal-1.svg"
                    className={styles.modalIcon}
                    alt="Symbol eines Sparschweins mit traurigem Gesichtsausdruck"
                  />
                  <p>Du sicherst dir keine attraktiven Nachlässe mehr</p>
                </div>
                <div className="col text-center">
                  <img
                    src="/modal-icons/cancel-modal-2.svg"
                    className="modal-icon"
                    alt="„X“- oder Kreuz-Symbol"
                  />
                  <p>
                    Du musst dich wieder mit tausenden ungültigen Gutscheincodes
                    rumschlagen
                  </p>
                </div>
              </div>
              <div className="row">
                <div className="col text-center">
                  <img
                    src="/modal-icons/cancel-modal-3.svg"
                    className={styles.modalIcon}
                    alt="Symbol eines runden Aufklebers mit der Aufschrift „NEW"
                  />
                  <p>
                    Du entdeckst keine exklusiven Angebote und Produkte mehr
                  </p>
                </div>
                <div className="col text-center">
                  <img
                    src="/modal-icons/cancel-modal-4.svg"
                    className={styles.modalIcon}
                    alt="Weinendes Smiley-Symbol"
                  />
                  <p>Gerhard Limone wird dich vermissen</p>
                </div>
              </div>
            </div>
          </div>
          <div>
            <button
              onClick={cancelSubscriptionHandler}
              className={`btn btn-red float-right btn-tall ${styles.btnCancelSubsModal}`}
              data-cy="endSubscriptionCompleteBtn"
            >
              Mitgliedschaft beenden
            </button>
            <button
              onClick={handleClose}
              className={`btn btn-dark btn-tall ${styles.btnCancelSubsModal}`}
              data-cy="endSubscriptionCancelBtn"
            >
              zurück
            </button>
          </div>
        </div>
      </div>
    </ModalGeneric>
  );
};
export default CancelSubscriptionModal;
