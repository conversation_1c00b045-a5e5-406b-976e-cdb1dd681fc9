@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');

.modalIcon {
  max-height: 85px;
  padding-bottom: 10px;
}

.modalDialog {
  :global(button.close) {
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 100;
  }
}

.cancelSubModal {
  max-width: 1140px;

  .btnCancelSubsModal {
    @media (max-width: 333.98px) {
      font-size: 12px;
      padding-left: 5px;
      padding-right: 5px;
    }
    @media (min-width: 334px) and (max-width: 377.98px) {
      font-size: 14px;
      padding-left: 6px;
      padding-right: 6px;
    }
    @media (min-width: 378px) and (max-width: 420px) {
      padding-left: 8px;
      padding-right: 8px;
    }
  }

  .sideImageWrap {
    position: relative;
    background: transparent
      url('/headers/captaincoupon_setting-delete-account.jpg') top center
      no-repeat;
    background-size: cover;
    margin-top: -17px;
    margin-bottom: -32px;
    margin-left: -2px;
  }

  .modalBody {
    padding-bottom: 30px;
  }
  .modalTitle {
    font-size: 30px;
    font-weight: 900;
    line-height: 30px;
  }
  .modalSubtitle {
    font-weight: bold;
  }
}

.cancelSubHeading {
  font-size: 40px;
  line-height: 43px;
  font-family: Montserrat;
  font-weight: 900;
}

@include media-breakpoint-down(md) {
  .cancelSubHeading {
    font-size: 30px;
    line-height: 35px;
  }
}