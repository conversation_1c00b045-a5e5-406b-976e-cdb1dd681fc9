import React, { useMemo, useState, useCallback, useContext } from 'react';
import { useRouter } from 'next/router';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import * as CouponService from 'services/coupons.service';
import { CardHorizontal } from '../CardHorizontal';
import { PDFDownloadButton } from '../StationaryCoupon/StationaryCoupon';
import { isAuth } from 'services/auth.service';
import MainAppContext from 'context/mainAppContext';

const LONG_WORD_MIN_LENGTH = 16;

const hasLongWords = (text) => {
  const words = text.split(/[\s-]+/);
  return (
    words.find((word) => word.length >= LONG_WORD_MIN_LENGTH) !== undefined
  );
};

const discountCodeFail = (editCoupon, id, onCustomError) => {
  return (status, errorData) => {
    let errorParam = '';
    switch (status) {
      case 500:
        errorParam = ['SERVER_ERROR'];
        break;
      case 401:
        errorParam = ['AUTHENTICATION_ERROR'];
        break;
      case 404:
        switch (errorData) {
          case 'Consumer not found.':
            errorParam = ['CONSUMER_NOT_FOUND'];
            break;
          case 'Coupon not found.':
            errorParam = ['COUPON_NOT_FOUND'];
            break;
          case 'Discount code not found for coupon.':
            errorParam = ['DISCOUNT_CODE_NOT_FOUND'];
            break;
          default:
            errorParam = ['UNKNOWN_ERROR'];
        }
        break;
      case 400:
        switch (errorData) {
          case 'Limit for copying discount codes per brand is reached.':
            errorParam = ['SPENT_ALL_DAILY_COUPONS'];
            break;
          case 'Coupon is sold out.':
            errorParam = ['SOLD_OUT'];
            break;
          case 'Coupon is expired.':
            errorParam = ['EXPIRED'];
            break;
          case 'Unsubscribed consumer cannot use this coupon!':
            errorParam = ['NO_ACTIVE_SUBSCRIPTION'];
            break;
          case 'Unregistered consumer cannot use this coupon!':
            errorParam = onCustomError();
            break;
          case 'Boost is not valid!':
            errorParam = onCustomError();
            break;
          default:
            errorParam = ['UNKNOWN_ERROR'];
        }
        break;
      default:
        errorParam = ['UNKNOWN_ERROR'];
    }
    return editCoupon(id, ['status'], errorParam);
  };
};

const discountCodeSuccess = (editCoupon, discountCodeType, id) => {
  return (discountCode) => {
    if (discountCodeType !== 'STATIONARY') {
      editCoupon(id, ['code'], [discountCode]);
    } else {
      editCoupon(id, ['codeURL'], [discountCode]);
    }
  };
};

const couponLinkSuccess = (editCoupon, id) => {
  return (couponLinkInfo) => {
    editCoupon(id, ['couponLinkInfo'], [couponLinkInfo]);
  }
}

const showCouponCode = async (
  id,
  discountCodeType,
  editCoupon,
  reachBoost,
  onCustomError
) => {
  await CouponService.getDiscountCode(
    id,
    discountCodeSuccess(editCoupon, discountCodeType, id),
    discountCodeFail(editCoupon, id, onCustomError),
    reachBoost
  );
};

const showCouponLink = async (
  id,
  editCoupon,
  onCustomError
) => {
  await CouponService.getCouponLink(
    id,
    couponLinkSuccess(editCoupon, id),
    discountCodeFail(editCoupon, id, onCustomError)
  );
}

const CouponCardHorizontal = ({ coupon, brandCountDownEnabled }) => {
  const { addMessage } = useAPIError();
  const router = useRouter();

  const [isFavourite, setIsFavourite] = useState(coupon.isFavourite);
  const [code, setCode] = useState(coupon.code);
  const [codeURL, setCodeURL] = useState(coupon.codeURL);
  const [couponLinkInfo, setCouponLinkInfo] = useState({});
  const [status, setStatus] = useState(coupon.status);
  const { setShowLoginModal } = useContext(MainAppContext);

  const onLikeSuccess = useCallback(
    () => setIsFavourite((isFavourite) => !isFavourite),
    [setIsFavourite]
  );
  const onLikeError = useCallback(
    (status, data) => {
      addMessage(data, 'error');
    },
    [addMessage]
  );

  const hasLongWordsHeadline = useMemo(
    () =>
      coupon.discountType === 'FREE' && hasLongWords(coupon.freeDescription),
    [coupon]
  );

  const isTouchDevice = useMemo(
    () =>
      'ontouchstart' in window ||
      navigator.maxTouchPoints > 0 ||
      navigator.msMaxTouchPoints > 0,
    []
  );

  const editCoupon = (id, keys, values) => {
    keys.forEach((key, i) => {
      switch (key) {
        case 'code':
          setCode(values[i]);
          break;
        case 'codeURL':
          setCodeURL(values[i]);
          break;
        case 'status':
          setStatus(values[i]);
        case 'couponLinkInfo':
          setCouponLinkInfo(values[i]);
      }
    });
  };

  const reachBoost = router.query?.boost === '';

  const onCustomError = () => {
    if (!isAuth()) {
      setShowLoginModal(true);
      return '';
    } else {
      return ['NO_ACTIVE_SUBSCRIPTION'];
    }
  };

  const getCouponCode = async () => {
    showCouponCode(
      coupon.id,
      coupon.discountCodeType,
      editCoupon,
      reachBoost,
      onCustomError
    );
  };

  const getCouponLink = async () => {
    showCouponLink(
      coupon.id,
      editCoupon,
      onCustomError
    );
  }

  return (
    <CardHorizontal
      coupon={{ ...coupon, status }}
      handleLikeClick={() =>
        CouponService.setFavouriteStatus(
          coupon.id,
          !isFavourite,
          onLikeSuccess,
          onLikeError
        )
      }
      isAuth={isAuth()}
      isTouchDevice={isTouchDevice}
      isLiked={isFavourite}
      hasLongWordsHeadline={hasLongWordsHeadline}
      getCouponCode={getCouponCode}
      getCouponLink={getCouponLink}
      showToastMessage={addMessage}
      PDFDownloadButton={PDFDownloadButton}
      code={code}
      codeURL={codeURL}
      couponLinkInfo={couponLinkInfo}
      brandCountDownEnabled={brandCountDownEnabled}
    />
  );
};

export default CouponCardHorizontal;
