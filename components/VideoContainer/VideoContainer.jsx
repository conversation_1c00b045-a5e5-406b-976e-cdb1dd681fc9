import { useState } from 'react';
import { FaRegPlayCircle } from 'react-icons/fa/';
import {
  toValidEmbedVideoUrl,
  getVideoType,
  isVideoTypeAccepted,
} from '../VideoBlock/VideoBlock';
import PlayVideoModal from '../PlayVideoModal/PlayVideoModal';
import styles from './VideoContainer.module.scss';

const VideoContainer = ({ videoUrl, thumblUrl }) => {
  const [modal, setModal] = useState(false);
  const [policiesAcceptenceModal, setPoliciesAcceptenceModal] = useState(false);

  const toggleVideoModal = (e) => {
    e.stopPropagation();
    setModal(!modal);
    if (!isVideoTypeAccepted(videoUrl)) {
      setPoliciesAcceptenceModal(true);
    } else {
      document.getElementById(videoUrl).src += '?autoplay=1';
    }
  };

  const playVideoOnAccept = (isAccepted) => {
    setPoliciesAcceptenceModal(false);
    if (!isAccepted) {
      setModal(false);
    } else {
      document.getElementById(videoUrl).src += '?autoplay=1';
    }
  };

  return (
    <>
      <div
        hidden={modal}
        className={styles.videoThumbnailContainer}
        onClick={toggleVideoModal}
      >
        <FaRegPlayCircle className={styles.videoThumbnailPlay} />

        <div className="video-placeolder-overlay"></div>
        <img src={thumblUrl} alt="Video-Vorschaubild" />
      </div>
      <iframe
        id={videoUrl}
        hidden={!modal}
        title="vimeo-player"
        src={toValidEmbedVideoUrl(videoUrl)}
        width="640"
        height="360"
        frameBorder="0"
        allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
      ></iframe>

      {policiesAcceptenceModal && (
        <PlayVideoModal
          videoType={getVideoType(videoUrl)}
          playVideoOnAccept={playVideoOnAccept}
        />
      )}
    </>
  );
};

export default VideoContainer;