import { useEffect } from 'react';
import styles from './Snackbar.module.scss';

const Snackbar = ({ open, message, status, onClose, autoHideDuration }) => {
  useEffect(() => {
    if (open) {
      setTimeout(() => onClose(), autoHideDuration);
    }
  }, [open]);

  return (
    <div
      className={
        open ? `${styles.apiErrorNotification} ${styles[status]}` : styles.apiErrorNotification
      }
    >
      {message}
    </div>
  );
};

export default Snackbar;