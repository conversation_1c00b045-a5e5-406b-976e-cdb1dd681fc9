import React from 'react';
import { useEffect, useState } from 'react';
import {
  PDFDownloadLink,
  Page,
  Text,
  View,
  Image,
  Document,
  StyleSheet,
  Font,
} from '@react-pdf/renderer';

import DownloadIcon from '../CustomIcons/DownloadIcon';

export const wrapCouponRegulations = (couponReg, fontSize = 12) =>
  `<div style="word-wrap:break-word; text-align:center; margin:0px; padding:0px; font-size:${fontSize}px; font-family:Montserrat; color: #000000">${couponReg}</div>`;

function getBase64Image(imgElement) {
  const img = document.getElementById(imgElement);
  var canvas = document.createElement('canvas');
  canvas.width = img.naturalWidth;
  canvas.height = img.naturalHeight;
  var ctx = canvas.getContext('2d');
  ctx.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight);
  var dataURL = canvas.toDataURL('image/png');
  return dataURL.replace(/^data:image\/(png|jpg);base64,/, '');
}

const MyDocument = ({
  codeURL,
  image,
  brandLogo,
  amountCondition,
  discountType,
  freeDescription,
  discountValue,
  validationDate,
  couponRegulations,
}) => {
  const [codeURLBase64, setCodeURLBase64] = useState(null);
  const [couponImageBase64, setCouponImageBase64] = useState(null);
  const [brandLogoBase64, setBrandLogoBase64] = useState(null);

  // Magic, do not touch
  //
  // WARNING, this is not JSX, if you need to edit this function, please read the documentation of react-pdf (https://react-pdf.org/)

  useEffect(() => {
    setCodeURLBase64(getBase64Image(`codeUrl-${codeURL}`));
    setCouponImageBase64(getBase64Image(`couponImage-${image}`));
    setBrandLogoBase64(getBase64Image(`brandLogo-${brandLogo}`));
  }, [codeURL, image, brandLogo]);

  const amountConditionJSX =
    amountCondition !== 0 ? (
      <View
        style={
          discountType === 'PERCENTAGE' && amountCondition.length > 6
            ? [styles.horizFlex, { marginBottom: 0 }]
            : [styles.horizFlex, { marginBottom: -15 }]
        }
      >
        <Text style={[styles.headingValue, { fontWeight: 400 }]}>
          Mindestbestellwert:{' '}
          <Text
            style={[styles.headingValue, { marginTop: 15, fontWeight: 700 }]}
          >
            {amountCondition.toLocaleString('de-DE')}
          </Text>
        </Text>
      </View>
    ) : (
      <View style={[styles.horizFlex, { marginBottom: -15 }]}>
        <Text>
          Mindestbestellwert:{' '}
          <Text style={styles.headingValueSmaller}>keiner</Text>
        </Text>
      </View>
    );

  return (
    <Document>
      <Page size="A4">
        <View style={styles.container}>
          <View style={styles.verticalLeft}>
            <Image
              style={styles.coverImage}
              src={`data:image/png;base64, ${couponImageBase64}`}
            />
          </View>
          <View style={styles.verticalRight}>
            <View style={[styles.horizFlex, { marginBottom: 15 }]}>
              <Image
                style={styles.brandImage}
                src={`data:image/png;base64, ${brandLogoBase64}`}
              />
            </View>
            <View style={[styles.horizFlex, { marginBottom: 15 }]}>
              <Text
                style={
                  discountType === 'FREE'
                    ? [styles.header, { fontWeight: 900 }]
                    : [styles.discount, { fontWeight: 700 }]
                }
              >
                {discountType === 'FREE'
                  ? freeDescription
                  : discountType === 'PERCENTAGE'
                  ? discountValue + '%'
                  : (discountValue % 1 != 0
                      ? discountValue.toLocaleString('de-DE', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })
                      : discountValue) + '€'}
              </Text>
            </View>
            {amountConditionJSX}
            <Text> </Text>
            <View
              style={
                amountCondition.length <= 6
                  ? [styles.horizFlex, { marginBottom: 15 }]
                  : [styles.horizFlex, { marginBottom: 15, marginTop: -7 }]
              }
            >
              <Text style={styles.headingValue}>
                Gültig bis:{' '}
                <Text style={{ fontWeight: 700 }}>
                  {Intl.DateTimeFormat('de-DE', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                  }).format(new Date(validationDate))}
                </Text>
              </Text>
            </View>
            <View style={[styles.horizFlex, { marginBottom: 30 }]}>
              <Image style={styles.regulationsImage} src={couponRegulations} />
            </View>
            <View style={[styles.horizFlex, { marginBottom: 15 }]}>
              <Image
                style={styles.barCodeImage}
                src={`data:image/png;base64, ${codeURLBase64}`}
              />
            </View>
            <View style={styles.footer}>
              <Text style={styles.regular}>
                Weitere Einkaufsvorteile auf{' '}
                <Text style={styles.bold}>www.captaincoupon.de</Text>
              </Text>
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
};

const MyDocumentMemo = React.memo(MyDocument);

export const PDFDownloadButton = (coupon) => (
  <PDFDownloadLink
    document={<MyDocumentMemo {...coupon} />}
    fileName="couponCode.pdf"
  >
    {({ blob, url, loading, error }) => {
      return error ? (
        'Fehler beim Erzeugen des Coupons'
      ) : loading && !url ? (
        'Dokument wird geladen...'
      ) : (
        <DownloadIcon />
      );
    }}
  </PDFDownloadLink>
);

const monserratURLRegular = '../fonts/montserrat-v15-latin-regular.woff';
const monserratURLBold = '../fonts/montserrat-v15-latin-700.woff';
const monserratURLSuperBold = '../fonts/montserrat-v15-latin-900.woff';

Font.register({
  family: 'Montserrat',
  fonts: [
    { src: monserratURLRegular, fontWeight: 400 },
    {
      src: monserratURLBold,
      fontWeight: 700,
    },
    {
      src: monserratURLSuperBold,
      fontWeight: 900,
    },
  ],
  format: 'woff',
});

const defaultFont = 'Montserrat';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'row',
  },
  verticalLeft: {
    width: '50%',
    height: '75%',
  },
  verticalRight: {
    width: '50%',
    height: '75%',
    position: 'relative',
    padding: 30,
    paddingBottom: 0,
  },
  coverImage: {
    width: '100%',
    minWidth: '100%',
    minHeight: '100%',
    objectFit: 'cover',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 40,
  },
  horizFlex: {
    display: 'flex',
    flexDirection: 'row',
  },
  brandImage: {
    'object-fit': 'cover',
    maxWidth: '90%',
    maxHeight: 140,
    position: 'relative',
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  barCodeImage: {
    maxWidth: '60%',
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  discount: {
    fontSize: 50,
    fontFamily: defaultFont,
    textAlign: 'center',
  },
  header: {
    textAlign: 'center',
    fontSize: 30,
    fontFamily: defaultFont,
  },
  regular: {
    fontSize: 8,
    fontFamily: defaultFont,
    fontWeight: 400,
    marginTop: 20,
  },
  bold: {
    fontSize: 9,
    fontFamily: defaultFont,
    fontWeight: 700,
    color: 'black',
  },
  headingValue: {
    fontSize: 18,
    fontFamily: defaultFont,
    textAlign: 'center',
  },
  headingValueSmaller: {
    fontSize: 16,
    fontFamily: defaultFont,
  },
  regulationsImage: {
    objectFit: 'cover',
    maxHeight: 180,
    position: 'relative',
  },
});
