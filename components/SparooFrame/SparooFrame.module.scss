@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.sparooFrame {
  h2 {
    font-size: 17px;
    line-height: 1.2;
    letter-spacing: 0.5em;
    text-transform: uppercase;
    margin-bottom: 32px;

    @include media-breakpoint-down(xs) {
      font-size: 12px;
      letter-spacing: 0.3em;
    }
  }

  iframe {
    border: 0;
    height: 750px;
    overflow: hidden;

    @include media-breakpoint-down(md) {
      height: 730px;
    }

    @include media-breakpoint-down(sm) {
      height: 820px;
    }

    @include media-breakpoint-down(xs) {
      height: 820px;
    }

    @media (max-width: 530px) {
      height: 780px;
    }

    @media (max-width: 480px) {
      height: 760px;
    }

    @media (max-width: 440px) {
      height: 740px;
    }

    @media (max-width: 400px) {
      height: 740px;
    }

    @media (max-width: 360px) {
      height: 680px;
    }
  }
}