import RightArrowIcon from '../CustomIcons/RightArrowIcon';
import LeftArrowIcon from '../CustomIcons/LeftArrowIcon';

import styles from './Pagination.module.scss';

const Pagination = ({ count, rowsPerPage, currentPage, onChangePage }) => {
  const totalPages = Math.ceil(count / rowsPerPage);
  return (
    <div className={styles.pagination}>
      {currentPage !== 1 && (
        <span
          className={styles.pagination__navPrev}
          onClick={() => onChangePage(currentPage - 1)}
        >
          <LeftArrowIcon fill="#fff" height="20px" />
        </span>
      )}
      <div className={styles.pagination__info}>
        Seite {currentPage} von {totalPages}
      </div>
      {currentPage < totalPages && (
        <span
          className={styles.pagination__navNext}
          onClick={() => onChangePage(currentPage + 1)}
        >
          <RightArrowIcon fill="#fff" height="20px" />
        </span>
      )}
    </div>
  );
};

export default Pagination;