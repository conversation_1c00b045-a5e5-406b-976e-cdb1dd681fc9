import { useEffect, useRef, useState } from 'react';
import { loadScript } from 'util/helpers';
import styles from './MolliePayment.module.scss';

const MolliePayment = (props) => {
  // const [pageMolie, setPageMolie] = useState();

  const { molieScript, formError, selectedPayment } = props;
  useEffect(() => {
    // const molieScript = loadScript('https://js.mollie/v1/mollie.js', () => {
    // const Mollie = window.Mollie;
    // const mollie = Mollie(process.env.NEXT_PUBLIC_MOLLIE_KEY, {
    //   testmode: process.env.NEXT_PUBLIC_MOLLIE_TESTING,
    // });

    // setPageMolie(mollie);
    console.log('Molie Payment Initialized');
    const options = {
      styles: {
        base: {
          color: '#444',
          fontSize: '14px',
          '::placeholder': {
            color: 'rgba(68, 68, 68, 0.2)',
          },
        },
      },
    };

    const cardHolder = molieScript.createComponent('cardHolder', options);

    cardHolder.mount('#card-holder');
    const cardHolderError = document.getElementById('card-holder-error');
    cardHolder.addEventListener('change', (event) => {
      if (event.error && event.touched) {
        cardHolderError.textContent =
          event.error === 'Card holder cannot be empty'
            ? 'Karteninhaberin muss ausgefüllt sein.'
            : event.error;
      } else {
        cardHolderError.textContent = '';
      }
    });

    const cardNumber = molieScript.createComponent('cardNumber', options);
    cardNumber.mount('#card-number');
    const cardNumberError = document.getElementById('card-number-error');
    cardNumber.addEventListener('change', (event) => {
      if (event.error && event.touched) {
        cardNumberError.textContent =
          event.error === 'Card number cannot be empty'
            ? 'Kartennummern muss ausgefüllt sein.'
            : event.error;
      } else {
        cardNumberError.textContent = '';
      }
    });

    const expiryDate = molieScript.createComponent('expiryDate', options);
    expiryDate.mount('#expiry-date');
    const expiryDateError = document.getElementById('expiry-date-error');
    expiryDate.addEventListener('change', (event) => {
      if (event.error && event.touched) {
        expiryDateError.textContent =
          event.error === 'Expiry date cannot be empty'
            ? 'Ablaufdatum muss ausgefüllt sein.'
            : event.error === 'Card has expired'
            ? 'Kreditkarte ist abgelaufen.'
            : event.error;
      } else {
        expiryDateError.textContent = '';
      }
    });

    const verificationCode = molieScript.createComponent(
      'verificationCode',
      options
    );
    verificationCode.mount('#verification-code');
    const verificationCodeError = document.getElementById(
      'verification-code-error'
    );
    verificationCode.addEventListener('change', (event) => {
      if (event.error && event.touched) {
        verificationCodeError.textContent =
          event.error === 'Verification code cannot be empty'
            ? 'Bestätigungscode muss ausgefüllt sein.'
            : event.error;
      } else {
        verificationCodeError.textContent = '';
      }
    });

    // setDisabled(false);
    // });

    // return () => {
    //   if (molieScript) document.body.removeChild(molieScript);
    // };
    return () => {
      console.log('unmounting fired');
      // cardHolder.unmount();
    };
  }, []);
  return (
    <>
      <div id="card"></div>
      <div className="form-fields">
        <div className="form-group form-group--card-holder">
          <label className="label" htmlFor="card-holder">
            Karteninhaber
          </label>
          <div id="card-holder"></div>
          <div
            id="card-holder-error"
            className="field-error"
            role="alert"
          ></div>
        </div>

        <div className="form-row">
          <div className="form-group col form-group--card-number">
            <label className="label" htmlFor="card-number">
              Kartennummer
            </label>
            <div id="card-number"></div>
            <div
              id="card-number-error"
              className="field-error"
              role="alert"
            ></div>
          </div>
          <div className="form-group col form-group--expiry-date">
            <label className="label" htmlFor="expiry-date">
              Ablaufdatum
            </label>
            <div id="expiry-date"></div>
            <div
              id="expiry-date-error"
              className="field-error"
              role="alert"
            ></div>
          </div>

          <div className="form-group col  form-group--verification-code">
            <label className="label" htmlFor="verification-code">
              Verifizierungs-Code
            </label>
            <div id="verification-code"></div>
            <div
              id="verification-code-error"
              className="field-error"
              role="alert"
            ></div>
          </div>
        </div>
      </div>
      {formError && (
        <div id="form-error" className="alert alert-danger">
          {formError}
        </div>
      )}
    </>
  );
};

export default MolliePayment;