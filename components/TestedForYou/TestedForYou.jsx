import React from 'react';
import Image from 'next/image';
import styles from './TestedForYou.module.scss';

const TestedForYou = ({ items }) => {

  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className={styles.testedForYouContainer}>
      <h2 className="col-12 section-header text-left justify-content-space-between px-1 px-md-3">
        <span>Für dich getestet</span>
      </h2>
      <div className="col-12 pt-4">
        {/* Desktop view */}
        <div className="row d-none d-md-flex">
          {items.map((item, index) => (
            <div key={index} className="col-md-6 px-1 px-md-3 mb-4">
              <div className={`${styles.testedCard} card`}>
                <div className="row">
                  <div className="col-6 pr-1">
                    <div className={styles.imageContainer}>
                      <a href={item.imageLink} target="_blank" rel="noopener noreferrer">
                        <Image
                          src={item.compressedImage || item.image}
                          alt={item.heading}
                          width={1200}
                          height={484}
                          sizes="80vw"
                          style={{ width: '100%', borderRadius: '10px', cursor: 'pointer' }}
                          className={styles.backgroundImage}
                        />
                      </a>
                    </div>
                  </div>
                  <div className="col-6 d-flex flex-column justify-content-around px-2 pr-3">
                    <div className={styles.logoWrapper}>
                      <Image
                        src={item.compressedLogo || item.logo}
                        alt="Brand logo"
                        width={100}
                        height={100}
                        sizes="80vw"
                        className={styles.logo}
                        style={{ width: '80px', height: '80px' }}
                      />
                    </div>
                    <div className={styles.descriptionContainer} style={{ marginTop: '18px' }}>
                      <div className={styles.descriptionTitle}>
                        {item.heading}
                      </div>
                      <div style={{ height: '12px' }}></div>
                      <div className={styles.descriptionContent} style={{ marginTop: '10px' }}>
                        <div dangerouslySetInnerHTML={{ __html: item.content }} />
                      </div>
                    </div>
                    <a
                      href={item.buttonLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`rabatteButton couponCodeBtn ${styles.button}`}
                      style={{ textDecoration: 'none' }}
                    >
                      {item.buttonText}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile view - horizontal scrolling */}
        <div className="d-md-none">
          <div className={styles.mobileScrollContainer}>
            {items.map((item, index) => (
              <div key={index} className={`${styles.mobileCard} card`}>
                <div className="row">
                  <div className="col-5 pr-0">
                    <div className={styles.imageContainer}>
                      <a href={item.imageLink} target="_blank" rel="noopener noreferrer">
                        <Image
                          src={item.compressedImage || item.image}
                          alt={item.heading}
                          width={175}
                          height={220}
                          style={{ width: '100%', objectFit: 'cover', borderRadius: '10px 0 0 10px', cursor: 'pointer' }}
                        />
                      </a>
                    </div>
                  </div>
                  <div className="col-7 d-flex flex-column justify-content-around pl-1 pr-2">
                    <div className={styles.logoWrapper}>
                      <Image
                        src={item.compressedLogo || item.logo}
                        alt="Brand logo"
                        width={61}
                        height={38}
                        className={styles.logo}
                      />
                    </div>
                    <div className={styles.descriptionContainer} style={{ marginTop: '18px' }}>
                      <div className={styles.descriptionTitle}>
                        {item.heading}
                      </div>
                      <div className={styles.descriptionContent} style={{ marginTop: '10px' }}>
                        <div dangerouslySetInnerHTML={{ __html: item.content }} />
                      </div>
                    </div>
                    <a
                      href={item.buttonLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`rabatteButton couponCodeBtn ${styles.button}`}
                      style={{ textDecoration: 'none' }}
                    >
                      {item.buttonText}
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestedForYou;
