import React, { useState } from 'react';
import Link from 'next/link';
import {
  Carousel,
  CarouselControl,
  CarouselIndicators,
  CarouselItem,
} from 'reactstrap';

import styles from './BrandCarousel.module.scss';

export default ({ brandsForSlides, showIndicator }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [animating, setAnimating] = useState(false);

  const next = () => {
    if (animating) return;
    const nextIndex =
      activeIndex === brandsForSlides.length - 1 ? 0 : activeIndex + 1;
    setActiveIndex(nextIndex);
  };

  const previous = () => {
    if (animating) return;
    const nextIndex =
      activeIndex === 0 ? brandsForSlides.length - 1 : activeIndex - 1;
    setActiveIndex(nextIndex);
  };

  const goToIndex = (newIndex) => {
    if (animating) return;
    setActiveIndex(newIndex);
  };

  const slides =
    Array.isArray(brandsForSlides) &&
    brandsForSlides.map((brandList, index) => {
      return (
        <CarouselItem
          onExiting={() => setAnimating(true)}
          onExited={() => setAnimating(false)}
          className={styles.brandCarouselItem}
          key={index}
        >
          <div className={styles.brandLogoContainer}>
            {brandList?.map((item) => {
              return (
                <Link href={`/brand/${item.slug}`} key={item.slug} legacyBehavior>
                  <img
                    src={item.compressedLogo || item.logo}
                    alt={`Logo der Brand ${item.slug} gutschein`}
                    loading="lazy"
                  />
                </Link>
              );
            })}
          </div>
        </CarouselItem>
      );
    });

  return (
    <Carousel
      activeIndex={activeIndex}
      next={next}
      previous={previous}
      className={styles.brandCarousel}
      interval={4000}
    >
      {showIndicator && (
        <CarouselIndicators
          items={brandsForSlides || []}
          activeIndex={activeIndex}
          onClickHandler={goToIndex}
        />
      )}
      {slides}
      <CarouselControl
        direction="prev"
        directionText="Previous"
        onClickHandler={previous}
        cssModule={{ color: '#000' }}
      />
      <CarouselControl
        direction="next"
        directionText="Next"
        onClickHandler={next}
      />
    </Carousel>
  );
};
