// import required since mixins are not working from global
@import '../../styles/sass/mixins';

.brandCarousel {
  width: 100%;
  margin-bottom: 50px;

  @include mediaMobile {
    width: 90%;
  }

  :global(.carousel-control-next) {
    width: 5%;
    right: -5%;
    @include mediaMobile {
      right: -3%;
    }

    @include mediaTablet {
      right: -3%;
    }

    @include mediaLaptop {
      right: -3%;
    }

    :global(.carousel-control-next-icon) {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23000' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
    }
  }

  :global(.carousel-control-prev) {
    width: 5%;
    left: -5%;
    @include mediaMobile {
      left: -3%;
    }

    @include mediaTablet {
      left: -3%;
    }

    @include mediaLaptop {
      left: -3%;
    }

    :global(.carousel-control-prev-icon) {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23000' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
    }
  }
}

.brandLogoContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;

  @include mediaMobile {
    justify-content: space-around;
  }
  @include mediaTablet {
    justify-content: space-around;
  }

  :global(img) {
    cursor: pointer;
    width: 14.6%;
    height: 23%;
    margin: 1% 1%;
    padding: 20px;

    @include mediaMobile {
      width: 30%;
      padding: 25px 10px;
    }
    @include mediaTablet {
      width: 20%;
      padding: 25px 10px;
    }
  }
}
