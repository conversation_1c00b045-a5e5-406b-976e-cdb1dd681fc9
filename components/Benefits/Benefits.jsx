import { Container, Row } from 'reactstrap';
import { BadgeCard } from '../../components';

import styles from './Benefits.module.scss';

export default function Benefits({ badges, showHeading = true }) {
  return (
    <div className="Benefits">
      {showHeading && <h1 className="main-header">Deine CC+ Vorteile</h1>}
      <Container>
        <Row className="justify-content-center mb-3">
          {badges.map((badge) => (
            <BadgeCard key={badge.title} badge={badge} />
          ))}
        </Row>

        <Row className="d-block d-md-none">
          <hr className={styles.benefitsHr} />
        </Row>
      </Container>
    </div>
  );
}
