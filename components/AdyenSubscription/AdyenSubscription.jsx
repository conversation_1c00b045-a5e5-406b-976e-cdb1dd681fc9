import { useState, useEffect, useCallback } from 'react';
import { Container } from 'reactstrap';
import { Row, Col } from 'reactstrap/lib';
import { AiOutlineMinus } from 'react-icons/ai';
import { GrAdd } from 'react-icons/gr';
import { CartItem, LoginModal } from 'components';
import AdyenPayment, {
  badAdyenResultCodes,
} from 'components/AdyenPayment/AdyenPayment';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import * as adyenService from 'services/adyen.service';
import * as subscriptionService from 'services/subscription.service';
import * as consumerService from 'services/consumer.service';
import { isAuth } from 'services/auth.service';

import FaqPayment from './FaqPayment';

import styles from './AdyenSubscription.module.scss';

const handleProductPackageError = (error) => {
  const errorMessage = error.response?.data;
  if (!errorMessage.startsWith('Consumer has not selected Product Package')) {
    addMessage(errorMessage, 'error');
  }
};

const canWeSkipPayment = (cart) =>
  cart.length &&
  !cart.find((item) => item.initialAmount !== 0 || item.recurring);

const AdyenSubscription = ({
  selectedProductPackage,
  onClose,
  isSettingsPage = false,
  onPaymentSuccess = () => {},
  onPaymentError = () => {},
  children,
}) => {
  const [shoppingCart, setShoppingCart] = useState([]);
  const [paymentNeeded, setPaymentNeeded] = useState(true);
  const [showAdditionalInfo, setShowAdditionalInfo] = useState(false);
  const { addMessage } = useAPIError();

  useEffect(() => {
    if (selectedProductPackage.registrationCode) {
      subscriptionService
        .getProductPackageForDiscountCode(
          selectedProductPackage.registrationCode
        )
        .then((response) => setShoppingCart([response.data]))
        .catch(handleProductPackageError);
    } else if (selectedProductPackage.productPackageId) {
      adyenService
        .getSubscriptionPackageById(selectedProductPackage.productPackageId)
        .then((response) => setShoppingCart([response.data]))
        .catch(handleProductPackageError);
    } else {
      setShoppingCart([]);
    }
  }, [selectedProductPackage]);

  useEffect(() => {
    setPaymentNeeded(!canWeSkipPayment(shoppingCart));
  }, [shoppingCart]);

  const preSubmitPayment = useCallback(() => {
    if (isSettingsPage) {
      if (selectedProductPackage.registrationCode) {
        return consumerService.setUserRegistrationCode(
          selectedProductPackage.registrationCode
        );
      } else if (selectedProductPackage.productPackageId) {
        return consumerService.setUserProductPackage(
          selectedProductPackage.productPackageId
        );
      }
    }
  }, []);

  const handleAdyenResponse = (paymentRes, error) => {
    if (!error && !badAdyenResultCodes.includes(paymentRes.resultCode)) {
      setTimeout(() => onPaymentSuccess(), 1000);
    } else {
      onPaymentError();
    }
  };

  return (
    <Container>
      <div className="form-group">
        {!paymentNeeded ? (
          <p className="inline-info">Kostenlose Mitgliedschaft</p>
        ) : null}
      </div>
      {isAuth() ? (
        paymentNeeded &&
        (shoppingCart?.length &&
        (shoppingCart[0]?.recurring ||
          parseInt(shoppingCart[0]?.initialAmount) !== 0) ? (
          <>
            <AdyenPayment
              shoppingCart={shoppingCart}
              preSubmitAction={preSubmitPayment}
              handleAdyenResponse={handleAdyenResponse}
              CartItem={CartItem}
              productType="PACKAGE"
            />
            <Container>
              {shoppingCart[0]?.recurring &&
              parseInt(shoppingCart[0]?.initialAmount) === 0 ? (
                <>
                  <Row className="mt-3 mb-3 flex-row-reverse">
                    <Col xs={6}>
                      <Row className="mt-5 align-center">
                        <Col xs={11} className="p-0">
                          <p className={styles.additionalInfo}>
                            Warum muss ich meine Zahlungsdetails angeben, obwohl
                            ich einen Registrierungscode für eine kostenlose
                            Mitgliedschaft habe?
                          </p>
                        </Col>
                        <Col
                          xs={1}
                          onMouseEnter={() => setShowAdditionalInfo(true)}
                          onMouseLeave={() => setShowAdditionalInfo(false)}
                        >
                          {showAdditionalInfo ? (
                            <AiOutlineMinus size={35}></AiOutlineMinus>
                          ) : (
                            <GrAdd size={35}></GrAdd>
                          )}
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <Container>
                    {showAdditionalInfo ? (
                      <Row className="flex-row-reverse">
                        <Col xs={6} className="p-0">
                          <p className="text-s">
                            Auch wenn zunächst keine Zahlung erforderlich ist,
                            benötigen wir zu Beginn einer kostenlosen
                            Mitgliedschaft eine gültige Zahlungsmethode. Eine
                            Abbuchung erfolgt jedoch nur, wenn sich deine
                            Mitgliedschaft automatisch verlängert.
                          </p>
                        </Col>
                      </Row>
                    ) : null}
                  </Container>
                </>
              ) : null}
            </Container>
          </>
        ) : null)
      ) : (
        <LoginModal />
      )}

      {children}

      <Container className="border p-4 mt-3 mb-3 desktop-only-label">
        <p className={styles.faq}>Häufig gestellte Fragen</p>
        <hr />
        <Row className="p-3">
          {FaqPayment.map((section) => (
            <Col>
              <Row className="pl-2 pr-2">
                <p className={styles.faqHeadline}>{section.title}</p>
              </Row>
              <Row className="pl-2 pr-2">
                <p className={styles.faqText}>{section.text}</p>
              </Row>
            </Col>
          ))}
        </Row>
      </Container>
      {isSettingsPage && (
        <button
          className="btn btn-info float-left fb-subs-btn"
          onClick={onClose}
        >
          abbrechen
        </button>
      )}

      {!paymentNeeded ? (
        <button
          className="btn btn-info float-right fb-subs-btn"
          onClick={async () => {
            try {
              await preSubmitPayment();
              onPaymentSuccess();
            } catch (error) {
              addMessage(
                error?.response?.data ||
                  'Error while executing pre-submit action',
                'error'
              );
            }
          }}
        >
          Abonnement beginnen
        </button>
      ) : null}
    </Container>
  );
};

export default AdyenSubscription;
