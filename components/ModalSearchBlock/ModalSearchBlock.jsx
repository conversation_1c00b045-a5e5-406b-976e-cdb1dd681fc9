import { useRef, useState } from 'react';

import SearchIcon from '../CustomIcons/SearchIcon';
import XIcon from '../CustomIcons/XIcon';

import styles from './ModalSearchBlock.module.scss';
import { ModalGeneric } from '../index';

const ModalSearchBlock = ({
  className = '',
  searchText,
  handleChange = (f) => f,
}) => {
  const [modal, setModal] = useState(false);
  const searchInput = useRef();
  const handleSubmit = (e) => {
    e.preventDefault();
    handleChange(searchInput.current?.value);
    setModal(false);
  };

  return (
    <>
      <div className={`${styles.modalSearchBlock} ${className}`}>
        <SearchIcon
          onClick={() => setModal(true)}
          className={styles.searchBtn}
          height="22px"
        ></SearchIcon>
      </div>
      <ModalGeneric modal={modal} centered={true}>
        <XIcon
          className="float-right text-m"
          onClick={() => setModal(false)}
        ></XIcon>
        <h2 className="sidebar-header text-center">Suche</h2>

        <form onSubmit={handleSubmit}>
          <div className="input-group">
            <input
              ref={searchInput}
              type="text"
              defaultValue={searchText}
              className="form-control"
              placeholder="Suchbegriff"
              aria-label="Suchbegriff"
              aria-describedby="basic-addon2"
            />
          </div>
          <button className="btn btn-dark mt-4 float-right">suchen</button>
        </form>
      </ModalGeneric>
    </>
  );
};
export default ModalSearchBlock;