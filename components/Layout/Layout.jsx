import dynamic from 'next/dynamic';
import Head from 'next/head';
import Link from 'next/link';
import { useDispatch, useSelector } from 'react-redux';
import * as SettingsActions from 'redux-store/settings/actions';
import Navigation from '../Navigation/Navigation';
import Header from '../Header/Header';
import Footer from '../Footer/Footer';
import styles from './Layout.module.scss';
import MainAppContext from 'context/mainAppContext';
import * as CategoryService from 'services/categories.service';
import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { getSubscription } from 'services/subscription.service';
import { isAuth } from 'services/auth.service';

const NoSubscriptionModal = dynamic(() => import('../NoSubscriptionModal/NoSubscriptionModal'));

const Layout = ({ children, home, customContainerFluidClass, hideFooter=false }) => {
  const [categories, setCategories] = useState(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showSubscribeModal, setShowSubscribeModal] = useState(false);
  const [productImageUrl, setProductImageUrl] = useState();

  const useCustomTheme = useSelector(
    (state) => state.settings.useCustomTheme
  );
  const useCustomColors = useSelector(
    (state) => state.settings.useCustomColors
  );

  const dispatch = useDispatch();

  const getCategories = async () => {
    return setCategories(
      await CategoryService.getCategories({ pageSize: 120 })
    );
  };

  useEffect(() => {
    getCategories();
    if (localStorage.getItem('showSubscribeModal') === 'true') {
      setShowSubscribeModal(true);
      localStorage.setItem('showSubscribeModal', false);
    }

    if (isAuth()) {
      dispatch(SettingsActions.getProductPackage());
    }

    return () => {};
  }, []);

  useEffect(() => {
    if (!isAuth()) {
      return;
    }

    getSubscription()
      .then(({ data }) => {
        const { productPackage, status } = data;
        if (productPackage) {
          const { corporate, imageLink, useCustomTheme } = productPackage;
          if (
            (!corporate && !useCustomTheme) ||
            !imageLink ||
            status !== 'ACTIVE'
          ) {
            return;
          }

          setProductImageUrl(imageLink);
        }
      })
      .catch(() => {
        setProductImageUrl(null);
      });
  }, [setProductImageUrl]);

  const hasSubscriptionLogo = !!productImageUrl;

  return (
    categories && (
      <MainAppContext.Provider
        value={{
          categories,
          showLoginModal,
          setShowLoginModal,
          showSubscribeModal,
          setShowSubscribeModal,
        }}
      >
        <div className={`container-fluid ${customContainerFluidClass || ''}`}>
          <Head>
            <link rel="icon" href="/favicon.ico" />
          </Head>
          <Header>
            <Navigation
              categories={categories}
              showSoFunktioniertsForNonCustomer={!isAuth()}
            ></Navigation>

            <Link href="/" legacyBehavior>
              <div
                className={`${styles.logosContainer} ${
                  hasSubscriptionLogo ? 'has-collaboration-partner-logo' : ''
                }`}
              >
                {(!useCustomTheme || !hasSubscriptionLogo) && (
                  <img
                    className={`${styles.mainLogo} captain-coupon-logo`}
                    src="/CaptainCoupon_logo.svg"
                    alt="Auf CaptainCoupon findest du nur gültige Gutscheincodes der besten Online-Shops & Marken! Jetzt Gratis-Mitgliedschaft sichern!"
                  />
                )}

                {hasSubscriptionLogo && (
                  <>
                    {!useCustomTheme && (
                      <span className={styles.collaborationSymbol}>&#215;</span>
                    )}
                    <img
                      className={styles.mainLogo}
                      src={productImageUrl}
                      alt="Logo des Anbieters dieses Coupon-Programms"
                    />
                  </>
                )}
              </div>
            </Link>
          </Header>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ ease: 'backInOut', duration: 0.6 }}
          >
            <main className="main-page-content">{children}</main>
          </motion.div>
          <NoSubscriptionModal />
          {!hideFooter && <Footer></Footer>}
        </div>
      </MainAppContext.Provider>
    )
  );
};

export default Layout;