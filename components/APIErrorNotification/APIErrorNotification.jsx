import useAPIError from './useAPIError';
import React, { useEffect, useState } from 'react';

import Snackbar from '../Snackbar/Snackbar';

export const APIErrorNotification = () => {
  const { error, removeError } = useAPIError();

  const [snackOpen, setSnack] = useState(false);

  useEffect(() => {
    if (error && error.message) {
      setSnack(true);
    }
    return () => {};
  }, [error]);

  return (
    <Snackbar
      open={snackOpen}
      status={error?.status}
      message={error?.message}
      autoHideDuration={3000}
      onClose={() => {
        setSnack(false);
        removeError();
      }}
    />
  );
};
