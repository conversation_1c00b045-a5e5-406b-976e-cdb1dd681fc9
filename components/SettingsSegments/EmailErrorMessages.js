export const EMAIL_EXISTS_en = 'Email already exists';
export const EMAIL_EXISTS_de =
  'Diese E-Mail Adresse wurde bereits hinzugefügt.';

export const CONSUMER_NOT_FOUND_en = 'Consumer not found';
export const CONSUMER_NOT_FOUND_de =
  'Leider ist uns dieser User nicht bekannt.';

export const NOT_SUPPORTED_OPERATION_en =
  'Email operations are only supported for regular users';
export const NOT_SUPPORTED_OPERATION_de =
  'Um E-Mail Adressen hinzuzufügen oder zu ändern musst du dich auf unserer Website registrieren.';

export const EMAIL_NOT_FOUND_en = 'Email not found';
export const EMAIL_NOT_FOUND_de =
  'Die E-Mail Adresse konnte nicht gefunden werden.';

export const CANNOT_REMOVE_PRIMARY_EMAIL_en = 'Cannot remove primary email';
export const CANNOT_REMOVE_PRIMARY_EMAIL_de =
  'Die primäre E-Mail Adresse kann nicht entfernt werden.';

export const CANNOT_REMOVE_FACEBOOK_EMAIL_en =
  'Cannot remove email associated with facebook account';
export const CANNOT_REMOVE_FACEBOOK_EMAIL_de =
  'Diese E-Mail Adresse ist mit deinem Facebook-Profil verbunden und kann daher leider nicht gelöscht werden.';

export const EMAIL_NOT_CONFIRMED_en = 'The email is not confirmed';
export const EMAIL_NOT_CONFIRMED_de =
  'Die E-Mail Adresse wurde noch nicht bestätigt.';

export const EMAIL_ALREADY_PRIMARY_en = 'Email is already primary';
export const EMAIL_ALREADY_PRIMARY_de =
  'Diese E-Mail Adresse ist bereits als primäre E-Mail Adresse hinterlegt.';
