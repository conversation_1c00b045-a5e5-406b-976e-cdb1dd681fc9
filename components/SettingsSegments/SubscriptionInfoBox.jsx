import React from 'react';
import { SET_CANCEL_SUBSCRIPTION_MODAL } from 'redux-store/settings/types';
import { subscriptionEndDateMessage } from 'util/helpers';
import { Loading } from 'components';

const SubscriptionInfoBox = ({
  setStartSubModal,
  subscriptionProcessing,
  subscriptionData,
  hasValidSub,
  dispatch,
}) => {
  const productPackage = subscriptionData?.productPackage;

  return (
    <div className="gray-box first-row col-sm-12 subscription-info-box">
      <div className="full-height-container d-flex flex-column h-10">
        {subscriptionProcessing ? (
          <Loading className="blockLoading" />
        ) : (
          <div className="row">
            <div className="col-sm-12">
              <b className="box-title">Mitgliedschaft</b>
            </div>
          </div>
        )}
        {!subscriptionData?.status ? (
          <>
            <div className="row">
              <div className="col-sm-12">
                <img
                  src="/icons/question-mark.svg"
                  className="no-subs-icon"
                  alt="Türkises Fragezeichen in einem türkisen Kreis"
                />
              </div>
              <div className="col-sm-12">
                <b className="no-subscription-note">
                  Aktuell liegt keine aktive Mitgliedschaft vor.
                </b>
              </div>
            </div>
            <div className="row mt-auto d-none">
              <div className="col-sm-12 text-right">
                <button
                  type="button"
                  className="btn btn-info group-button "
                  onClick={() => setStartSubModal(true)}
                >
                  Mitgliedschaft starten
                </button>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="row">
              {subscriptionData?.status !== 'EXPIRED' && (
                <div className="col-sm-12">
                  {productPackage?.corporate && (
                    <p className="font-weight-bold">Powered by</p>
                  )}
                  <img
                    src={`${productPackage?.imageLink}`}
                    className="subs-icon mt-3 mb-3"
                    alt="Logo des ausgewählten Pakets"
                  />
                </div>
              )}
              <div className="col-sm-12">
                <b className="subscriptionTitle">{productPackage?.title}</b>
                {productPackage?.subtitle && <p>{productPackage?.subtitle}</p>}
                {productPackage?.label && <p>{productPackage?.label}</p>}

                {subscriptionData?.status ? (
                  <p>{subscriptionEndDateMessage(subscriptionData)}</p>
                ) : null}

                {productPackage?.additionalInformation && (
                  <p className="additional-info-footnote">
                    {productPackage?.additionalInformation}
                  </p>
                )}
              </div>
            </div>
            {subscriptionData?.status === 'EXPIRED' ? (
              <div className="row mt-auto">
                <div className="col-sm-12 text-right">
                  <button
                    type="button"
                    className="btn btn-info group-button "
                    onClick={() => setStartSubModal(true)}
                  >
                    Mitgliedschaft starten
                  </button>
                </div>
              </div>
            ) : null}
          </>
        )}
        {hasValidSub ? (
          <>
            <div className="row buttons-wrapper mt-4">
              <div className="col-12 col-sm-12 col-lg-6 group-btn-wrap-left">
                <button
                  type="button"
                  className="btn btn-dark group-button large-btn"
                  onClick={() => {
                    dispatch({
                      type: SET_CANCEL_SUBSCRIPTION_MODAL,
                      payload: true,
                    });
                  }}
                >
                  Mitgliedschaft beenden
                </button>
              </div>
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default SubscriptionInfoBox;
