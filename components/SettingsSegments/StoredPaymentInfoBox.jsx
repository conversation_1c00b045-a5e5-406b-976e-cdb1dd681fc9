const StoredPaymentInfoBox = ({ hasValidSub, storedPayment }) =>
  hasValidSub ? (
    storedPayment?.storedPaymentMethods?.length ? (
      <div className="gray-box col-sm-12 mar-t-2 second-row payment-method-card">
        <div className="full-height-container d-flex flex-column h-10">
          <b>Zahlungsmethode</b>
          {storedPayment.storedPaymentMethods[0].type === 'scheme' ? (
            <>
              <div className="payment-card">
                Kartennummer - ∗∗∗∗ ∗∗∗∗ ∗∗∗∗{' '}
                {storedPayment.storedPaymentMethods[0].lastFour}
              </div>
              <img
                src={`logos/${
                  storedPayment.storedPaymentMethods[0].brand.indexOf(
                    'applepay'
                  ) === -1
                    ? storedPayment.storedPaymentMethods[0].brand
                    : 'applepay'
                }.png`}
                alt="Das Logo eines Zahlungsanbieters"
                className="payment-img"
              />
            </>
          ) : storedPayment.storedPaymentMethods[0].type ===
            'sepadirectdebit' ? (
            <img
              src="logos/sepa.png"
              alt="SEPA-LOGO: Kugelschreiber, Kreditkarte und Papier sowie der Schriftzug „SEPA Lastschrift“"
              className="payment-img"
            />
          ) : (
            <img
              src={`logos/${storedPayment.storedPaymentMethods[0].type}.png`}
              alt={`${storedPayment.storedPaymentMethods[0].type}-payment`} // TODO
              className="payment-img"
            />
          )}
          {/* TODO - comment out later when edit is needed */}
          {/* <div className="row mt-auto">
        <div className="col-sm-12 text-right">
          <button
            type="button"
            className="btn btn-dark group-button"
            onClick={() => setViewMode('paymentMethodEdit')}
          >
            bearbeiten
          </button>
        </div>
      </div> */}
        </div>
      </div>
    ) : (
      <div className="gray-box col-sm-12 mar-t-2 second-row payment-method-card white-box-override" />
    )
  ) : null;

export default StoredPaymentInfoBox;
