import { useState, useEffect } from 'react';
import { Container } from 'reactstrap';
import { Row } from 'reactstrap/lib';

import { AdyenSubscription, PackageSelect } from 'components';

const SubscriptionPaymentDialog = ({
  consumer,
  onSuccess,
  onClose,
  onPaymentError,
}) => {
  const [productPackageData, setProductPackageData] = useState(null);
  const [prodPackageAdditionalInfo, setProdPackageAdditionalInfo] = useState();

  useEffect(() => {
    const { productPackage, registrationCode } = consumer;
    setProductPackageData({
      productPackageId: productPackage?.productPackageId,
      registrationCode: registrationCode,
    });
  }, [consumer]);

  return productPackageData ? (
    <>
      <PackageSelect
        selected={productPackageData}
        onSelect={(data) => setProductPackageData({ ...data })}
        setProductInfo={setProdPackageAdditionalInfo}
      />
      <AdyenSubscription
        key={`${productPackageData.registrationCode} / ${productPackageData.productPackageId}`}
        isSettingsPage={true}
        selectedProductPackage={productPackageData}
        onPaymentSuccess={onSuccess}
        onPaymentError={onPaymentError}
        onClose={onClose}
      >
        <Container className="mt-5 mb-5">
          {prodPackageAdditionalInfo?.map((productPackage, i) => (
            <Row key={`${i}-${productPackage.info || ''}`}>
              <p>{productPackage.info}</p>
            </Row>
          ))}
        </Container>
      </AdyenSubscription>
    </>
  ) : null;
};

export default SubscriptionPaymentDialog;
