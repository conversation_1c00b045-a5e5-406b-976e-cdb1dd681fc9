import React from 'react';
import { SET_SHOW_EDIT_USER_FORM } from 'redux-store/settings/types';

import { RegisterForm } from 'components';

const UserInfoBox = ({
  userData,
  processChildForm,
  getParsedDate,
  hasValidSub,
  showEditUserForm,
  dispatch,
}) => {
  return (
    <div
      className={`col-sm-12 gray-box gray-box-settings first-row ${
        showEditUserForm && 'edit-user-form'
      }`}
    >
      <div className="full-height-container d-flex flex-column h-10">
        <div className="row">
          <div className="col-sm-12">
            <div>
              <b>Kunden Nr.</b>
            </div>
            {userData?.consumerId && (
              <div>
                <p> {'21' + userData?.consumerId}</p>
              </div>
            )}
          </div>
        </div>

        {showEditUserForm && (
          <RegisterForm
            actionOnSubmit={processChildForm}
            formData={userData}
            submitButtonLabel="speichern"
            settingsPage="true"
          />
        )}

        {!showEditUserForm && (
          <>
            <div className="row mt-3">
              <div className="col-sm-12">
                <b>Kontakt</b>
                <p>
                  {userData?.gender === 'male'
                    ? 'Herr'
                    : userData?.gender === 'female'
                    ? 'Frau'
                    : ''}{' '}
                  {/* TODO To avoid these multiple conditions create a small method in this component that takes the gender as an input and returns the proper string. */}
                  {userData?.surname} {userData?.firstName}
                  {userData?.street && userData?.houseNumber && (
                    <>
                      <br /> {userData?.street} {userData?.houseNumber}
                    </>
                  )}
                  {userData?.postalCode && userData?.place && (
                    <>
                      <br /> {userData?.postalCode} {userData?.place}
                    </>
                  )}
                  {/* TODO Avoid using <br /> in the code and use CSS instead to create the spacing. */}
                </p>
              </div>
            </div>
            <div className="row buttons-wrapper d-flex mt-3">
              <div className="col-sm-12 col-lg-6">
                <b>Geburtstag</b>
                {userData?.dateOfBirth && (
                  <p>{getParsedDate(userData.dateOfBirth)}</p>
                )}
              </div>
              <div className="col-sm-12 col-lg-6 text-right mt-auto user-infobox-edit-btn-wrapper">
                <button
                  type="button"
                  className="btn btn-dark group-button"
                  onClick={() => {
                    localStorage.setItem(
                      'selectedPayment',
                      hasValidSub ? userData?.selectedPayment : ''
                    );
                    // TODO check logic when working on subscription
                    // If you plan to write additional code here (as indicated by the TODO comment)
                    // please create a separate method for this onClick action.
                    dispatch({
                      type: SET_SHOW_EDIT_USER_FORM,
                      payload: true,
                    });
                  }}
                >
                  bearbeiten
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default UserInfoBox;
