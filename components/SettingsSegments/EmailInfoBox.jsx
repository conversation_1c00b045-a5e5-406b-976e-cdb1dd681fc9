import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';

import {
  hasLowerCase,
  hasSpec<PERSON><PERSON>haracter,
  setErrorMessageForEditEmail,
} from 'util/helpers';
import * as ConsumerService from 'services/consumer.service';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { EditEmailModal } from 'components';

const EmailInfoBox = ({ userData, updateUserEmails }) => {
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userEmails, setUserEmails] = useState(userData?.emails || []);
  const [isMakePrimaryModalOpen, setIsMakePrimaryModalOpen] = useState(false);
  const [emailToEdit, setEmailToEdit] = useState('');

  const [password, setThePassword] = useState('');
  const { addMessage } = useAPIError();

  const { register, errors, handleSubmit, setError } = useForm({
    defaultValues: { email: '', password: '', confirmPass: '' },
    mode: 'onBlur',
    reValidateMode: 'onBlur',
  });

  const getUniqueUserEmails = () => {
    function onlyUniqueEmails(value, index, array) {
      const indexOfFirstOccurence = array.findIndex(
        (item) => item.email === value.email
      );
      if (indexOfFirstOccurence === index) {
        return true;
      } else {
        if (!array[indexOfFirstOccurence].primaryEmail) {
          array[indexOfFirstOccurence].primaryEmail = array[index].primaryEmail;
        }
        return false;
      }
    }

    return userData?.emails?.filter(onlyUniqueEmails);
  };

  useEffect(() => {
    const mailsToShow = getUniqueUserEmails();
    setUserEmails(mailsToShow);
  }, [userData]);

  const handleAddEmail = async (data) => {
    try {
      await ConsumerService.addEmail(data);
      updateUserEmails();
      setShowEmailForm(false);
      addMessage(
        'Die E-Mail Adresse wurde erfolgreich hinzugefügt.',
        'success'
      );
    } catch (error) {
      setErrorMessageForEditEmail(
        error.response?.data || error.message,
        setError,
        addMessage
      );
    }
  };

  const deleteEmail = async () => {
    try {
      await ConsumerService.deleteEmail(emailToEdit);
      updateUserEmails();
      setShowEmailForm(false);
      setIsDeleteModalOpen(false);
      addMessage(`Die E-Mail Adresse wurde erfolgreich gelöscht.`, 'success');
    } catch (error) {
      setErrorMessageForEditEmail(
        error.response?.data || error.message,
        setError,
        addMessage
      );
    }
  };

  const makeEmailPrimary = async () => {
    try {
      await ConsumerService.makeEmailPrimary(emailToEdit);
      updateUserEmails();
      setShowEmailForm(false);
      setIsMakePrimaryModalOpen(false);
      addMessage(
        `Die E-Mail Adresse ${emailToEdit} ist nun deine Primäre E-Mail Adresse.`,
        'success'
      );
    } catch (error) {
      setErrorMessageForEditEmail(
        error.response?.data || error.message,
        setError,
        addMessage
      );
    }
  };

  const onDeleteEmailClicked = (email) => {
    setIsDeleteModalOpen(true);
    setEmailToEdit(email);
  };

  const onMakePrimaryEmailClicked = (email) => {
    setIsMakePrimaryModalOpen(true);
    setEmailToEdit(email);
  };

  return (
    <div
      className={`gray-box col-sm-12 mar-t-2 ${!showEmailForm && 'second-row'}`}
    >
      <EditEmailModal
        isOpen={isDeleteModalOpen}
        onConfirm={deleteEmail}
        setIsModalOpen={setIsDeleteModalOpen}
        confirmBtnLabel="löschen"
        cancelBtnLabel="abbrechen"
        modalContent="wirklich löschen"
        emailToEdit={emailToEdit}
      />

      <EditEmailModal
        isOpen={isMakePrimaryModalOpen}
        onConfirm={makeEmailPrimary}
        setIsModalOpen={setIsMakePrimaryModalOpen}
        confirmBtnLabel="ja"
        cancelBtnLabel="abbrechen"
        modalContent="wirklich als Haupt-E-Mail-Adresse nutzenn"
        emailToEdit={emailToEdit}
      />

      <div className="full-height-container d-flex flex-column">
        <div className="row">
          <div className="col-sm-12">
            <b className="box-title">E-Mail Adresse</b>
          </div>
        </div>
        {userEmails?.map((email, index) => (
          <div className="row" key={index}>
            <div className="col-9 email-wrapper">
              <p className={`${email?.primaryEmail && 'font-weight-bold'}`}>
                {email?.email}
              </p>
              {email?.primaryEmail && (
                <small>Dies ist dein primäre E-Mail Adresse</small>
              )}
            </div>
            <div className="col-3 edit-email-btn-wrapper">
              {!showEmailForm && email?.primaryEmail && (
                <div className="primary-email-check">
                  <img
                    src="/icons/circle-check-hover.svg"
                    alt="Symbol eines weißen Hakens auf einem grünen Kreis"
                  />
                </div>
              )}
              {showEmailForm && !email?.primaryEmail && (
                <div>
                  <button
                    className="btn make-primary-email-btn"
                    onClick={() => onMakePrimaryEmailClicked(email.email)}
                  >
                    <img
                      src="/icons/circle-check.svg"
                      alt="Ausgegrautes Symbol eines weißen Hakens auf einem Kreis"
                    />
                  </button>
                  <button
                    className="btn delete-email-btn"
                    onClick={() => onDeleteEmailClicked(email.email)}
                  >
                    <img
                      src="/modal-icons/cancel-modal-2.svg"
                      alt="„X“- oder Kreuz-Symbol"
                    />
                  </button>
                </div>
              )}
            </div>
          </div>
        ))}

        {showEmailForm ? (
          <div className="row mar-t-2">
            <form className="w-100" onSubmit={handleSubmit(handleAddEmail)}>
              <div className="col-sm-12">
                <div className="form-group mar-b-4">
                  <input
                    type="email"
                    autoComplete="new-password"
                    className="form-control"
                    placeholder="Neue E-Mail Adresse hinzufügen"
                    name="email"
                    ref={register({
                      maxLength: {
                        value: 64,
                        message: 'Die maximale Zeichenanzahl beträgt 64',
                      },
                    })}
                  />
                  {errors.email && (
                    <p className="inline-error pad-t-1">
                      {errors.email.message}
                    </p>
                  )}
                </div>
                <div className="form-group mar-b-3">
                  <b>Passwort</b>
                  <input
                    type="password"
                    autoComplete="new-password"
                    className="form-control"
                    placeholder="Passwort"
                    name="password"
                    onChange={(e) => setThePassword(e.target.value)}
                    ref={register({
                      maxLength: {
                        value: 16,
                        message: 'Die maximale Zeichenanzahl beträgt 16',
                      },
                      minLength: {
                        value: 8,
                        message:
                          'Bitte wähle ein Passwort, das aus mindestens acht Zeichen besteht',
                      },
                      validate: {
                        testSpecialCharacter: hasSpecialCharacter,
                        testLowerCase: hasLowerCase,
                      },
                    })}
                  />
                  {errors.password && (
                    <p className="inline-error pad-t-1">
                      {errors.password.message}
                      {errors.password.type === 'testSpecialCharacter'
                        ? 'Muss mindestens ein Sonderzeichen enthalten.'
                        : errors.password.type === 'testLowerCase' &&
                          'Muss mindestens einen Kleinbuchstaben haben.'}
                    </p>
                  )}
                </div>
                <div className="form-group">
                  <input
                    type="password"
                    autoComplete="new-password"
                    className="form-control"
                    placeholder="Passwort wiederholen"
                    name="confirmPass"
                    ref={register({
                      validate: (value) => value === password,
                    })}
                  />
                  {errors.confirmPass && (
                    <p className="inline-error pad-t-1">
                      Das Passwort stimmt nicht überein
                    </p>
                  )}
                </div>
                <div className="row mt-auto edit-email-buttons-wraper">
                  <div className="col-6 col-sm-6 text-left">
                    <button
                      type="button"
                      className="btn btn-outline-dark group-button"
                      onClick={() => setShowEmailForm(false)}
                    >
                      abbrechen
                    </button>
                  </div>
                  <div className="col-6 col-sm-6 text-right">
                    <button type="submit" className="btn btn-dark group-button">
                      speichern
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        ) : (
          <div className="row mt-3">
            <div className="col-sm-12 text-right">
              <button
                className="btn btn-dark group-button"
                onClick={() => setShowEmailForm(true)}
              >
                bearbeiten
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailInfoBox;
