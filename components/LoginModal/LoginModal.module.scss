@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.soFunktioniertsLink {
  font-size: 13px;
  white-space: nowrap;
  padding: 10px 20px;
  text-decoration: none;
  color: hsl(0, 0%, 0%);
  &:hover {
    text-decoration: none;
    color: #000;
    font-weight: bold;
    cursor: pointer;
  }
}

.loginModal {
  :global(.close) {
    position: absolute;
    right: 10px;
    top: 10px;
    font-size: 18px;
    z-index: 5;
    height: auto;
  }
  :global(.input-group-text) {
    background: #fff;
    border: 1px solid #000;
    margin-left: -1px;
    border-left: 0;
    position: relative;
    font-size: 22px;
  }
  &.loginModalVisible {
    display: flex;
    flex-wrap: nowrap;
    margin-right: auto;
    margin-left: auto;
    :global(.modal-body) {
      padding: 0;
      width: 100%;
    }
    :global(.modal-content) {
      border: none;
    }
    :global(.loginModalImage) {
      height: 100%;
      width: 100%;
    }
    :global(.loginModalImageWrapper) {
      padding-left: 0px;
      padding-right: 0px;
    }
    :global(.sidebar-header) {
      padding: 0 0 35px;
      margin-bottom: 0;
    }
    :global(.loginModalContent) {
      padding: 35px;
    }
  }

  :global(.passwortVergessen) {
    margin-top: auto;
    margin-bottom: auto;
  }
  :global(.form-group) {
    margin-bottom: 20px;
  }
  :global(.link-dark) {
    text-decoration: none;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  :global(button) {
    height: 50px;
    padding: 0;
    font-weight: bold;
  }
  :global(.alert) {
    margin: 30px 15px 0px;
  }
  :global(.modal-body) {
    padding: 35px;
  }
  :global(.forgotPasswordModal) {
    padding: 15px 0;
    :global(.sidebar-header) {
      font-size: 36px;
      line-height: 40px;
      margin-bottom: 30px;
    }
  }
}
.loginModalVisible {
  :global(.section-header) {
    font-size: 30px;
    line-height: 40px;
  }
  :global(.coupon-modal-content-section) {
    padding: 40px 50px 30px;
  }
}
.logoutIcon {
  display: none;
}

.alert {
  :global(.close) {
    font-size: 14px;
    position: absolute;
    right: 2px;
    top: 2px;
  }
}

@include media-breakpoint-down(xs) {
  .Header {
    :global(.btn.btn-dark) {
      margin-right: 15px;
      padding: 5px 8px;
    }
  }
  .logoutIcon {
    display: block;
    font-size: 28px;
  }
  .logoutLabel {
    display: none;
  }
  .modal-body {
    padding: 20px;
  }
}

@include media-breakpoint-up(md) {
  .float-right {
    float: right;
  }
}

@include media-breakpoint-down(md) {
  .soFunktioniertsLink {
    display: none;
  }
}