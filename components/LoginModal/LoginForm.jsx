import { InputGroup, InputGroupText } from 'reactstrap';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { AiOutlineEye, AiOutlineEyeInvisible } from 'react-icons/ai/index';

import { signIn, storeUserData } from 'services/auth.service';
import * as DisplayStates from './DisplayStates';
import { Loading } from 'components';
import * as ERROR_CODES from './ErrorCodes';

const LoginForm = ({
  setCurrentDisplay,
  setLoginStatus,
  setModal,
  setUsername,
  children,
  showLoginModal,
}) => {
  const { register, handleSubmit, formState: { errors } } = useForm();
  const [showPass, setShowPass] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const hideRegister = localStorage.getItem('hideRegister');
  const firstLogin = localStorage.getItem('firstLogin');

  const type = showPass ? 'text' : 'password';
  const eyeIcon = showPass ? (
    <AiOutlineEye
      onClick={() => setShowPass(!showPass)}
      data-cy="togglePasswordVisibility"
    ></AiOutlineEye>
  ) : (
    <AiOutlineEyeInvisible
      data-cy="togglePasswordVisibility"
      onClick={() => setShowPass(!showPass)}
    ></AiOutlineEyeInvisible>
  );

  const loginSuccess = (user) => {
    storeUserData(user, ({ justLoggedIn, subscribed }) => {
      if (!subscribed && !firstLogin) {
        localStorage.setItem('showSubscribeModal', true);
      }

      location.reload();
      return;
    });
  };

  const handleLoginSubmit = async (data) => {
    setLoading(true);
    const { username, password } = data;
    setUsername(username);
    try {
      const response = await signIn(username, password);
      loginSuccess(response.data);
    } catch (error) {
      switch (error?.response?.data) {
        case 'The user does not exist':
          setLoginStatus(ERROR_CODES.UserNotFoundException);
          break;
        case 'The user is not confirmed':
          setLoginStatus(ERROR_CODES.UserNotConfirmedException);
          break;
        case 'Incorrect username or password.':
          setLoginStatus(ERROR_CODES.NotAuthorizedException);
          break;
        default:
          setLoginStatus(error);
          break;
      }
    }
    setLoading(false);
  };

  const isUserOnRegistrationPage =
    typeof window !== 'undefined' && window.location.pathname.indexOf('/register') !== -1;

  return (
    <div className="container-fluid">
      <div className="row">
        {/* NOTE: A NEW IMAGE IS PLANNED HERE, FOR THE MOMENT NONE IS NEEDED */}
        {/* {showLoginModal && (
          <div className="col-12 col-sm-12 col-md-12 col-lg-6 col-xl-6 loginModalImageWrapper">
            <picture>
              <source
                media="(max-width:414px)"
                srcSet="/headers/CaptainCoupon_Lightbox_Registration.gif"
              />
              <source
                media="(min-width:415px) and (max-width:991px)"
                srcSet="/headers/CaptainCoupon_Lightbox_Registration_Mobile_New.gif"
              />
              <source
                media="(min-width:992px)"
                srcSet="/headers/CaptainCoupon_Lightbox_Registration.gif"
              />
              <img src="" className="loginModalImage" />
            </picture>
          </div>
        )} */}
        <div
          className={`${
            showLoginModal ? 'col-12 loginModalContent' : 'col-12'
          } `}
        >
          {showLoginModal ? (
            <h1 className="login-header col-sm-12 text-left">
              <b style={{ marginBottom: '8px' }}>
                Uups – Das ist ein exklusiver Gutscheincode für CC+ Mitglieder.
              </b>
              <br />
              Registriere oder logge dich ein um Zugriff auf alle Gutscheincodes
              zu erhalten.
            </h1>
          ) : (
            <h1 className="sidebar-header col-sm-12 text-center mar-b-3">
              Anmelden
            </h1>
          )}

          <form
            onSubmit={handleSubmit(handleLoginSubmit)}
            className="login-modal-form "
          >
            <div className="row">
              <div className="col-sm-12">
                <div className="form-group">
                  <input
                    type="email"
                    name="username"
                    className="form-control"
                    placeholder="E-Mail-Adresse"
                    {...register('username', {
                      required: true,
                    })}
                    data-cy="loginEmail"
                  />
                  {errors.username && (
                    <p className="inline-error">Dies ist ein Pflichtfeld</p>
                  )}
                </div>
              </div>
              <div className="col-sm-12">
                <div className="form-group">
                  <InputGroup>
                    <input
                      type={type}
                      className="form-control"
                      id="passwort"
                      name="password"
                      placeholder="Passwort"
                      {...register('password', {
                        required: true,
                      })}
                      data-cy="loginPassword"
                    />

                    <InputGroupText>{eyeIcon}</InputGroupText>
                  </InputGroup>
                  {errors.password && (
                    <p className="inline-error">Dies ist ein Pflichtfeld</p>
                  )}
                </div>
              </div>
            </div>
            <div className="row pad-t-1">
              <div className="col-6 passwort-vergessen">
                <a
                  className="link-dark"
                  onClick={() =>
                    setCurrentDisplay(DisplayStates.FORGOT_PASSWORD)
                  }
                >
                  Passwort vergessen?
                </a>
              </div>
              {loading ? (
                <Loading />
              ) : (
                <div className="col-6">
                  <button
                    type="submit"
                    className="btn btn-dark float-right w-100 login-btn"
                    data-cy="loginSubmitBtn"
                  >
                    Anmelden
                  </button>
                </div>
              )}
            </div>

            {!hideRegister && !isUserOnRegistrationPage && (
              <>
                <div className="row pad-t-4">
                  <h4 className="col-sm-12 text-center">
                    Noch kein CC+ Mitglied?
                  </h4>
                </div>
                <div className="container">
                  <div className="row pt-1">
                    <Link href="/register" className="col-sm-12 btn btn-main font-weight-bold pad-tb-1 text-m border-radius-50px">
                      Registrieren
                    </Link>
                  </div>
                </div>
              </>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};
export default LoginForm;