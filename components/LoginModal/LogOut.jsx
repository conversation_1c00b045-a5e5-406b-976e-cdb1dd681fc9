import { Button } from 'reactstrap';
import { useRouter } from 'next/router';

import { logoutUser } from 'services/auth.service';
import { RiLogoutBoxRLine } from 'react-icons/ri/index';
import styles from './LoginModal.module.scss';

const LogOut = () => {
  const router = useRouter();

  const handleLogout = async () => {
    await logoutUser();
    router.push('/');
  };

  return (
    <div>
      <Button
        className="btn btn-dark hover-white"
        onClick={async () => {
          handleLogout();
        }}
      >
        <span className="logout-label">Abmelden</span>
        <RiLogoutBoxRLine className={styles.logoutIcon} />
      </Button>
    </div>
  );
};
export default LogOut;
