@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.so_funktionierts-link {
  font-size: 13px;
  white-space: nowrap;
  padding: 10px 20px;
  text-decoration: none;
  color: #000;
  &:hover {
    text-decoration: none;
    color: #000;
    font-weight: bold;
    cursor: pointer;
  }
}

.LoginModal {
  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    font-size: 18px;
    z-index: 5;
    height: auto;
  }
  .input-group-text {
    background: #fff;
    border: 1px solid #000;
    margin-left: -1px;
    border-left: 0;
    position: relative;
    font-size: 22px;
  }
  &.loginModalVisible {
    display: flex;
    flex-wrap: nowrap;
    margin-right: auto;
    margin-left: auto;
    .modal-body {
      padding: 0;
      width: 100%;
    }
    .modal-content {
      border: none;
    }
    .loginModalImage {
      height: 100%;
      width: 100%;
    }
    .loginModalImageWrapper {
      padding-left: 0px;
      padding-right: 0px;
    }
    .sidebar-header {
      padding: 0 0 35px;
      margin-bottom: 0;
    }
    .loginModalContent {
      padding: 35px;
    }
  }

  .passwort-vergessen {
    margin-top: auto;
    margin-bottom: auto;
  }
  .form-group {
    margin-bottom: 20px;
  }
  .link-dark {
    text-decoration: none;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  button {
    height: 50px;
    padding: 0;
    font-weight: bold;
  }
  .alert {
    margin: 30px 15px 0px;
  }
  .modal-body {
    padding: 35px;
  }
  .forgot-password-modal {
    padding: 15px 0;
    .sidebar-header {
      font-size: 36px;
      line-height: 40px;
      margin-bottom: 30px;
    }
  }
}
.loginModalVisible {
  .section-header {
    font-size: 30px;
    line-height: 40px;
  }
  .coupon-modal-content-section {
    padding: 40px 50px 30px;
  }
}
.logoutIcon {
  display: none;
}

.alert .close {
  font-size: 14px;
  position: absolute;
  right: 2px;
  top: 2px;
}

@include media-breakpoint-down(xs) {
  .Header .btn.btn-dark {
    margin-right: 15px;
    padding: 5px 8px;
  }
  .logoutIcon {
    display: block;
    font-size: 28px;
  }
  .logout-label {
    display: none;
  }
  .modal-body {
    padding: 20px;
  }
}

@include media-breakpoint-up(md) {
  .floatRight {
    float: right;
  }
}

@include media-breakpoint-down(md) {
  .so_funktionierts-link {
    display: none;
  }
}
