import { useState } from 'react';
import { GrClose } from 'react-icons/gr';

import * as ERROR_CODES from './ErrorCodes';
import { resendConfirmationCodeEmail } from 'services/auth.service';

const LoginErrors = (props) => {
  const { status, currentUsername } = props;
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState();

  const errorStatuses = {
    [ERROR_CODES.UserNotConfirmedException]: (
      <div
        className="alert alert-warning text-center p-3 "
        data-cy="loginErrorMessage"
      >
        <button className="close float-right" onClick={props.closeError}>
          <GrClose></GrClose>
        </button>
        Du hast dein Konto noch nicht bestätigt. Bitte prüfe deine E-Mail und{' '}
        {loading && <>loading</>}
        {!message && !loading && (
          <span
            className="link-normal"
            onClick={() => {
              setLoading(true);
              resendConfirmationCodeEmail({ username: currentUsername })
                .then((response) => {
                  if (
                    response?.data ===
                    'Confirmation code email successfully sent'
                  ) {
                    setMessage('E-Mail erneut gesendet');
                  } else {
                    setMessage(response?.data);
                  }
                })
                .catch((e) => console.log(e))
                .finally(() => {
                  setLoading(false);
                });
            }}
          >
            <br /> bestätige deine Registrierung
          </span>
        )}
        {message && (
          <>
            <span>verifiziere dein Konto. </span>
            <br />
            <p className="text-custom-color">{message}</p>
          </>
        )}
      </div>
    ),
    [ERROR_CODES.NotAuthorizedException]: (
      <div
        className="alert alert-danger text-center p-3 "
        data-cy="loginErrorMessage"
      >
        E-Mail Adresse und Passwort stimmen nicht überein. Bitte versuche es
        erneut.
      </div>
    ),
    [ERROR_CODES.UserNotFoundException]: (
      <div
        className="alert alert-danger text-center p-3 "
        data-cy="loginErrorMessage"
      >
        E-Mail Adresse und Passwort stimmen nicht überein. Bitte versuche es
        erneut.
      </div>
    ),
    [ERROR_CODES.ExpiredCodeException]: (
      <div
        className="alert alert-danger text-center p-3 "
        data-cy="loginErrorMessage"
      >
        Der Code ist ungültig.
      </div>
    ),
    [ERROR_CODES.CodeMismatchException]: (
      <div
        className="alert alert-danger text-center p-3 "
        data-cy="loginErrorMessage"
      >
        Bitte geben Sie den ungültigen Bestätigungscode erneut an.
      </div>
    ),
    [ERROR_CODES.EmailSendOk]: (
      <div
        className="alert alert-success text-center p-3 "
        data-cy="loginErrorMessage"
      >
        Wir haben dir eine E-Mail mit einem Login-Link geschickt!
      </div>
    ),
    [ERROR_CODES.ResetOk]: (
      <div
        className="alert alert-success text-center p-3 "
        data-cy="loginErrorMessage"
      >
        Passwort aktualisiert!
      </div>
    ),
    [ERROR_CODES.FacebookError]: (
      <div
        className="alert alert-warning text-center p-3 "
        data-cy="loginErrorMessage"
      >
        Ich kann mich nicht mit Facebook anmelden!
      </div>
    ),
    [ERROR_CODES.ActivatedOK]: (
      <div
        className="alert alert-success text-center p-3 "
        data-cy="loginErrorMessage"
      >
        Dein Konto wurde erfolgreich bestätigt
      </div>
    ),
  };
  return errorStatuses[status] || <></>;
};

export default LoginErrors;
