import React, { useEffect, useState, useContext } from 'react';
import { Button } from 'reactstrap';
import Modal from 'reactstrap/lib/Modal';
import ModalBody from 'reactstrap/lib/ModalBody';
import { useRouter } from 'next/router';
import { GrClose } from 'react-icons/gr';

import MainAppContext from 'context/mainAppContext';
import { removeLocalStorageItem } from 'util/localStorageManagement';

import * as DisplayStates from './DisplayStates';
import LoginForm from './LoginForm';
import LogOut from './LogOut';
import ForgotPass from './ForgotPass';
import LoginErrors from './LoginErrors';
import UtilitiesMenu from '../UtilitiesMenu/UtilitiesMenu';
import * as ERROR_CODES from './ErrorCodes';

import styles from './LoginModal.module.scss';
import ActiveLink from '../ActiveLink/ActiveLink';

const LoginModal = (props) => {
  const { open, showSoFunktioniertsLink } = props;
  const { showLoginModal, setShowLoginModal } = useContext(MainAppContext);
  const router = useRouter();
  const [currentDisplay, setCurrentDisplay] = useState(
    DisplayStates.NOT_LOGGED_IN
  );
  // TODO check where display error was set
  const [displayError, setDisplayError] = useState(false);
  const [loginStatus, setLoginStatus] = useState();
  const [username, setUsername] = useState();

  const [modal, setModal] = useState(() => {
    if (router.query.activated === 'ok' && !router.pathname.includes('login')) {
      setLoginStatus(ERROR_CODES.ActivatedOK);
      removeLocalStorageItem('emailToBeActivated');
      return true;
    }
    if (open) {
      return true;
    }
    return false;
  });

  React.useEffect(() => {
    const urlSearchParams = new URLSearchParams(window.location.search)
    const data = urlSearchParams.get('activated')
    if (data === 'ok' && !window.location.pathname.includes('login')) {
      setLoginStatus(ERROR_CODES.ActivatedOK);
      removeLocalStorageItem('emailToBeActivated');
      setModal(true);
    }
  }, []);

  useEffect(() => {
    setModal(showLoginModal);
  }, [showLoginModal]);

  useEffect(() => {
    let theTimeOut = null;
    // clears the error message
    if (
      loginStatus !== null &&
      loginStatus !== ERROR_CODES.UserNotConfirmedException
    ) {
      theTimeOut = setTimeout(() => {
        setLoginStatus(null);
      }, 4000);
    }

    return () => {
      if (theTimeOut) {
        clearTimeout(theTimeOut);
      }
    };
  }, [loginStatus, router.query]);

  useEffect(() => {
    if (localStorage.getItem('idToken')) {
      setCurrentDisplay(DisplayStates.LOGGED_IN);
    }
  }, [router.query]);

  useEffect(() => {
    if (router.query.tokenExp) {
      setModal(true);
      setCurrentDisplay(DisplayStates.NOT_LOGGED_IN);
      router.push({
        pathname: '/logout',
      });
    }
  }, [router.query]);

  const toggle = () => {
    setModal(!modal);
    setShowLoginModal(false);
  };

  const handleCloseError = () => {
    return setLoginStatus(null);
  };

  const loginDisplays = {
    [DisplayStates.LOGGED_IN]: (
      <>
        <UtilitiesMenu className="d-none d-lg-block"></UtilitiesMenu>
        <LogOut setCurrentDisplay={setCurrentDisplay} />
      </>
    ),
    [DisplayStates.NOT_LOGGED_IN]: (
      <>
        {showSoFunktioniertsLink && (
          <ActiveLink
            href="/so_funktioniert"
            className={styles.soFunktioniertsLink}
          >
            So funktioniert's
          </ActiveLink>
        )}
        <Button
          className="btn login-btn header-login-btn"
          onClick={toggle}
          data-cy="mainLoginBtn"
        >
          Login
        </Button>
        <Modal
          isOpen={modal}
          toggle={toggle}
          className={`${styles.loginModal} ${showLoginModal ? styles.loginModalVisible : ''}`}
        >
          <div className="container-fluid">
            <div className="row">
              <button className="close float-right" onClick={toggle}>
                <GrClose></GrClose>
              </button>
              <ModalBody>
                <LoginForm
                  setCurrentDisplay={setCurrentDisplay}
                  setLoginStatus={setLoginStatus}
                  setModal={setModal}
                  setUsername={setUsername}
                  username={username}
                  showLoginModal={showLoginModal}
                >
                </LoginForm>
                <LoginErrors status={loginStatus} currentUsername={username} />
              </ModalBody>
            </div>
          </div>
        </Modal>
      </>
    ),
    [DisplayStates.FORGOT_PASSWORD]: (
      <>
        <Button
          className="btn btn-dark hover-white btn-login"
          onClick={toggle}
          data-cy="mainLoginBtn"
        >
          Login
        </Button>
        <Modal
          isOpen={modal}
          toggle={() => {
            toggle();
            setCurrentDisplay(DisplayStates.NOT_LOGGED_IN);
          }}
          className={styles.loginModal}
        >
          <button
            className="close float-right"
            onClick={() => {
              toggle();
              setCurrentDisplay(DisplayStates.NOT_LOGGED_IN);
            }}
          >
            <GrClose></GrClose>
          </button>
          <ModalBody className="pad-t-4">
            <ForgotPass
              setCurrentDisplay={setCurrentDisplay}
              setLoginStatus={setLoginStatus}
              username={username}
            />
            <LoginErrors status={loginStatus} currentUsername={username} />
          </ModalBody>
        </Modal>
      </>
    ),
  };

  return (
    <>
      {loginDisplays[currentDisplay]}

      <div className="position-absolute">
        <LoginErrors
          status={loginStatus}
          currentUsername={username}
          closeError={handleCloseError}
        />
      </div>
      {router.query.error_description && displayError && (
        <>
          {loginStatus === ERROR_CODES.UserNotConfirmedException ? (
            <div className="position-absolute">
              <LoginErrors
                status={loginStatus}
                currentUsername={username}
                closeError={handleCloseError}
              />
            </div>
          ) : (
            <></>
            // <div className="alert alert-danger position-absolute">
            //   {/* {router.query.error_description} */}
            // </div>
          )}
        </>
      )}
    </>
  );
};

export default LoginModal;