import * as DisplayStates from './DisplayStates';
import { useForm } from 'react-hook-form';
import React, { useState } from 'react';
import * as ERROR_CODES from './ErrorCodes';
import { sendResetPasswordEmail } from 'services/auth.service';

const ForgotPass = ({ setCurrentDisplay, setLoginStatus, username }) => {
  const { register, handleSubmit, formState: { errors }, control } = useForm({
    defaultValues: {
      email: username || ''
    }
  });

  const handleResetPassword = async (data) => {
    try {
      sendResetPasswordEmail(data.email);
      localStorage.setItem('resetMail', data.email);
      setLoginStatus(ERROR_CODES.EmailSendOk);
    } catch (error) {
      setLoginStatus(error.code);
    }
  };
  return (
    <div className="forgot-password-modal">
      <h1 className="sidebar-header text-center">Passwort zurücksetzen</h1>
      <form className="container" onSubmit={handleSubmit(handleResetPassword)}>
        <div className="row">
          <div className="col-sm-12">
            <div className="form-group">
              <input
                type="email"
                className="form-control"
                placeholder="E-Mail-Adresse"
                {...register("email", {
                  required: true
                })}
              />
              {errors.email && (
                <p className="inline-error">Dies ist ein Pflichtfeld</p>
              )}
            </div>
          </div>
        </div>
        <div className="row pad-t-1">
          <div className="col-sm-8 col-md-8 col-lg-8 pad-b-1">
            <button className="btn btn-dark">Passwort zurücksetzen</button>
          </div>
          <div className="col-sm-4 col-md-4 col-lg-4">
            <button
              className="btn btn-dark p-2 floatRight"
              onClick={() => {
                setCurrentDisplay(DisplayStates.NOT_LOGGED_IN);
              }}
            >
              abbrechen
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};
export default ForgotPass;