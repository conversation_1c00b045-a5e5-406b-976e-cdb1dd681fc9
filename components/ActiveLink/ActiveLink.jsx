import Link from 'next/link';
import React from 'react';

const ActiveLink = React.memo(({ asPath, children, className, title, href, as, ...props }) => {
  const activeClassName = 'active-link';
  
  // Determine if the link is active
  const isActive = asPath === href || asPath === as;
  
  // Combine classes
  const linkClassName = isActive
    ? `${className || ''} ${activeClassName}`.trim()
    : className || '';

  return (
    <Link 
      href={href}
      as={as}
      className={linkClassName}
      title={title}
      {...props}
    >
      {children}
    </Link>
  );
});

ActiveLink.displayName = 'ActiveLink';

export default ActiveLink;