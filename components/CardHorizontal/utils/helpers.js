const fallbackCopyTextToClipboard = (text) => {
  var textArea = document.createElement("textarea");
  textArea.value = text;

  // Avoid scrolling to bottom
  textArea.style.top = "0";
  textArea.style.left = "0";
  textArea.style.position = "fixed";

  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    var successful = document.execCommand("copy");
  } catch (err) {
    console.error("Fallback: Oops, unable to copy", err);
  }

  document.body.removeChild(textArea);
};

export const copyTextToClipboard = (text) => {
  if (!navigator.clipboard) {
    fallbackCopyTextToClipboard(text);
    return;
  }
  navigator.clipboard.writeText(text).then(null, function (err) {
    console.error("Async: Could not copy text: ", err);
  });
};

export const calculateTimeLeft = (remainingSeconds) => {
  // Directly use remainingSeconds to determine the state
  if (remainingSeconds <= 0) {
    return { text: '', updateInterval: null }; // No need to update further
  }

  // Calculate days, hours, minutes, and seconds from remainingSeconds
  const days = Math.floor(remainingSeconds / (24 * 60 * 60));
  remainingSeconds -= days * 24 * 60 * 60;
  const hours = Math.floor(remainingSeconds / (60 * 60));
  remainingSeconds -= hours * 60 * 60;
  const minutes = Math.floor(remainingSeconds / 60);
  const seconds = remainingSeconds % 60;

  // Format hours, minutes, and seconds to always display two digits
  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');

  let text;
  if (days > 0) {
    // If there are days remaining, include them in the text
    text = `Noch ${days} ${days > 1 ? 'Tage' : 'Tag'} gültig`;
    // Update once an hour if more than 24 hours are remaining
    return { text, updateInterval: 3600 * 1000 };
  } else {
    // If less than a day remains, display hours, minutes, and seconds
    text = `Läuft ab in: ${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    // Update every second
    return { text, updateInterval: 1000 };
  }
}