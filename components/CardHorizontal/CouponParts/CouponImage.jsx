'use client';
import React from "react";
import styles from '../CardHorizontal.module.scss';

function CouponImage({ className = "", alt = "", src, children }) {
  return (
    <div className={`${styles.cardHorizontalImage} ${className}`}>
      <img
        id={`couponImage-${src}`}
        src={src ? src : "coupon_image_placeholder.png"}
        alt={alt || "image"}
      />
      {children}
    </div>
  );
}
export default CouponImage;