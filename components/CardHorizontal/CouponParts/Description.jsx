'use client';
import React, { useState, useEffect } from "react";
import styles from '../CardHorizontal.module.scss';
import { htmlToPngDataUri } from "../utils/htmlToImage";

export const wrapCouponRegulations = (couponReg, fontSize = 12) =>
  `<div style="word-wrap:break-word; text-align:center; margin:0px; padding:0px; font-size:${fontSize}px; font-family:Montserrat; color: #000000">${couponReg}</div>`;

function Description({ className = "", reg, id, children }) {
  const [regUri, setRegUri] = useState(null);
  
  useEffect(() => {
    (async function () {
      if (reg) {
        const uri = await htmlToPngDataUri(
          wrapCouponRegulations(reg, 13),
          300,
          document.getElementById("htmlToPrint-" + id)?.offsetHeight
        );
        setRegUri(uri);
      }
    })();
  }, [reg, id]);

  return (
    <div
      id={"htmlToPrint-" + id}
      className={`${styles.cardHorizontalDescription} ${className}`}
    >
      {reg ? regUri && <img src={regUri} alt="Coupon regulations" /> : children}
    </div>
  );
}
export default Description;