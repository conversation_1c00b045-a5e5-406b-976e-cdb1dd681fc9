'use client';
import React from "react";
import styles from '../CardHorizontal.module.scss';

function Discount({
  value,
  className = "",
  type = "PERCENTAGE",
  hasLongWords,
}) {
  return type === "FREE" ? (
    <div
      className={`${styles.cardHorizontalDiscountFree} ${
        hasLongWords ? styles.cardHorizontalDiscountFreeLongWords : ""
      } ${className}`}
    >
      {value || ""}
    </div>
  ) : (
    <div className={`${styles.cardHorizontalDiscount} ${className}`}>
      {value
        ? type === "PERCENTAGE"
          ? value + "%"
          : (value % 1 != 0
              ? value.toLocaleString("de-DE", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
              : value) + "€"
        : null}
    </div>
  );
}
export default Discount;