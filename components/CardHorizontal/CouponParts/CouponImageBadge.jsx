'use client';
import React from 'react';
import styles from '../CardHorizontal.module.scss';
import NurInDenFilialenIcon from '../../CustomIcons/NurInDenFilialenIcon';
import NeukundenRabattIcon from '../../CustomIcons/NeukundenRabattIcon';

function CouponImageBadge({ className = '', coupon }) {
  return (
    <div>
      {coupon.isForNewCustomers && (
        <NeukundenRabattIcon className={styles.imageBadge} />
      )}
      {coupon.discountCodeType === 'STATIONARY' && (
        <NurInDenFilialenIcon className={styles.imageBadge} />
      )}
    </div>
  );
}
export default CouponImageBadge;