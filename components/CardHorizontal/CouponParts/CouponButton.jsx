'use client';
import React, { useState, useEffect } from "react";
import { FaExternalLinkAlt } from "react-icons/fa";
import { htmlToPngDataUri } from "../utils/htmlToImage.js";
import { copyTextToClipboard } from "../utils/helpers.js";
import styles from '../CardHorizontal.module.scss';

export const wrapCouponRegulations = (couponReg, fontSize = 12) =>
  `<div style="word-wrap:break-word; text-align:center; margin:0px; padding:0px; font-size:${fontSize}px; font-family:Montserrat; color: #000000">${couponReg}</div>`;

function CouponButton({
  coupon = {},
  customButtonClass = "",
  onClick = () => {},
  getCouponLink = () => {},
  addMessage,
  PDFDownloadButton = () => null,
}) {

  const [regUri, setRegUri] = useState(null);
  const [imgLoaded, setImgLoaded] = useState(false);

  useEffect(() => {
    (async function () {
      if (coupon.codeURL) {
        const uri = await htmlToPngDataUri(
          wrapCouponRegulations(coupon.couponRegulations, 13),
          300,
          document.getElementById("htmlToPrint-" + coupon.id)?.offsetHeight
        );
        setRegUri(uri);
      }
    })();
  }, [coupon.codeURL]);

  if(coupon.discountCodeType === 'LINK') {
    if(coupon?.couponLinkInfo?.buttonText) {
      return (
        <>
          <a
            href={coupon.couponLink}
            target="_blank"
            className={`btn ${styles.cardHorizontalZumShopBtn}`}
          >
            { coupon?.couponLinkInfo?.buttonText } <FaExternalLinkAlt size={11} className="ml-1" style={{ marginBottom: '2px' }} />
          </a>
          <p className={styles.cardHorizontalLinkDescription}>
            { coupon?.couponLinkInfo?.linkDescription }
          </p>
        </>
      )
    } else {
      return (
        <div
          className={`${styles.cardHorizontalCouponCodeBtn} ${customButtonClass}`}
          onClick={getCouponLink}
        >
        <p className={styles.rabatteButtonBrand}>Gutscheincode</p>
        </div>
      );
    }
  }

  if (coupon.code) {
    return (
      <>
        <div className={`${styles.cardHorizontalCouponCodeBtn} ${customButtonClass}`}>
          <div className={styles.couponCode}>{coupon.code}</div>
          <div
            className={styles.copyBtn}
            onClick={() => {
              copyTextToClipboard(coupon.code);
              addMessage("Code erfolgreich kopiert!", "success");
            }}
          >
            <p className={styles.rabatteButtonBrand}>Code kopieren</p>
          </div>
        </div>
        <a
          href={coupon.couponLink}
          target="_blank"
          className={`btn ${styles.cardHorizontalZumShopBtn}`}
        >
          Zum Shop
        </a>
      </>
    );
  }

  if (coupon.codeURL) {
    return (
      <>
        <img
          src={coupon.codeURL}
          alt="Coupon image"
          id={`codeUrl-${coupon.codeURL}`}
          onLoad={() => setImgLoaded(true)}
          loading="lazy"
        />
        <div className={styles.pdfBtn}>
          {regUri && imgLoaded && (
            <PDFDownloadButton {...coupon} couponRegulations={regUri} />
          )}
        </div>
      </>
    );
  }

  return (
    <div
      className={`${styles.cardHorizontalCouponCodeBtn} ${customButtonClass}`}
      onClick={onClick}
    >
      <p className={styles.rabatteButtonBrand}>Gutscheincode</p>
    </div>
  );
}

export default CouponButton;