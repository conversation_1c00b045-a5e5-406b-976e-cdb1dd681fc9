@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";
@import "~bootstrap/scss/mixins";

@import "./styles/sass/variables";

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;500;700;900&display=swap");

// Override default variables before the import
$body-bg: #ebebeb;
$body-color: #000;
$enable-rounded: false;
$theme-colors: (
  "primary": #007bff,
  "danger": #ff4136,
  "custom-color": #900,
  "dark": #000,
  "gray": #000,
  "red": #ff0000,
);
$font-family-sans-serif: "Montserrat", sans-serif;
// html {
//   scroll-behavior: smooth;
// }

$imageHeight: 250px;
.cardHorizontal {
  font-family: $font-family-sans-serif;
  display: flex;
  margin-bottom: 35px;
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  border: 1px solid #AFAFAF;

  @media screen and (max-width: $handheld-breakpoint) {
    flex-direction: column;
    margin-bottom: 16px;
  }

  &Image {
    min-width: 50%;
    position: relative;
    min-height: $imageHeight;
    overflow: hidden;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-51%, -50%);
      width: 100%;
      min-width: 102%;
      min-height: 100%;
      object-fit: cover;
    }
  }

  .noPadding {
    padding: 0px;
  }

  &Details.hasCountdown {
    padding: 0px;
    
    .countdown {
      width: 100%;
      padding: 10px;
      background-color: #81e9f0;
      font-weight: bold;
      font-size: 20px;      
    }

    .details {
      width: 100%;
      padding: 32px;
    }
  }

  &Details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    padding: 32px;
    text-align: center;
    min-width: 50%;

    @media screen and (max-width: $handheld-breakpoint) {

      img {
        display: none;
      }
    }

    img {
      max-width: 100%;
    }
  }

  .pdfBtn {
    bottom: 0;
    right: 0;
    border-radius: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    border-color: #000;
    color: #fff;
    font-size: 14px;
    display: flex;
    position: absolute;

    @media (max-width: 767.98px) {
      justify-content: center;
      position: relative;
      margin-top: 20px;
    }

    a {
      width: auto;
      height: auto;
      background-color: #000;
      padding: 8px 10px;
    }
  }

  .imageBadge {
    z-index: 1;
    position: absolute;
    top: 0;
    left: 0;
    height: auto;
    width: 60%;

    display: block;
    @media screen and (max-width: $handheld-breakpoint) {
      display: none;
    }
  }

  .cardText {
    background-color: black;
    z-index: 1;
    position: absolute;
    width: 100%;
    bottom: 0;

    display: none;

    @media screen and (max-width: $handheld-breakpoint) {
      display: block;
    }

    p {
      color: white;
      text-align: center;
      margin-bottom: 0px;
      padding: 1px 10px;
      font-weight: 600;
      font-size: 20px;
    }
  }

  &Logo {
    display: block;
    margin: 0 auto;
    max-height: 75px;
    @media screen and (max-width: $handheld-breakpoint) {
      max-height: 36px;
      max-width: 75%;
    }
  }
  &Discount {
    font-size: 62px;
    font-weight: 900;
    line-height: 1;
    padding: 15px;
    @media (min-width: 992px) and (max-width: 1199px) {
      font-size: 42px;
    }
    @media (max-width: 991.98px) {
      font-size: 50px;
    }
    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 32px;
      padding: 6px;
    }
  }
  &DiscountFree {
    font-size: 25px;
    font-weight: 900;
    line-height: 1.2;
    padding-bottom: 15px;
    padding-top: 15px;
    word-break: break-word;

    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 16px;
      padding: 4px;
    }
  }

  &DiscountFreeLongWords {
    font-size: 21px;

    @include media-breakpoint-down(sm) {
      font-size: 15px;
    }

    @include media-breakpoint-down(xs) {
      font-size: 12px;
    }

    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 10px;
    }
  }

  &Heading {
    font-size: 16px;
    line-height: 25px;

    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 10px;
      line-height: 12px;
    }
  }
  &Description {
    font-size: 10px;
    padding: 20px 0;

    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 10px;
      line-height: 12px;
      padding: 6px 0;
    }

    p {
      @media screen and (max-width: $handheld-breakpoint) {
        word-break: break-word;
      }
    }
  }
  &CouponBtn {
    display: inline-block;
    margin: 0 auto;
    padding: 5px 30px;
    font-size: 20px;
    line-height: 32px;
    min-width: 70%;
  }

  &ZumShopBtn {
    width: 100%;
    margin-top: 10px;
    color: #000 !important;
    background-color: #81e9f0 !important;
    text-decoration: none;
    font-weight: 600 !important;
    border-radius: 50px !important;

    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 10px;
      line-height: 23px;
      padding: 6px 0;
      width: 90%;
    }

    &:focus {
      box-shadow: none;
      color: #000 !important;
      background-color: #81e9f0 !important;
    }

    &:hover {
      background-color: #000 !important;
      color: white !important;
    }
  }

  &CouponCodeBtn {
    width: 100%;
    text-align: center;
    vertical-align: middle;
    border-radius: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    color: #fff;
    border-color: #000;
    font-size: 14px;
    font-weight: 700;
    line-height: 42px;
    min-width: 70%;
    text-decoration: none;
    display: inline-block;
    margin: 0 auto;
    display: flex;
    justify-content: center;

    @media screen and (max-width: $handheld-breakpoint) {
      line-height: 12px;
      height: 37px;
      width: 90%
    }

    &.myListCouponButton {
      margin: 20px auto 0;
    }

    .couponCode {
      width: 70%;
      background-color: white;
      color: black;
      float: left;
      cursor: auto;
      border: 1px solid #000;
      font-weight: 700;
      font-size: 12px;
      line-height: 1.2;
      min-height: 37px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 2px;
      word-break: break-all;
      border-radius: 30px 0px 0 30px;

      @media screen and (max-width: $handheld-breakpoint) {
        font-size: 8px;
      }
    }

    .rabatteButtonBrand {
      width: 100%;
      font-size: 20px;
      border-radius: 50px;

      @media screen and (max-width: $handheld-breakpoint) {
        width: 100%;
        font-size: 1rem;
        padding: 6px 10px;
        line-height: 24px;
      }
    }

    .copyBtn {
      width: 30%;
      float: left;
      padding: 11px 0;
      background-color: #000;
      border: 1px solid #000;
      border-left: 0;
      cursor: pointer;
      display: table;
      user-select: none;
      border-radius: 0 50px 50px 0;

      @media (max-width: 428px) {
        max-height: 37px;
      }

      @media screen and (max-width: $handheld-breakpoint) {
        padding: 3px 0;
      }

      p {
        font-size: 10px;
        line-height: 10px;
        font-weight: 700;
        padding: 0 10%;
        margin-bottom: 0rem;
        color: white;
        display: table-cell;
        vertical-align: middle;
        text-align: center;

        @media screen and (max-width: $handheld-breakpoint) {
          font-size: 7px;
          line-height: 9px;
        }

        @media screen and (max-width: 350px) {
          padding: 0 5%;
        }
      }

      &:hover {
        background-color: #81e9f0;
        color: #000;

        p {
          background-color: #81e9f0;
          color: #000;
        }
      }
    }

    p {
      margin-bottom: 0rem;
      background-color: #000;

      &:hover {
        background-color: #81e9f0;
        color: #000;
        cursor: pointer;
      }
    }

    a {
      width: 280px;
      height: 42px;
      text-decoration: none;
      background-color: black;
      color: white;
      padding: 8px 23px;
      font-size: 20px;
      font-weight: 700;
      line-height: 25px;

      &:hover {
        background-color: #81e9f0;
        color: #000;
        cursor: pointer;
      }
    }
  }

  &CouponCode {
    font-size: 20px;
    line-height: 32px;
    padding: 7px 30px;
    min-width: 70%;
    display: inline-block;
    border: 1px solid #000;
    font-weight: 700;
  }

  &ImageBadge {
    background-color: #000;
    font-size: 20px;
    position: absolute;
    width: 400px;
    text-align: center;
    color: #fff;
    padding: 10px;
    font-weight: 900;
    transform: rotate(-45deg);
    left: -50px;
    transform-origin: 0% 50%;
    top: 200px;
  }
  &Download {
    padding: 7px 10px;
    position: absolute;
    right: 0;
    bottom: 0;
    background-color: #000;
    color: #fff;
  }

  &LinkDescription {
    font-size: 12px;
    line-height: 20px;
    padding-top: 10px;
    font-weight: 600;
    margin-bottom: 0 !important;
    @media screen and (max-width: $handheld-breakpoint) {
      font-size: 10px;
      line-height: 12px;
      padding: 6px 0;
    }
  }

  .like {
    position: absolute;
    right: 16px;
    bottom: 16px;

    @media screen and (max-width: $handheld-breakpoint) {
      right: 12px;
      bottom: 12px;
    }
  }
  
  .imageContainer {
    min-width: 50%;
    position: relative;
    min-height: $imageHeight;
    overflow: hidden;
  }
  
  .minHeight250 {
    min-height: 250px;
  }
}