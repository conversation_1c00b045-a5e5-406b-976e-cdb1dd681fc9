'use client';
import React, { useEffect, useState } from 'react';
import {
  CouponImage,
  Discount,
  Heading,
  Description,
  CouponButton,
  Like,
  CouponImageBadge,
} from './CouponParts';
import LimitOverlay, { getOverlayMessage } from './LimitOverlay';
import { calculateTimeLeft } from './utils/helpers';
import { getCouponAltText } from '../../util/helpers';
import styles from './CardHorizontal.module.scss';

const createMarkup = (value) => ({
  __html: value.toString(),
});

export const CardHorizontal = ({
  coupon,
  className = '',
  handleLikeClick,
  isAuth,
  isTouchDevice,
  isLiked,
  hasLongWordsHeadline,
  getCouponCode,
  getCouponLink,
  showToastMessage,
  PDFDownloadButton,
  code,
  codeURL,
  couponLinkInfo,
  brandCountDownEnabled
}) => {
  const [countDownText, setCountDownText] = useState('');
  const [timerInterval, setTimerInterval] = useState(1000);

  useEffect(() => {
    if (!brandCountDownEnabled) return;

    let dynamicRemainingTime = coupon.remainingTime || 0;

    const updateTimer = () => {
      const { text, updateInterval } = calculateTimeLeft(dynamicRemainingTime);

      if (!text.length) {
        coupon.status = 'EXPIRED';
      }
      
      setCountDownText(text);
      dynamicRemainingTime -= timerInterval / 1000;

      if (updateInterval !== timerInterval) {
        setTimerInterval(updateInterval);
        clearInterval(intervalId);
        intervalId = setInterval(updateTimer, updateInterval);
      }
    };

    let intervalId = setInterval(updateTimer, timerInterval);

    return () => clearInterval(intervalId);
  }, [brandCountDownEnabled, coupon.remainingTime, timerInterval]);

  if (!coupon) return null;

  const detailsClassName = countDownText 
    ? `${styles.cardHorizontalDetails} ${styles.hasCountdown}`
    : styles.cardHorizontalDetails;

  return (
    <div className={`${styles.cardHorizontal} ${className}`}>
        <CouponImage 
          src={coupon.compressedImage || coupon.image} 
          alt={getCouponAltText(coupon)}
        >
          <CouponImageBadge coupon={coupon} />
          <LimitOverlay
            message={getOverlayMessage({
              status: coupon.status,
              isBrandCopiesLimitReached: coupon.isBrandCopiesLimitReached,
            })}
            className={styles.minHeight250}
          />
          <Like
            isLiked={isLiked || coupon.isFavourite}
            id={coupon.id}
            handleClick={handleLikeClick}
            isAuth={isAuth}
            isTouchDevice={isTouchDevice}
          />
        </CouponImage>
      <div className={detailsClassName}>
        {countDownText && (
          <div className={styles.countdown}>{countDownText}</div>
        )}

        <div className={styles.details}>
          <Discount
            type={coupon.discountType}
            value={coupon.discountType === 'FREE' 
              ? coupon.freeDescription 
              : coupon.discountValue
            }
            hasLongWords={hasLongWordsHeadline}
          />
          
          <Heading>
            Mindestbestellwert:{' '}
            {coupon.amountCondition && coupon.amountCondition !== 0 ? (
              <b>{coupon.amountCondition.toLocaleString('de-DE')}</b>
            ) : (
              <b>keiner</b>
            )}
            <br />
            Gültig bis:{' '}
            <b>
              {new Intl.DateTimeFormat('de-DE', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
              }).format(new Date(coupon.validationDate))}
            </b>
          </Heading>

          <Description id={coupon.id}>
            <div
              style={{
                wordWrap: 'break-word',
                wordBreak: 'break-word',
              }}
              dangerouslySetInnerHTML={createMarkup(coupon.couponRegulations)}
            />
          </Description>

          <CouponButton
            coupon={{
              ...coupon,
              ...(code && { code }),
              ...(codeURL && { codeURL }),
              ...(couponLinkInfo && { couponLinkInfo }),
            }}
            PDFDownloadButton={PDFDownloadButton}
            onClick={getCouponCode}
            getCouponLink={getCouponLink}
            addMessage={showToastMessage}
          />
        </div>
      </div>
    </div>
  );
};

export default CardHorizontal;
