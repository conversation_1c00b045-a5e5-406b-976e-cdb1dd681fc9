import { Container, Row } from 'reactstrap';
import Benefits from '../Benefits/Benefits';
import styles from './SpecialAudienceBenefits.module.scss';
import badges from '../../model/special-audience-badges.json';

const FIRST_PARAGRAPH_TEXTS_BY_AUDIENCE_KEY = {
  corporate:
    'Dein Arbeitgeber hat einen kostenlosen Account bei CaptainCoupon.de für dich bereitgestellt.',
  club: 'Dein Verein hat einen kostenlosen Account bei CaptainCoupon.de für dich bereitgestellt.',
};

const ENTER_CODE_PROMPT_TEXTS_BY_AUDIENCE_KEY = {
  corporate:
    'Gib einfach den Code ein, den du von deinem Arbeitgeber erhalten hast.',
  club: 'Gib einfach den Code ein, den du von deinem Verein erhalten hast.',
};

// TODO standard CC logo is only a placeholder and will not be replaced
const IMAGE_PATHS_BY_AUDIENCE_KEY = {
  corporate: '/logos/personalvorteile.png',
  club: '/logos/vereinsrabatte.png',
};

const IMAGE_ALT_TEXTS_BY_AUDIENCE_KEY = {
  corporate: 'Schriftzug "PERSONALVORTEILE" in schwarz',
  club: 'Schriftzug "VEREINSRABATTE" in schwarz',
};

export default function SpecialAudienceBenefits({
  isProductFromCodeLoaded,
  specialAudience, // 'corporate' | 'club'
}) {
  return (
    <Container className={`${styles.specialAudienceBenefits} pt-2 ${styles.paddingSide25}`}>
      <Row className="justify-content-center pb-5">
        <img
          src={IMAGE_PATHS_BY_AUDIENCE_KEY[specialAudience]}
          className={styles.specialAudienceLogo}
          alt={IMAGE_ALT_TEXTS_BY_AUDIENCE_KEY[specialAudience]}
        />
      </Row>
      <Row className="pb-2">
        <h1 className="main-header text-center">Tolle Neuigkeiten!</h1>
      </Row>
      <Row className="pl-3 pr-3">
        <p>
          {FIRST_PARAGRAPH_TEXTS_BY_AUDIENCE_KEY[specialAudience]} Nach einer
          kurzen Registrierung hast du Zugang zu Coupon-Codes von deinen
          Lieblingsmarken und den beliebtesten Online-Shops &ndash; immer mit
          unserer 100%-Gutscheincode-Gültigkeitsgarantie.
        </p>
        {isProductFromCodeLoaded ? (
          <p>Mit nur einem Klick geht's los zur Registrierung:</p>
        ) : (
          <p>
            {ENTER_CODE_PROMPT_TEXTS_BY_AUDIENCE_KEY[specialAudience]} Mit nur
            einem Klick geht's dann zur Registrierung.
          </p>
        )}
      </Row>

      <Row className="justify-content-center pl-3 pr-3 pt-5">
        <Benefits badges={badges} />
      </Row>
    </Container>
  );
}