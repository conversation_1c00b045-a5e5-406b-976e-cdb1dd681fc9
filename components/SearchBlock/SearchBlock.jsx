import SearchIcon from '../CustomIcons/SearchIcon';
import styles from './SearchBlock.module.scss';

const SearchBlock = ({
  className = '',
  searchText,
  handleChange = (f) => f,
}) => {
  return (
    <div className={`${styles.searchBlock} ${className}`}>
      <div className={styles.inputGroup}>
        <input
          type="text"
          className={`form-control ${styles.formControl}`}
          placeholder="Suche"
          aria-label="Suchbegriff"
          aria-describedby="basic-addon2"
          value={searchText}
          onChange={(e) => handleChange(e.target.value)}
        />
        <div className={styles.inputGroupAppend}>
          <SearchIcon height="22px" />
        </div>
      </div>
    </div>
  );
};

export default SearchBlock;