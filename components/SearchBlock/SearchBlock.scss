@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
.SearchBlock {
  .form-control {
    background: none;
    border: 0;
    border-bottom: 1px solid #000;
    opacity: 1;
  }
  .input-group-append {
    padding: 10px;
    display: block;
    font-size: 18px;
  }
  &:hover .form-control,
  .form-control:focus {
    opacity: 1;
    outline: none;
    box-shadow: none;
  }
  &.shorter {
    .input-group {
      height: 38px;
    }
    .form-control {
      height: 100%;
    }
    .input-group-append {
      padding: 0;
      font-size: 24px;
    }
  }
}
@include media-breakpoint-down(md) {
  .SearchBlock {
    &:hover .form-control,
    .form-control:focus {
      font-size: 12px;
    }
  }
}
