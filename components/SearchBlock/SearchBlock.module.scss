@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.searchBlock {
  .formControl {
    background: none;
    border: 0;
    border-bottom: 1px solid #000;
    opacity: 1;
  }
  
  .inputGroupAppend {
    padding: 10px;
    display: block;
    font-size: 18px;
  }
  
  &:hover .formControl,
  .formControl:focus {
    opacity: 1;
    outline: none;
    box-shadow: none;
  }
  
  &.shorter {
    .inputGroup {
      height: 38px;
    }
    
    .formControl {
      height: 100%;
    }
    
    .inputGroupAppend {
      padding: 0;
      font-size: 24px;
    }
  }
}

@include media-breakpoint-down(md) {
  .searchBlock {
    &:hover .formControl,
    .formControl:focus {
      font-size: 12px;
    }
  }
}