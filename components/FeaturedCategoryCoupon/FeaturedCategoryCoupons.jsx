import React, { useRef, useState, useEffect } from 'react';
import styles from './FeaturedCategoryCoupon.module.scss';
import {
  Layout,
  Like,
  Section,
  Card,
} from 'components';
import { displayCouponCode } from 'components/Card/Card';
import LimitOverlay, {
  getOverlayMessage,
} from 'components/LimitOverlay/LimitOverlay';
import { toggleCouponFavorite } from 'util/helpers';
import { getCouponAltText } from 'util/helpers';
import useAPIError from 'components/APIErrorNotification/useAPIError';

const FeaturedCategoryCoupons = ({ coupons }) => {
  const { addMessage } = useAPIError();
  const scrollContainerRef = useRef(null);
  const [isMouseDown, setIsMouseDown] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [localCoupons, setLocalCoupons] = useState(coupons);

  // Mouse events
  const handleMouseDown = (e) => {
    if (!scrollContainerRef.current) return;
    setIsMouseDown(true);
    setStartX(e.pageX);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
    // Change cursor style
    scrollContainerRef.current.style.cursor = 'grabbing';
    // Prevent default behavior
    e.preventDefault();
  };

  const handleMouseUp = () => {
    setIsMouseDown(false);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseMove = (e) => {
    if (!isMouseDown || !scrollContainerRef.current) return;
    const x = e.pageX;
    // Calculate how far the mouse has moved
    const walk = (x - startX) * 2; // Multiply by 2 for faster scrolling
    // Update the scroll position
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
    e.preventDefault();
  };

  // Touch events for mobile
  const handleTouchStart = (e) => {
    if (!scrollContainerRef.current) return;
    setIsMouseDown(true);
    setStartX(e.touches[0].clientX);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
  };

  const handleTouchMove = (e) => {
    if (!isMouseDown || !scrollContainerRef.current) return;
    const x = e.touches[0].clientX;
    const walk = (x - startX) * 2;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
  };

  // Add event listeners to document to ensure dragging works even when mouse moves outside the container
  useEffect(() => {
    const handleMouseUpDocument = () => {
      setIsMouseDown(false);
      if (scrollContainerRef.current) {
        scrollContainerRef.current.style.cursor = 'grab';
      }
    };

    document.addEventListener('mouseup', handleMouseUpDocument);
    return () => {
      document.removeEventListener('mouseup', handleMouseUpDocument);
    };
  }, []);

  // Update local coupons when props change
  useEffect(() => {
    setLocalCoupons(coupons);
  }, [coupons]);

  return (
    <div className="horizontal-scroll-wrapper">
      <div
        className={styles.scrollContainer}
        ref={scrollContainerRef}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleMouseUp}
      >
        {localCoupons &&
          localCoupons.map((item, index) => {
            return (
              <div
                key={index}
                className={styles.cardItem}
                style={{
                  pointerEvents: isMouseDown ? 'none' : 'auto', // Disable pointer events during drag
                }}
              >
                <Card
                  className="coupon-card-landing-page white-card"
                  cardClassName="border-grey"
                >
                  <Card.Image
                    url={`/brand/${item.brandSlug}`}
                    src={item.compressedImage || item.image}
                    alt={getCouponAltText(item)}
                    eager={index < 4}
                    className={styles.fixedHeightImage}
                  >
                    <Like
                      isLiked={item.isFavourite}
                      id={item.id}
                      onSuccess={() =>
                        setLocalCoupons((prevCoupons) =>
                          toggleCouponFavorite(prevCoupons, item.id)
                        )
                      }
                      onError={(_, data) => {
                        addMessage(data, 'error');
                      }}
                    />
                    {item?.status !== 'NO_ACTIVE_SUBSCRIPTION' && (
                      <LimitOverlay
                        message={getOverlayMessage(item)}
                        className="border-rounded-top-14"
                      />
                    )}
                    <Card.Discount
                      type={item.discountType}
                      shortDescription={item.shortDescription}
                    >
                      {item.discountValue}
                    </Card.Discount>
                  </Card.Image>
                  <div className={styles.cardContent}>
                    <Card.ClientLogo
                      src={item?.brandLogo}
                      alt={`Logo der Brand ${item.brandSlug} gutschein`}
                    />
                    <Card.Description>
                      {item.shortCouponDescription}
                    </Card.Description>
                    <div className={styles.buttonWrapper}>
                      <Card.CouponButton
                        coupon={item}
                        getCouponCode={() => displayCouponCode(item.brandSlug)}
                      >
                        <p>{item.code ? 'Code kopieren' : 'Gutscheincode'}</p>
                      </Card.CouponButton>
                    </div>
                  </div>
                </Card>
              </div>
            );
          })}
      </div>
      <style jsx>{`
        .scroll-container::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}

export default FeaturedCategoryCoupons;