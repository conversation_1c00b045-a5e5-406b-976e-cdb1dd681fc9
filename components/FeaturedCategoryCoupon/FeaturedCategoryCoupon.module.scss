@import '../../styles/sass/mixins';

.scrollContainer {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  -webkit-overflow-scrolling: touch;
  cursor: grab;
  user-select: none;
  padding: 10px 0;

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.cardItem {
  flex: 0 0 auto;
  width: calc(22% - 20px);
  min-width: 270px;
  margin: 0 10px;
  display: flex;

  /* Ensure all cards have the same height */
  & > div {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Make the card component take full height */
  :global(.coupon-card-landing-page) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure the card inner content takes full height */
  :global(.coupon-card-landing-page > div) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  @include mediaMobile {
    min-width: 170px;
    margin-right: 10px;
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
}

/* Fixed height image container */
.fixedHeightImage {
  height: 212px !important;
  min-height: 212px !important;

  @include mediaMobile {
    height: 139px !important;
    min-height: 139px !important;
  }

  /* Ensure images maintain aspect ratio and cover the container */
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

/* Card content container with flex layout */
.cardContent {
  display: flex;
  flex-direction: column;
  flex: 1;
  /* Ensure consistent spacing */
  padding: 0 10px;
  /* Set minimum height for content area */
  min-height: 180px;

  @include mediaMobile {
    min-height: 150px;
  }
}

/* Button wrapper to push button to bottom */
.buttonWrapper {
  margin-top: auto;
  padding-bottom: 15px;

  @include mediaMobile {
    padding-bottom: 10px;
  }
}