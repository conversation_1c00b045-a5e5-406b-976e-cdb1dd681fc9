import { Section } from '../../components';
import Image from 'next/image';
import Link from 'next/link';
import styles from './AnnouncementBannerSection.module.scss';

const AnnouncementBanner = ({
  banner = null
}) => {

  // Extract data from the banner object if available
  const content = banner?.content;
  const isButton = banner?.isButton !== undefined ? banner.isButton : true;
  const buttonText = banner?.buttonText || "Gutscheincode";
  const buttonLink = banner?.buttonLink || "#";
  // Prefer compressed image if available, otherwise use regular image
  const image = banner?.compressedImage || banner?.image;

  // Determine if we should show an image
  const showImage = !!image && banner?.image;

  return (
    <Section
      background="#81E9F0"
      customPadding={true}
      rowClass='text-center justify-content-center align-items-center'
      className='pad-b-3 pad-t-3'
      containerClass='container'
    >
      <div className="row">
        {showImage ? (
          <>
            <div className="d-none d-md-block col-1"></div>
            <div className="col-12 col-md-2 m-auto">
              <div className={styles.imageContainer}>
                <Image
                  src={image}
                  alt="Announcement banner image"
                  width={300}
                  height={300}
                  className={styles.bannerImage}
                />
              </div>
            </div>
            <div className="col-12 col-md-8">
              <div className={styles.contentContainer}>
                <div
                  className={styles.content}
                  dangerouslySetInnerHTML={{ __html: content }}
                />
                {isButton && (
                  <Link
                    href={buttonLink}
                    className={`rabatteButton couponCodeBtn ${styles.button}`}
                  >
                    <p>{buttonText}</p>
                  </Link>
                )}
              </div>
            </div>
            <div className="d-none d-md-block col-1"></div>
          </>
        ) : (
          <div className="col-12 col-md-8 offset-md-2 text-center">
            <div className={`${styles.contentContainer} ${styles.centered}`}>
              <div
                className={styles.content}
                dangerouslySetInnerHTML={{ __html: content }}
              />
              {isButton && (
                <Link
                  href={buttonLink}
                  className={`rabatteButton couponCodeBtn ${styles.button}`}
                >
                  <p>{buttonText}</p>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </Section>
  )
}

export default AnnouncementBanner;