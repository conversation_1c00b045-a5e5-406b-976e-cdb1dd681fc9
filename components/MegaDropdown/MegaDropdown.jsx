import React from 'react';
import Link from 'next/link';
import styles from './MegaDropdown.module.scss';

const MegaDropdown = ({ isOpen }) => {
  const categories = [
    { title: 'Essen & Trinken', href: '/kategorie/essen-trinken', icon: '/icons/food.svg' },
    { title: 'Mode & Schmuck', href: '/kategorie/mode-schmuck', icon: '/icons/fashion.svg' },
    { title: '<PERSON><PERSON> & Wohnen', href: '/kategorie/haus-wohnen', icon: '/icons/home.svg' },
    { title: 'Gesundheit', href: '/kategorie/gesundheit', icon: '/icons/health.svg' },
    { title: 'Unterhaltung & Bildung', href: '/kategorie/unterhaltung-bildung', icon: '/icons/entertainment.svg' },
    { title: 'Technik & Mobilfunk', href: '/kategorie/technik-mobilfunk', icon: '/icons/tech.svg' },
    { title: 'Finanzen & Versicherung', href: '/kategorie/finanzen-versicherung', icon: '/icons/finance.svg' },
    { title: 'Geschenke & Blumen', href: '/kategorie/geschenke-blumen', icon: '/icons/gifts.svg' },
    { title: 'Tierbedarf', href: '/kategorie/tierbedarf', icon: '/icons/pets.svg' },
    { title: 'Freizeit & Hobbys', href: '/kategorie/freizeit-hobbys', icon: '/icons/hobbies.svg' },
    { title: 'Mobilität', href: '/kategorie/mobilitaet', icon: '/icons/mobility.svg' },
    { title: 'Baby & Kind', href: '/kategorie/baby-kind', icon: '/icons/baby.svg' },
    { title: 'Beauty & Körperpflege', href: '/kategorie/beauty-koerperpflege', icon: '/icons/beauty.svg' },
    { title: 'Sport', href: '/kategorie/sport', icon: '/icons/sport.svg' },
    { title: 'Liebesleben', href: '/kategorie/liebesleben', icon: '/icons/love.svg' },
    { title: 'Alle anzeigen', href: '/kategorien', icon: '/icons/all.svg' }
  ];

  const coupons = [
    { title: '25% von Bettwarenshop.de', href: '/rabatt/bettwarenshop' },
    { title: '22% von SNOCKS', href: '/rabatt/snocks' },
    { title: '50€ von Outfittery', href: '/rabatt/outfittery' },
    { title: '25% von OCEANSAPART', href: '/rabatt/oceansapart' },
    { title: '125€ von HelloFresh', href: '/rabatt/hellofresh' },
    { title: '20€ von Purelei', href: '/rabatt/purelei' },
    { title: '20€ von Purelei', href: '/rabatt/purelei-2' },
    { title: '20€ von Purelei', href: '/rabatt/purelei-3' },
    { title: 'Alle anzeigen', href: '/rabatte' }
  ];

  const brands = [
    { title: 'Logo 1', href: '/marke/logo1', logoUrl: '/logos/logo1.png' },
    { title: 'Logo 2', href: '/marke/logo2', logoUrl: '/logos/logo2.png' },
    { title: 'Logo 3', href: '/marke/logo3', logoUrl: '/logos/logo3.png' },
    { title: 'Logo 4', href: '/marke/logo4', logoUrl: '/logos/logo4.png' },
    { title: 'Logo 5', href: '/marke/logo5', logoUrl: '/logos/logo5.png' },
    { title: 'Logo 6', href: '/marke/logo6', logoUrl: '/logos/logo6.png' },
    { title: 'Logo 7', href: '/marke/logo7', logoUrl: '/logos/logo7.png' },
    { title: 'Logo 8', href: '/marke/logo8', logoUrl: '/logos/logo8.png' },
    { title: 'Logo 9', href: '/marke/logo9', logoUrl: '/logos/logo9.png' },
    { title: 'Logo 10', href: '/marke/logo10', logoUrl: '/logos/logo10.png' },
    { title: 'Logo 11', href: '/marke/logo11', logoUrl: '/logos/logo11.png' },
    { title: 'Logo 12', href: '/marke/logo12', logoUrl: '/logos/logo12.png' },
    { title: 'Logo 13', href: '/marke/logo13', logoUrl: '/logos/logo13.png' },
    { title: 'Logo 14', href: '/marke/logo14', logoUrl: '/logos/logo14.png' },
    { title: 'Logo 15', href: '/marke/logo15', logoUrl: '/logos/logo15.png' },
    { title: 'Logo 13', href: '/marke/logo13', logoUrl: '/logos/logo13.png' },
    { title: 'Logo 14', href: '/marke/logo14', logoUrl: '/logos/logo14.png' },
    { title: 'Logo 15', href: '/marke/logo15', logoUrl: '/logos/logo15.png' },
    { title: 'Alle anzeigen', href: '/marken' }
  ];

  return (
    <div className={`${styles.megaDropdownWrapper} py-4 px-2`}>
      <div className="row no-gutters justify-content-center">
        {/* Mobile View - Single column layout for categories */}
        <div className="d-md-none w-100">
          <div className="container-fluid px-3 py-2">
            {/* Header with tabs */}
            <div className="d-flex border-bottom mb-3">
              <div className="px-3 py-2 border-bottom border-dark font-weight-bold">
                Kategorien
              </div>
              <div className="px-3 py-2 text-muted">
                Coupons
              </div>
              <div className="px-3 py-2 text-muted">
                Marken
              </div>
            </div>

            {/* Categories List */}
            <div className="categories-list">
              {categories.slice(0, 10).map((category, index) => (
                <Link
                  href={category.href}
                  key={`category-mobile-${index}`}
                  className={styles.categoryItem}
                >
                  <div className="mr-2">
                    <img src={category.icon} alt="" className="img-fluid" style={{ width: '24px', height: '24px' }} />
                  </div>
                  <span className="text-truncate">{category.title}</span>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Desktop View - Three column layout */}
        <div className="d-none d-md-block w-100">
          <div className="row no-gutters">
            {/* Categories Column (2 items per row) */}
            <div className="col-lg-4 mb-4 mb-lg-0 pr-3">
              <h3 className={`${styles.heading} mb-2`}>Kategorien</h3>
              <hr className={styles.sectionDivider} />
              <div className={styles.categoryContainer}>
                {categories.slice(0, 15).map((category, index) => (
                  <Link
                  href={category.href}
                  className={`${styles.categoryItem} d-flex align-items-center text-decoration-none`}
                  key={`category-${index}`}
                >
                  <div className={`${styles.iconWrapper}`}>
                  </div>
                  <div className="text-wrap font-15 font-500 pl-2">{category.title}</div>
                </Link>
                ))}
                <Link
                  href="/kategorien"
                  className={`${styles.categoryItem} ${styles.viewAllButton} d-flex align-items-center text-decoration-none`}
                >
                  <div className="text-wrap font-15 font-500 pl-2 w-100 text-center">Alle anzeigen</div>
                </Link>
              </div>
            </div>

            {/* Coupons Column (1 item per row) */}
            <div className="col-lg-4 mb-4 mb-lg-0 pr-3">
              <h3 className={`${styles.heading} mb-2`}>Coupons</h3>
              <hr className={styles.sectionDivider} />
              <div className={styles.couponList}>
                {coupons.slice(0, 8).map((coupon, index) => (
                  <Link
                    href={coupon.href}
                    key={`coupon-${index}`}
                    className={`d-flex align-items-center ${styles.couponItem}`}
                  >
                    <div className={`${styles.couponIconWrapper} mr-3 d-flex align-items-center justify-content-center`}>
                    </div>
                    <span dangerouslySetInnerHTML={{
                      __html: coupon.title.replace(
                        /(\d+%|\d+€)/g,
                        '<strong>$1</strong>'
                      )
                    }}></span>
                  </Link>
                ))}
                <Link
                  href="/rabatte"
                  className={`d-flex align-items-center mb-2 ${styles.alleAnzeigenCoupon} justify-content-center`}
                >
                  <span className="py-1">Alle anzeigen</span>
                </Link>
              </div>
            </div>

            {/* Brands Column (3 items per row) */}
            <div className="col-lg-4">
              <h3 className={`${styles.heading} mb-2`}>Marken</h3>
              <hr className={styles.sectionDivider} />
              <div className="row">
                {Array.from({ length: 24 }).map((_, index) => (
                  <div className="col-4 mb-2" key={`brand-${index}`}>
                    {index === 23 ? (
                      <Link
                        href="/marken"
                        className={`d-flex align-items-center justify-content-center ${styles.alleAnzeigenMarken}`}
                      >
                        <span>Alle anzeigen</span>
                      </Link>
                    ) : (
                      <Link
                        href={`/marke/logo${index + 1}`}
                        className="d-flex align-items-center justify-content-center"
                      >
                        <div className={`${styles.logoItem} text-center`}>LOGO</div>
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MegaDropdown;