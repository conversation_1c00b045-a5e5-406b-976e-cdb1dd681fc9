/* Custom styles for the mega dropdown */
.categoryItem,
.couponItem {
  background-color: #ffffff;
  transition: background-color 0.2s ease;
  text-decoration: none;
  color: #212529;
  border-radius: 10px;
  border: 0.5px solid #707070;
  font-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 2px;
  margin-bottom: 10px;

  &:hover {
    text-decoration: none;
    color: #212529;
  }
}

/* Mobile specific styles for category items */
@media (max-width: 767.98px) {
  .categoryItem {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 16px;
    color: #333;
    text-decoration: none;

    &:hover {
      background-color: #f8f9fa;
      text-decoration: none;
      color: #333;
    }

    .mr-2 {
      margin-right: 12px !important;
      width: 40px;
      height: 40px;
      background-color: #b8f2f5;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .text-truncate {
      font-weight: 500;
      color: #333;
    }
  }
}

.alleAnzeigenItem {
  background-color: #F5F5F5;
  transition: background-color 0.2s ease;
  text-decoration: none;
  color: #212529;
  border-radius: 15px;
  border: 1px solid #707070;
  font-size: 16px;
}

.alleAnzeigenCoupon {
  background-color: #F5F5F5;
  transition: background-color 0.2s ease;
  text-decoration: none;
  color: #212529;
  border-radius: 15px;
  border: 1px solid #707070;
  font-size: 16px;
}

.alleAnzeigenMarken {
  text-decoration: none;
  color: #212529;
  font-size: 16px;
}

.iconWrapper {
  width: 56px;
  height: 62px;
  background-color: rgba(173, 245, 249, 0.7);
  border-radius: 9px;
  flex-shrink: 0;
}

.couponIconWrapper {
  width: 70px;
  height: 50px;
  background-color: rgba(173, 245, 249, 0.7);
  border-radius: 9px;
  flex-shrink: 0;
}

/* Mega dropdown menu wrapper */
.megaDropdownWrapper {
  /* This local class can be used as a wrapper for global styles */
  background-color: white;

  :global(.dropdown-menu.megaDropdownMenu) {
    position: absolute;
    top: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 80% !important;
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background-color: white;
  }

  @media (max-width: 767.98px) {
    padding: 0 !important;
    margin: 0 !important;

    :global(.container) {
      padding: 0;
    }

    :global(.container-fluid) {
      padding: 0 15px;
    }
  }
}

.megaDropdownMenu {
  position: absolute;
  top: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 80% !important;
  padding: 0;
  margin: 0;
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  background-color: white;
}

/* New styles for section dividers */
.sectionDivider {
  border: 0;
  border-top: 1px solid #000;
  margin-top: 0;
  margin-bottom: 1rem;
  width: 100%;
}

/* Make sure coupon items are full width */
.couponList {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Bold text for percentages and euro amounts */
.couponItem strong {
  font-weight: bold;
}

/* Add some space between the brand logos */
.logoItem {
  font-weight: 900;
  font-size: 22px;
  padding: 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60px;
}

.categoryContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  column-gap: 5px;
}

.categoryItem {
  flex: 0 0 calc(50% - 3px);
  margin-bottom: 3px;
}

.viewAllButton {
  background: #F5F5F5;
}

.heading {
  font-size: 22px;
  font-weight: 800;
}