import React, { useEffect, useState } from 'react';
import { ListGroupItem } from 'reactstrap';
import Row from 'reactstrap/lib/Row';
import Col from 'reactstrap/lib/Col';
import styles from './CartTotal.module.scss';

export default function CartTotal({ products = [] }) {
  const [total, setTotal] = useState(0);

  useEffect(() => {
    setTotal(calculateTotal(products));
  }, [products]);

  const calculateTotal = (products) => {
    return products.reduce(
      (total, product) => parseFloat(total) + parseFloat(product.initialAmount),
      0
    );
  };

  return (
    <ListGroupItem key={'total'} className={styles.paymentTotal}>
      <Row className="align-items-center">
        <Col xs={7} className={styles.paymentTotalAmount}>
          <p>GESAMT</p>
        </Col>
        <Col xs={3} className={styles.paymentTotalTax}>
          <p>(inkl. MwSt.)</p>
        </Col>
        <Col xs={2} className={styles.paymentTotalNumber}>
          <p>
            {`${total.toLocaleString('de-DE', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })} €`}
          </p>
        </Col>
      </Row>
    </ListGroupItem>
  );
}