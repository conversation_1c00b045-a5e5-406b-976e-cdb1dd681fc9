import { ListGroupItem } from 'reactstrap';
import { CodeType } from '../../util/helpers';
import styles from './CartItem.module.scss';

const CartItem = ({
  productPackageId,
  title,
  subtitle,
  initialAmount,
  imageLink,
}) => {
  // TO DO remove after this is fixed on BE
  const productName =
    title === CodeType.REGULAR || title === CodeType.GENERIC
      ? 'Süßwassermatrose'
      : title;
  const productDescription =
    title === CodeType.REGULAR
      ? '12-monatige Mitgliedschaft'
      : title === CodeType.GENERIC
      ? 'Kostenlose Jahresmitgliedschaft'
      : subtitle;

  return (
    <ListGroupItem key={productPackageId} className={styles.cartItem}>
      <div>
        <img
          className={styles.packageIcon}
          src={imageLink}
          alt="Ein illustratives Symbol zum Paket, z. B. eine Kapitänsmütze"
        ></img>
        <div className={styles.packageText}>
          <div className={`col-sm-12 col-lg-12 ${styles.packageName}`}>{productName}</div>
          <div className={`col-sm-12 col-lg-12 ${styles.packageDescription}`}>
            {productDescription}
          </div>
        </div>
        <div className={styles.packageAmount}>
          {`${initialAmount.toLocaleString('de-DE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })} €`}
        </div>
      </div>
    </ListGroupItem>
  );
};

export default CartItem;