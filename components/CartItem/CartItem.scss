.cartItem {
  border: 0px;
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding: 0.75rem 0.25rem;
  width: 100%;

  .package-icon {
    padding: 0;
    width: 18%;
    display: inline-block;
    vertical-align: top;
  }

  .package-text {
    padding-left: 1rem;
    width: 59%;
    display: inline-block;
  }

  .package-name {
    color: #098caf;
    font-size: 16px;
    font-weight: 900;
    line-height: 1.8ch;
    padding-left: 0;
  }

  .package-description {
    color: black;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.8ch;
    padding-left: 0;
  }

  .package-amount {
    width: 20%;
    color: black;
    height: 1rem;
    font-size: 18px;
    font-weight: 700;
    text-align: end;
    padding-left: 0;
    padding-top: 1rem;
    display: inline-block;
    vertical-align: top;
  }

  @media screen and (max-width: 420px) {
    .package-name {
      font-size: 14px;
    }

    .package-description {
      font-size: 14px;
    }

    .package-amount {
      font-size: 15px;
      padding-top: 0.9rem;
    }
  }
}
