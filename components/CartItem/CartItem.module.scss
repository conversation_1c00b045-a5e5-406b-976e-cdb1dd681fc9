.cartItem {
  border: 0px;
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding: 0.75rem 0.25rem;
  width: 100%;

  .packageIcon {
    padding: 0;
    width: 18%;
    display: inline-block;
    vertical-align: top;
  }

  .packageText {
    padding-left: 1rem;
    width: 59%;
    display: inline-block;
  }

  .packageName {
    color: #098caf;
    font-size: 16px;
    font-weight: 900;
    line-height: 1.8ch;
    padding-left: 0;
  }

  .packageDescription {
    color: black;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.8ch;
    padding-left: 0;
  }

  .packageAmount {
    width: 20%;
    color: black;
    height: 1rem;
    font-size: 18px;
    font-weight: 700;
    text-align: end;
    padding-left: 0;
    padding-top: 1rem;
    display: inline-block;
    vertical-align: top;
  }

  @media screen and (max-width: 420px) {
    .packageName {
      font-size: 14px;
    }

    .packageDescription {
      font-size: 14px;
    }

    .packageAmount {
      font-size: 15px;
      padding-top: 0.9rem;
    }
  }
}