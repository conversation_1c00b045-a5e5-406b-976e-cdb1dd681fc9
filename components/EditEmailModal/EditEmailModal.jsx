import React from 'react';
import { GrClose } from 'react-icons/gr';
import { ModalGeneric } from '../index';
import styles from './EditEmailModal.module.scss';

const EditEmailModal = ({
  isOpen = false,
  confirmBtnLabel = 'löschen',
  cancelBtnLabel = 'abbrechen',
  modalContent = 'wirklich löschen',
  emailToEdit = 'email',
  setIsModalOpen,
  onConfirm,
  ...props
}) => {
  return (
    <ModalGeneric modal={isOpen} className={styles.deleteEmailModal}>
      <button
        className="close float-right"
        onClick={() => setIsModalOpen(false)}
      >
        <GrClose />
      </button>
      <div className={styles.content}>
        <div className={styles.modalText}>
          <div>Möchtest du </div>
          <div>
            <b>{emailToEdit}</b>
          </div>
          <div>{modalContent}?</div>
        </div>
        <div className={styles.modalBtnGroup}>
          <button
            onClick={() => setIsModalOpen(false)}
            className="btn btn-dark"
          >
            {cancelBtnLabel}
          </button>
          <button onClick={onConfirm} className="btn btn-dark">
            {confirmBtnLabel}
          </button>
        </div>
      </div>
    </ModalGeneric>
  );
};

export default EditEmailModal;