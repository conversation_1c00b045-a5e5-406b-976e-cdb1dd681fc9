@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.footer {
  background-color: #81e9f0;
  line-height: 26px;
  
  .footerHeader {
    font-size: 18px;
    font-weight: bold;
  }
  
  a {
    color: #000;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }
}

.footerLinks {
  a {
    display: block;
  }
}

.footerSocial {
  a {
    &:hover {
      text-decoration: none;
    }

    svg {
      margin-left: 7px;
      display: inline-block;
      font-size: 18px;
    }
  }
}

.hoverWhite:hover {
  fill: #fff;
}

.footerSocialIcon {
  height: 20px;
  margin-right: 8px;
  &:hover {
    text-decoration: none;
  }
}

@media (max-width: 575px) {
  .footerContent {
    display: flex;
  }
}

@media (max-width: 575.98px) {
  .paddingSide25 {
    padding-left: 25px;
    padding-right: 25px;
  }
}