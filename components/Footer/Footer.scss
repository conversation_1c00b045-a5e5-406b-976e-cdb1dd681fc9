@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

.Footer {
  background-color: #81e9f0;
  line-height: 26px;
  .footer-header {
    font-size: 18px;
    font-weight: bold;
  }
  a {
    color: #000;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }
  &__links {
    a {
      display: block;
    }
  }
  &__social {
    a {
      &:hover {
        text-decoration: none;
      }

      svg {
        margin-left: 7px;
        display: inline-block;
        font-size: 18px;
      }
    }
  }

  .hover-white:hover {
    fill: #fff;
  }

  .footer-social-icon {
    height: 20px;
    margin-right: 8px;
    &:hover {
      text-decoration: none;
    }
  }

  @media (max-width: 575px) {
    .footer-content {
      display: flex;
    }
  }
  @media (max-width: 575.98px) {
    .padding-side-25 {
      padding-left: 25px;
      padding-right: 25px;
    }
  }
}
