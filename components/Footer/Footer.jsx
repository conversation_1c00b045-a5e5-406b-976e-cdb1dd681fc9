import Link from 'next/link';
import { useSelector } from 'react-redux';
import InstagramIcon from '../CustomIcons/InstagramIcon';
import styles from './Footer.module.scss';

const Footer = (props) => {
  const { useCustomTheme } = useSelector((state) => state.settings);

  return (
    <div className={`${styles.footer} row`}>
      <div className={`container pad-tb-6 ${styles.paddingSide25}`}>
        <div className={`row ${styles.footerContent}`}>
          <div
            className="col-sm-6 col-lg-3 pad-b-3 text-lg-left text-center"
            id="about"
          >
            <h4 className={styles.footerHeader}>AdsAvenue GmbH</h4>
            <p>
              Unter den Linden 10
              <br />
              10117 Berlin
            </p>
          </div>

          <div
            className="col-sm-6 col-lg-3 pad-b-3 text-lg-left text-center"
            id="contact"
          >
            <h4 className={styles.footerHeader}>Kontakt</h4>
            <p>
              030 3080 6387
              <br />
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </div>

          <div
            className="col-sm-6 col-lg-3 pad-b-3 text-lg-left text-center"
            id="business"
          >
            <h4 className={styles.footerHeader}>Business</h4>
            <div className={styles.footerLinks}>
              <a href=" http://www.traffico.de/unternehmen/">Unternehmen</a>
              <a href="http://www.traffico.de/partner/">Partner</a>
            </div>
          </div>

          {!useCustomTheme && (
            <div
              className="col-sm-6 col-lg-3 pad-b-3 text-lg-left text-center"
              id="links"
            >
              <h4 className={styles.footerHeader}>Links</h4>
              <div className={styles.footerLinks}>
                <>
                  <div className={styles.footerSocial}>
                    <a
                      href="https://www.instagram.com/captaincoupon.de/"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Folge uns
                      <InstagramIcon className={styles.hoverWhite} />
                    </a>
                  </div>
                  <Link href="/impressum" legacyBehavior>
                    <a>Impressum</a>
                  </Link>
                  <Link href="/datenschutz" legacyBehavior>
                    <a>Datenschutz</a>
                  </Link>
                  <Link href="/agb" legacyBehavior>
                    <a>AGB</a>
                  </Link>
                  <Link href="/faq" legacyBehavior>
                    <a>Häufig gestellte Fragen</a>
                  </Link>
                </>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Footer;