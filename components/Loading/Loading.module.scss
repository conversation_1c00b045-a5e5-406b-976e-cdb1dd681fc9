.loadingWrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  text-align: center;
  .loadingBar {
    max-width: 64px;
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    svg {
      max-width: 100%;
    }
  }
  &.blockLoading {
    position: static;
    height: auto;
    .loadingBar {
      position: static;
      transform: none;
      margin: 30px auto;
    }
  }
}