@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

@import '../../styles/sass/variables';

.registerCardTop {
  width: 50%;
}

.customCardTitle {
  font-size: 25px;
  color: #098caf;
  font-weight: 900;
  letter-spacing: 0px;
  margin-top: 30px;
}

.customCardSubtitle1 {
  font-size: 25px;
  color: #000000;
  font-weight: 900;
  letter-spacing: 0px;
}

.customCardSubtitle2 {
  font-size: 20px;
  color: #000000;
  letter-spacing: 0px;
}

.customCard {
  background: #f7f7f7 0% 0% no-repeat padding-box;
  border: 3px solid #f7f7f7;
  transition: all ease-out 0.2s;
  overflow: hidden;
  height: 100%;
  cursor: pointer;
  box-shadow: 3px 3px 8px rgb(0 0 0 / 16%);

  .Card {
    margin: 0;
    padding-bottom: 0;

    .cardBody {
      padding-bottom: 0;
    }

    @media (max-width: 576px) {
      padding-bottom: 0;

      img {
        margin-top: 0;
        padding-top: 1rem !important;
      }
    }
  }
}

@media (min-width: 768px) {
  .customCardSelected {
    transform: scale(1.08);
  }

  .customCard:hover:not(.customCardSelected) {
    opacity: 1;
    transform: scale(1.02);
    box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.6);
  }
}

.customCardSelected {
  border: 3px solid #098caf;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.4);
}

.customCardSentBack {
  opacity: 0.6;
}

.productRibbon {
  position: absolute;
  top: 35px;
  right: -91px;
  transform: rotate(35deg);
  background: transparent linear-gradient(90deg, #199ba6 0%, #006a9e 100%) 0% 0%
    no-repeat padding-box;
  color: black;
  font-size: 19px;
  font-weight: 900;
  color: #fff;
  letter-spacing: 0px;
  padding: 10px 100px;

  @media (max-width: 575px) {
    top: 23px;
    font-size: 14px;
    font-weight: 700;
  }
}

.customCardFromCoupon img.registerCardTop {
  max-width: 14rem;
}

.closeButton {
  margin-left: -45px;
  width: 30px;
  height: 30px;
  border-radius: 50%;

  &:hover {
    border: 1px solid #999999;
  }
}

.prodPackage {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;

  &:not(.customCardFromCoupon) {
    @include media-breakpoint-down(lg) {
      .productRibbon {
        top: 26px;
        right: -97px;
        font-size: 14px;
        padding: 5px 100px;
      }
    }

    @include media-breakpoint-down(md) {
      .productRibbon {
        top: 20px;
        right: -97px;
        font-size: 11px;
        padding: 4px 100px;
      }
    }

    @include media-breakpoint-down(sm) {
      padding: 10px;

      .customCard {
        border-width: 1px;
      }

      .registerCardTop {
        width: 75%;
      }

      .cardBody {
        padding: 10px;

        .customCardTitle {
          margin-top: 12px;
          margin-bottom: 12px;
        }

        .customCardTitle,
        .customCardSubtitle1 {
          font-size: 17px;
          line-height: 19px;
          word-wrap: break-word;
        }

        .customCardSubtitle2 {
          font-size: 10px;
          line-height: 12px;
          margin-bottom: 3px;
        }
      }

      .productRibbon {
        top: 18px;
        right: -97px;
        font-size: 10px;
        padding: 5px 100px;
      }
    }

    @include media-breakpoint-down(xs) {
      padding: 6px;

      .cardBody {
        padding: 3px;

        .customCardTitle {
          margin-top: 10px;
          margin-bottom: 7px;
        }

        .customCardTitle,
        .customCardSubtitle1 {
          font-size: 12px;
          line-height: 14px;
        }

        .customCardSubtitle2 {
          font-size: 8px;
          line-height: 10px;
        }
      }

      .productRibbon {
        font-size: 8px;
        padding: 2px 100px;
        top: 13px;
        right: -101px;
      }
    }
  }
}