@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

@import '../../styles/sass/variables';

.register-card-top {
  width: 50%;
}

.custom-card-title {
  font-size: 25px;
  color: #098caf;
  font-weight: 900;
  letter-spacing: 0px;
  margin-top: 30px;
}

.custom-card-subtitle-1 {
  font-size: 25px;
  color: #000000;
  font-weight: 900;
  letter-spacing: 0px;
}

.custom-card-subtitle-2 {
  font-size: 20px;
  color: #000000;
  letter-spacing: 0px;
}

.custom-card {
  background: #f7f7f7 0% 0% no-repeat padding-box;
  border: 3px solid #f7f7f7;
  transition: all ease-out 0.2s;
  overflow: hidden;
  height: 100%;
  cursor: pointer;
  box-shadow: 3px 3px 8px rgb(0 0 0 / 16%);

  .Card {
    margin: 0;
    padding-bottom: 0;

    .card-body {
      padding-bottom: 0;
    }

    @media (max-width: 576px) {
      padding-bottom: 0;

      img {
        margin-top: 0;
        padding-top: 1rem !important;
      }
    }
  }
}

@media (min-width: 768px) {
  .custom-card-selected {
    transform: scale(1.08);
  }

  .custom-card:hover:not(.custom-card-selected) {
    opacity: 1;
    transform: scale(1.02);
    box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.6);
  }
}

.custom-card-selected {
  border: 3px solid #098caf;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.4);
}

.custom-card-sent-back {
  opacity: 0.6;
}

.product-ribbon {
  position: absolute;
  top: 35px;
  right: -91px;
  transform: rotate(35deg);
  background: transparent linear-gradient(90deg, #199ba6 0%, #006a9e 100%) 0% 0%
    no-repeat padding-box;
  color: black;
  font-size: 19px;
  font-weight: 900;
  color: #fff;
  letter-spacing: 0px;
  padding: 10px 100px;

  @media (max-width: 575px) {
    top: 23px;
    font-size: 14px;
    font-weight: 700;
  }
}

.custom-card-from-coupon img.register-card-top {
  max-width: 14rem;
}

.close-button {
  margin-left: -45px;
  width: 30px;
  height: 30px;
  border-radius: 50%;

  &:hover {
    border: 1px solid #999999;
  }
}

.prod-package {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;

  &:not(.custom-card-from-coupon) {
    @include media-breakpoint-down(lg) {
      .product-ribbon {
        top: 26px;
        right: -97px;
        font-size: 14px;
        padding: 5px 100px;
      }
    }

    @include media-breakpoint-down(md) {
      .product-ribbon {
        top: 20px;
        right: -97px;
        font-size: 11px;
        padding: 4px 100px;
      }
    }

    @include media-breakpoint-down(sm) {
      padding: 10px;

      .custom-card {
        border-width: 1px;
      }

      .register-card-top {
        width: 75%;
      }

      .card-body {
        padding: 10px;

        .custom-card-title {
          margin-top: 12px;
          margin-bottom: 12px;
        }

        .custom-card-title,
        .custom-card-subtitle-1 {
          font-size: 17px;
          line-height: 19px;
          word-wrap: break-word;
        }

        .custom-card-subtitle-2 {
          font-size: 10px;
          line-height: 12px;
          margin-bottom: 3px;
        }
      }

      .product-ribbon {
        top: 18px;
        right: -97px;
        font-size: 10px;
        padding: 5px 100px;
      }
    }

    @include media-breakpoint-down(xs) {
      padding: 6px;

      .card-body {
        padding: 3px;

        .custom-card-title {
          margin-top: 10px;
          margin-bottom: 7px;
        }

        .custom-card-title,
        .custom-card-subtitle-1 {
          font-size: 12px;
          line-height: 14px;
        }

        .custom-card-subtitle-2 {
          font-size: 8px;
          line-height: 10px;
        }
      }

      .product-ribbon {
        font-size: 8px;
        padding: 2px 100px;
        top: 13px;
        right: -101px;
      }
    }
  }
}
