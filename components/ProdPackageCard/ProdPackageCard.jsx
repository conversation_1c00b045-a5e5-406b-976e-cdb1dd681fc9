import {
  Col,
  CardImg,
  CardBody,
  CardTitle,
  CardSubtitle,
  CardText,
} from 'reactstrap';
import Row from 'reactstrap/lib/Row';
import { AiOutlineClose } from 'react-icons/ai';
import { Card } from 'components';

import styles from './ProdPackageCard.module.scss';

const colWidth = {
  xs: 4,
  sm: 4,
  md: 4,
  lg: 4,
  xl: 4,
};

const getProductPackageLabel = (product, selectedFromCoupon) => {
  const { label, additionalInformation, position } = product;
  let productLabel = label || '';
  if (additionalInformation) {
    productLabel += '*'.repeat(selectedFromCoupon ? 1 : position);
  }
  return productLabel;
};

const ProdPackageCard = ({
  product,
  selected = false,
  selectedFromCoupon = false,
  sentBack = false,
  onClick = () => {},
  onClickSelected = () => {},
  onUnselectFromCoupon = () => {},
  className = '',
}) => (
  <Col
    {...(selectedFromCoupon ? { xs: 12 } : { ...colWidth })}
    className={className}
  >
    <Card
      className={`mt-2 ${styles.customCard} ${
        selected ? styles.customCardSelected : ''
      } ${
        selectedFromCoupon ? `mb-3 ${styles.customCardFromCoupon}` : ''
      } ${sentBack ? styles.customCardSentBack : ''}`}
      onClick={() => {
        if (selected || selectedFromCoupon) {
          onClickSelected();
        } else {
          onClick(product.productPackageId);
        }
      }}
    >
      {product.specialOffer ? (
        <div className={styles.productRibbon}>Sonderaktion</div>
      ) : null}
      {selectedFromCoupon ? (
        <Row className="float-right">
          <AiOutlineClose
            className={styles.closeButton}
            onClick={(event) => {
              event.stopPropagation();
              onUnselectFromCoupon();
            }}
          />
        </Row>
      ) : null}
      <CardImg
        src={
          product.imageLink?.length
            ? product.imageLink
            : 'images/register-1-1.png'
        }
        alt="Card image cap"
        className={styles.registerCardTop}
      />
      <CardBody className="pt-0">
        <CardTitle className={styles.customCardTitle}>{product.title}</CardTitle>
        {product.subtitle && (
          <CardSubtitle className={`${styles.customCardSubtitle2} p-1`}>
            {product.subtitle}
          </CardSubtitle>
        )}
        <CardText className={`${styles.customCardSubtitle1} p-1`}>
          {getProductPackageLabel(product, selectedFromCoupon)}
        </CardText>
      </CardBody>
    </Card>
  </Col>
);

export default ProdPackageCard;