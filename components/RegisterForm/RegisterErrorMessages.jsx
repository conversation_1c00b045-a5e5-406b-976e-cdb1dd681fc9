export const RegisterErrorMessages = {
  gender: '<PERSON><PERSON> ist ein <PERSON>flich<PERSON>',
  name: '<PERSON><PERSON> ist ein <PERSON>f<PERSON>',
  family_name: '<PERSON><PERSON> ist ein Pflich<PERSON>feld',
  street: 'Dies ist ein Pflichtfeld',
  houseNumber: 'Dies ist ein Pflichtfeld',
  email: 'Dies ist ein Pflichtfeld',
  required: 'Dies ist ein Pflichtfeld',
  maxLength: 'Die maximale Zeichenanzahl beträgt 64',
  postalCode: 'Dies ist ein Pflichtfeld',
  place: 'Dies ist ein Pflichtfeld',
  acceptTerms: 'Bitte bestätige die AGBs und Datenschutzbestimmungen',
  birthdate: 'Dies ist ein Pflichtfeld',
  confirmPass: 'Das Passwort stimmt nicht überein',
  passRequired:
    'Bitte wähle ein Passwort, das aus mindestens acht Zeichen besteht',
  passMinLength:
    'Bitte wähle ein Passwort, das aus mindestens acht Zeichen besteht',
  testLowerCase: 'muss mindestens einen Kleinbuchstaben haben',
  testSpecialCharacter: 'muss mindestens ein Sonderzeichen enthalten',
  emailAvailable:
    'Die eingegebene E-Mail-Adresse kommt uns bekannt vor. Melde dich an oder registriere dich mit einer anderen E-Mail-Adresse.',
};
