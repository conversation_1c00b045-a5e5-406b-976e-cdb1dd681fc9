import { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import DatePicker from 'reactstrap-date-picker';
import { useForm, Controller } from 'react-hook-form';

import { hasLowerCase, hasSpec<PERSON><PERSON><PERSON>cter } from 'util/helpers';
import { isAuth } from 'services/auth.service';

import { Loading } from 'components';
import ArrowDownIcon from '../CustomIcons/ArrowDownIcon';

import * as EmailErrors from 'components/SettingsSegments/EmailErrorMessages';

import styles from './RegisterForm.module.scss';
import pageStyles from '../../styles/sass/_registerPage.module.scss';

export default function RegisterForm({
  formData,
  allFormData = {},
  settingsPage = false,
  children = '',
  actionOnSubmit = () => {},
  submitButtonLabel = '',
  backButtonLabel = '',
  onBackButton,
}) {
  const isRegistered = isAuth();

  const {
    register,
    handleSubmit,
    formState: { errors, touchedFields },
    setError,
    control,
    getValues,
    setValue,
    reset,
  } = useForm({
    defaultValues: formData || {},
    mode: 'onBlur',
    reValidateMode: 'onBlur',
  });

  useEffect(() => {
    reset(formData);
  }, [formData, reset]);

  const [loading, setLoading] = useState(false);
  const [password, setThePassword] = useState(() => allFormData?.password);
  const [registerError, setRegisterError] = useState('');
  const emailError = useRef();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      await actionOnSubmit(data);
      setLoading(false);
    } catch (error) {
      setLoading(false);

      if (error.response?.data === EmailErrors.EMAIL_EXISTS_en) {
        setError('email', {
          type: 'emailAvailable',
          message:
            'Die eingegebene E-Mail-Adresse kommt uns bekannt vor. Melde dich an oder registriere dich mit einer anderen E-Mail-Adresse.',
        });

        if (emailError) {
          window.scrollTo(0, emailError.current?.offsetTop);
        }
      } else if (
        error.response?.data === 'Error occurred while trying to create account'
      ) {
        setRegisterError('Error occurred while trying to create account');
        setTimeout(async () => {
          setRegisterError('');
        }, 9000);
      }
    }
  };

  // Create the format function outside of any components
  const formatDateInput = (input) => {
    // Remove all non-digits
    const digitsOnly = input.replace(/\D/g, '');
    
    // Limit to max 8 digits (DDMMYYYY)
    const limitedDigits = digitsOnly.slice(0, 8);
    
    // Apply formatting based on length
    if (limitedDigits.length <= 2) {
      return limitedDigits;
    } else if (limitedDigits.length <= 4) {
      return `${limitedDigits.slice(0, 2)}.${limitedDigits.slice(2)}`;
    } else {
      return `${limitedDigits.slice(0, 2)}.${limitedDigits.slice(2, 4)}.${limitedDigits.slice(4, 8)}`;
    }
  };

  const birthdateField = (
    <div className="form-group">
      <Controller
        control={control}
        name="dateOfBirth"
        defaultValue={getValues('dateOfBirth') || ''}
        rules={{
          validate: function (value) {
            const bday = value;
            
            if (!bday || bday === '') {
              return 'Dies ist ein Pflichtfeld';
            }
            
            // Check if it's a valid date in DD.MM.YYYY format
            const datePattern = /^(\d{2})\.(\d{2})\.(\d{4})$/;
            const matches = bday.match(datePattern);
            
            if (matches) {
              const day = parseInt(matches[1], 10);
              const month = parseInt(matches[2], 10) - 1; // JS months are 0-11
              const year = parseInt(matches[3], 10);
              
              const dateObj = new Date(year, month, day);
              
              // Verify the date is valid and in the past
              if (
                dateObj.getDate() === day &&
                dateObj.getMonth() === month &&
                dateObj.getFullYear() === year &&
                dateObj < new Date()
              ) {
                return true;
              }
            }
            
            return `Ungültiges Geburtsdatum`;
          },
        }}
        render={({ field }) => {
          // Use a variable instead of state for the placeholder
          // This will work for initial render
          let placeholderText = "Geburtsdatum*";
          
          return (
            <input
              className="form-control custom-form-control"
              style={{
                border: '1px solid #000',
              }}
              placeholder={placeholderText}
              value={field.value || ''}
              onChange={(e) => {
                const formattedValue = formatDateInput(e.target.value);
                setValue('dateOfBirth', formattedValue, { shouldValidate: false });
                field.onChange(formattedValue);
              }}
              onFocus={(e) => {
                // Change the placeholder attribute directly instead of using state
                e.target.placeholder = "DD.MM.YYYY";
              }}
              onBlur={(e) => {
                // Reset placeholder on blur if empty
                if (!e.target.value) {
                  e.target.placeholder = "Geburtsdatum*";
                }
                field.onBlur();
              }}
              maxLength={10}
            />
          );
        }}
      />
      {errors?.dateOfBirth && (
        <p className="inline-error pad-t-1">{errors.dateOfBirth.message}</p>
      )}
    </div>
  );

  return (
    <>
      {loading ? <Loading /> : null}
      {registerError && (
        <div className="alert alert-danger d-block mar-t-1 w-100">
          {registerError}
        </div>
      )}
      <form
        onSubmit={handleSubmit(onSubmit)}
        id="registerForm"
        className={`${styles.registerForm} ${settingsPage ? 'row' : undefined}`}
      >
        <input {...register('registrationCode')} hidden={true} />
        <input {...register('productPackageId')} hidden={true} />

        {!settingsPage && (
          <>
            <div className={`col-sm-12 register-substep-one ${styles.registerSubStep}`}>
              <div className="row">
                <div className={`col-md-12 col-lg-3 ${pageStyles.registerSubSteps}`}>
                  <div className="row">
                    <div className="col-md-12 step-wrapper">
                      <h2>SCHRITT </h2>
                      <img src="/icons/1.svg" alt="Die Ziffer „1 in türkis" />
                    </div>
                  </div>
                </div>

                <div className="col-md-12 offset-md-0 col-lg-8 offset-lg-1">
                  <div className="row">
                    <h2 className={`col-sm-12 secondary-header--bold ${styles.desktopOnlyLabel} register-subheader`}>
                      Logindaten
                    </h2>
                    <h2 className={`col-sm-12 secondary-header--bold ${styles.mobileOnlyLabel} pad-b-2 pad-t-1 register-subheader`}>
                      Logindaten
                    </h2>
                  </div>
                  {/* TODO check .facebookUser logic when federated login is working  */}

                  {!settingsPage && (
                    <>
                      <div className="row">
                        <div className="col-sm-6">
                          <div className="form-group">
                            {allFormData.facebookUser ? (
                              <>
                                <img
                                  src="facebook_label.png"
                                  alt="Das Facebook-„f-Logo und ein Schriftzug „eingeloggt über Facebook"
                                  className={styles.fbImg}
                                />
                                <p
                                  className="pad-t-3"
                                  style={{
                                    paddinBottom: '4.6rem',
                                  }}
                                >
                                  Du bist mit Facebook eingeloggt. Alle
                                  Benachrichtiguangen von CaptainCoupon werden
                                  an die E-Mail Adresse versendet, mit der du
                                  dich auf Facebook registriert hast.
                                </p>
                              </>
                            ) : (
                              <>
                                <input
                                  readOnly={
                                    !!settingsPage || allFormData.facebookUser
                                  }
                                  type="email"
                                  autoComplete="new-password"
                                  className="form-control"
                                  placeholder="E-Mail-Adresse*"
                                  disabled={isRegistered}
                                  {...register('email', {
                                    required: !isRegistered
                                      ? 'Dies ist ein Pflichtfeld'
                                      : false,
                                    maxLength: {
                                      value: 64,
                                      message:
                                        'Die maximale Zeichenanzahl beträgt 64',
                                    },
                                  })}
                                />
                                {errors.email && (
                                  <p
                                    className="inline-error pad-t-1"
                                    ref={emailError}
                                  >
                                    {errors.email.message}
                                  </p>
                                )}
                              </>
                            )}
                          </div>
                        </div>

                        <div className="col-sm-6">
                          <div className="form-group">
                          <Controller
                            name="password"
                            control={control}
                            rules={{
                              required: !isRegistered ? 'Dies ist ein Pflichtfeld' : false,
                              maxLength: {
                                value: 32,
                                message: 'Die maximale Zeichenanzahl beträgt 32',
                              },
                              minLength: {
                                value: 8,
                                message: 'Bitte wähle ein Passwort, das aus mindestens acht Zeichen besteht',
                              },
                              validate: {
                                testSpecialCharacter: (str) => hasSpecialCharacter(str, isRegistered),
                                testLowerCase: (str) => hasLowerCase(str, isRegistered),
                              },
                            }}
                            render={({ field }) => (
                              <input
                                type="password"
                                autoComplete="new-password"
                                className="form-control"
                                placeholder="Passwort*"
                                disabled={isRegistered}
                                onChange={(e) => {
                                  console.log(e.target.value, 'field changed')
                                  field.onChange(e);
                                  setThePassword(e.target.value);
                                }}
                                value={field.value || ''}
                                onBlur={field.onBlur}
                                ref={field.ref}
                              />
                            )}
                          />
                            {errors.password && (
                              <p className="inline-error pad-t-1">
                                {errors.password.message}
                                {errors.password.type === 'testSpecialCharacter'
                                  ? 'Muss mindestens ein Sonderzeichen enthalten.'
                                  : errors.password.type === 'testLowerCase' &&
                                    'Muss mindestens einen Kleinbuchstaben haben.'}
                              </p>
                            )}
                          </div>
                          <div className="form-group">
                            <input
                              type="password"
                              autoComplete="new-password"
                              className="form-control"
                              placeholder="Passwort wiederholen*"
                              disabled={isRegistered}
                              {...register('confirmPass', {
                                validate: value => {
                                  console.log(value, password)
                                  return (isRegistered ? true : (value === password || 'Das Passwort stimmt nicht überein'))
                                }
                              })}
                            />
                            {errors.confirmPass && (
                              <p className="inline-error pad-t-1">
                                Das Passwort stimmt nicht überein
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
        <div
          className={`${
            !settingsPage
              ? `register-substep-two ${styles.registerSubStep}`
              : styles.registerSubstepSettings
          } col-sm-12`}
        >
          <div className="row">
            {!settingsPage && (
              <div className={`col-md-12 col-lg-3 ${pageStyles.registerSubSteps}`}>
                <div className="row">
                  <div className={`col-md-12 step-wrapper`}>
                    <h2>SCHRITT </h2>
                    <img src="/icons/2.svg" alt="Die Ziffer „2 in türkis" />
                  </div>
                </div>
              </div>
            )}

            <div
              className={
                !settingsPage
                  ? `col-md-12 offset-md-0 col-lg-8 offset-lg-1 ${styles.registerPersonalInfo}`
                  : 'col-sm-12'
              }
            >
              <div className="row">
                {!settingsPage ? (
                  <h2 className="col-xs-12 col-sm-6 col-sm-12 secondary-header--bold register-subheader">
                    Kontaktdaten
                  </h2>
                ) : (
                  <div className="edit-block-header col-sm-12 mt-3">
                    <b>Kontakt</b>
                  </div>
                )}
              </div>

              <div className="row">
                <div className="col-sm-6 registerPersonalInfoGender">
                  <label className="register-label">Anrede*</label>

                  <div
                    className={
                      settingsPage ? 'form-group mar-b-2' : 'form-group mar-b-3'
                    }
                  >
                    <select
                      className="form-control"
                      {...register('gender', { 
                        required: 'Dies ist ein Pflichtfeld' 
                      })}
                    >
                      <option value="">- Bitte wählen -</option>{' '}
                      <option value="male">Herr</option>
                      <option value="female">Frau</option>
                      <option value="divers">Divers</option>
                    </select>
                    <ArrowDownIcon
                      height="20px"
                      className="select-icon"
                    ></ArrowDownIcon>
                    {errors.gender && (
                      <p className="inline-error pad-t-1">
                        {errors.gender.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`${styles.registerPersonalInfoBirthdate} col-sm-6`}>
                  {!settingsPage && birthdateField}
                </div>
                <div className={`col-sm-6 ${styles.registerPersonalInfoName}`}>
                  <div
                    className={
                      settingsPage ? 'form-group mar-b-2' : 'form-group mar-b-3'
                    }
                  >
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Vorname*"
                      {...register('firstName', {
                        required: 'Dies ist ein Pflichtfeld',
                        maxLength: {
                          value: 64,
                          message: 'Die maximale Zeichenanzahl beträgt 64',
                        },
                      })}
                    />
                    {errors.firstName && (
                      <p className="inline-error pad-t-1">
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`${styles.registerPersonalInfoSurname} col-sm-6`}>
                  <div
                    className={
                      settingsPage ? 'form-group mar-b-2' : 'form-group mar-b-3'
                    }
                  >
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Name*"
                      {...register('surname', {
                        required: 'Dies ist ein Pflichtfeld',
                        maxLength: {
                          value: 64,
                          message: 'Die maximale Zeichenanzahl beträgt 64',
                        },
                      })}
                    />
                    {errors.surname && (
                      <p className="inline-error pad-t-1">
                        {errors.surname.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`${styles.registerPersonalInfoStreet} col-sm-6`}>
                  <div
                    className={
                      settingsPage ? 'form-group mar-b-2' : 'form-group mar-b-3'
                    }
                  >
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Straße"
                      {...register('street', {
                        maxLength: {
                          value: 128,
                          message: 'Die maximale Zeichenanzahl beträgt 128',
                        },
                      })}
                    />
                    {errors.street && (
                      <p className="inline-error pad-t-1">
                        {errors.street.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`${styles.registerPersonalInfoHousenumber} col-sm-6`}>
                  <div
                    className={
                      settingsPage ? 'form-group mar-b-2' : 'form-group mar-b-3'
                    }
                  >
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Hausnummer"
                      {...register('houseNumber', {
                        maxLength: {
                          value: 16,
                          message: 'Die maximale Zeichenanzahl beträgt 16',
                        },
                      })}
                    />
                    {errors.houseNumber && (
                      <p className="inline-error pad-t-1">
                        {errors.houseNumber.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`${styles.registerPersonalInfoPostalcode} col-sm-6`}>
                  <div className="form-group mar-b-2">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="PLZ"
                      {...register('postalCode', {
                        maxLength: {
                          value: 16,
                          message: 'Die maximale Zeichenanzahl beträgt 16',
                        },
                      })}
                    />
                    {errors.postalCode && (
                      <p className="inline-error pad-t-1">
                        {errors.postalCode.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`${styles.registerPersonalInfoPlace} col-sm-6`}>
                  <div className="form-group mar-b-2">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Ort"
                      {...register('place', {
                        maxLength: {
                          value: 64,
                          message: 'Die maximale Zeichenanzahl beträgt 64',
                        },
                      })}
                    />
                    {errors.place && (
                      <p className="inline-error pad-t-1">
                        {errors.place.message}
                      </p>
                    )}
                  </div>
                </div>
                {settingsPage && (
                  <>
                    <div className="d-block col-sm-12 mar-t-2 info-card edit-block-header ">
                      <b>Geburtstag</b>
                    </div>
                    <div className="col-sm-12 mar-b-2">{birthdateField}</div>
                  </>
                )}
              </div>
              {!settingsPage && (
                <div className="row">
                  <div className={`col-sm-12 ${styles.desktopOnlyLabel}`}>
                    <label>*Pflichtfelder</label>
                  </div>
                  <div className={`col-sm-12 ${styles.mobileOnlyLabel}`}>
                    <small>*Pflichtfelder</small>
                  </div>
                </div>
              )}
              {!settingsPage && (
                <>
                  <div className="row mt-3 p-3">
                    <div>
                      <input
                        className={styles.formCheckInputInline}
                        type="checkbox"
                        value="1"
                        id="defaultCheck1"
                        disabled={isRegistered}
                        {...register('acceptTerms', {
                          required: 'Dies ist ein Pflichtfeld',
                        })}
                      />
                      <label className={styles.formCheckInputInline}> *</label>
                      <label
                        className={`${styles.formCheckLabel} text-s`}
                        htmlFor="defaultCheck1"
                      >
                        Hiermit bestätigst Du, dass Du die{' '}
                        <Link href="/datenschutz" className={styles.customLink} target="_blank">
                          <b>Datenschutzerklärung</b>
                        </Link>{' '}
                        gelesen hast und akzeptierst.
                      </label>
                      {errors.acceptTerms && (
                        <p className="inline-error pad-t-1">
                          {errors.acceptTerms.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <input
                        className={styles.formCheckInputInline}
                        type="checkbox"
                        value="1"
                        id="defaultCheck2"
                        disabled={isRegistered}
                        {...register('acceptTerms2', {
                          required: 'Dies ist ein Pflichtfeld',
                        })}
                      />
                      <label className={styles.formCheckInputInline}> *</label>
                      <label
                        className={`${styles.formCheckLabel} text-s`}
                        htmlFor="defaultCheck2"
                      >
                        Hiermit bestätigst Du, dass Du die{' '}
                        <Link href="/agb" className={styles.customLink} target="_blank">
                          <b>Nutzungsbedingungen</b>
                        </Link>{' '}
                        gelesen hast und akzeptierst.
                      </label>
                      {errors.acceptTerms2 && (
                        <p className="inline-error pad-t-1">
                          {errors.acceptTerms2.message}
                        </p>
                      )}
                    </div>

                    {false && 
                      <div>
                        <input
                          className={styles.formCheckInputInline}
                          type="checkbox"
                          id="defaultCheck3"
                          value="1"
                          {...register('isNewslettersSubscribed')}
                        />
                        <label
                          className={`${styles.formCheckLabel} text-s`}
                          htmlFor="defaultCheck3"
                        >
                          Hiermit meldest Du dich kostenfrei zu unserem Newsletter
                          an und bestätigst, dass du regelmäßig über neue Partner,
                          Blog-Posts und Angebote informiert werden willst. Deine
                          E-Mail-Adresse wird nicht an Dritte weitergereicht. Du
                          kannst den Newsletter jederzeit abbestellen.
                        </label>
                      </div>
                    }
                  </div>
                </>
              )}

              <div className={`${!settingsPage && 'pad-t-4 mar-b-3'} row`}>
                {settingsPage && (
                  <>
                    {children ? (
                      <div className="col-sm-6">{children}</div>
                    ) : null}
                    <div className="col-sm-12 text-right">
                      <button
                        className="btn btn-dark group-button"
                        type="submit"
                      >
                        {submitButtonLabel}
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
          {!settingsPage && (
            <div className="row">
              <div className="col-sm-12 text-right d-flex justify-content-xs-center justify-content-md-between">
                <button
                  className={`btn btn-dark btn-tall col-md-7 col-lg-4 ${styles.registerAction} m-1 ${styles.registerButton}`}
                  type="button"
                  onClick={() => {
                    onBackButton(getValues());
                  }}
                >
                  {backButtonLabel}
                </button>
                <button
                  className={`btn btn-dark btn-tall col-md-7 col-lg-4 ${styles.registerAction} m-1 ${styles.registerButton}`}
                  type="submit"
                  style={{ margin: 'auto 0 auto auto' }}
                >
                  {submitButtonLabel}
                </button>
              </div>
            </div>
          )}
        </div>
      </form>
    </>
  );
}