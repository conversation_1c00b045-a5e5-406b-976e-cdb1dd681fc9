@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import '../../styles/sass/mixins';

.registerAction {
  max-width: 177px;
}

.mobileOnlyLabel {
  display: none;
}

.customLink {
  cursor: pointer;
}

.rdpOverlay {
  display: none;
}

.rightBoxReg {
  padding-top: 2rem;
  padding-left: 20%;
  padding-right: 20%;
}

.desktopOnlyLabel {
  line-height: 20px;
  font-size: 14px;
}

.settingsPersonalInfoBirthdate {
  order: 8;
}

.registerPersonalInfo {
  display: flex;
  flex-direction: column;

  @media screen and (min-width: 575px) {
    &Birthdate {
      margin-top: auto;
    }
  }

  @media screen and (max-width: 575px) {
    &Name {
      order: 2;
    }
  
    &Surname {
      order: 3;
    }
  
    &Birthdate {
      order: 4;
    }
  
    &Street {
      order: 5;
    }
  
    &Housenumber {
      order: 6;
    }
  
    &Postalcode {
      order: 7;
    }
  
    :global(.customLink) {
      order: 8;
    }
  }  
  
}

@include media-breakpoint-down(xs) {
  .desktopOnlyLabel {
    display: none;
  }

  .mobileOnlyLabel {
    display: inline-block;
  }

  .rightBoxReg {
    padding-top: 1rem;
    padding-left: 10%;
    padding-right: 10%;
  }
}

.registerForm {
  :global(.register-subheader) {
    line-height: 35px;
    margin-bottom: 15px;
    font-size: 25px;
  }
  
  :global(.register-substep-one) :global(.register-subheader) {
    margin-bottom: 35px;
  }
  
  :global(.register-substep-two) {
    :global(.step-wrapper) {
      min-width: 318px;
    }
  }
  
  :global(.register-substep-one) {
    :global(.step-wrapper) {
      min-width: 240px;
    }
  }
  
  :global(.form-check-input) {
    width: 20px;
    height: 20px;
  }
  
  .formCheckInputInline {
    display: inline;
    width: 20px;
    height: 20px;
    margin-bottom: 10px;
  }
  
  .formCheckLabel {
    display: inline;
    font-size: 12px;
    line-height: 20px;
    margin-left: 15px;
  }

}

.grayBoxSettings:global(.grayBox) {
  :global(b) {
    padding-bottom: 0;
    display: block;
    font-size: 20px;
    line-height: 32px;
  }
  
  :global(p) {
    line-height: 24px;
    font-size: 14px;
    margin-bottom: 0px;
  }
  
  :global(#registerForm) {
    :global(.register-label) {
      line-height: 28px;
      font-size: 16px;
      margin-bottom: 5px;
    }
  }
}

.registerButton {
  @include mediaMobile {
    font-size: 14px;
  }
}

.registerSubStep {
  padding: 75px 50px 75px 50px;
  
  &:global(.register-substep-one) {
    background-color: #f7f7f7;
    :global(img) {
      height: 175px;
      width: 82px;
    }
  }
  
  @include media-breakpoint-up(xs) {
    padding: 75px 20px 75px 20px;
  }

  @include media-breakpoint-up(md) {
    &:global(.register-substep-one) {
      margin-top: 60px;
    }
  }
  
  &:global(.register-substep-two) {
    margin-top: 30px;
    :global(img) {
      height: 179px;
      width: 145px;
      margin-left: 20px;
    }
  }
}