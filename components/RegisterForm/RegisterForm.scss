@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import '../../styles/sass/mixins';

.register-action {
  max-width: 177px;
}

.mobile-only-label {
  display: none;
}

.custom-link {
  cursor: pointer;
}

.rdp-overlay {
  display: none;
}

.right-box-reg {
  padding-top: 2rem;
  padding-left: 20%;
  padding-right: 20%;
}
.desktop-only-label {
  line-height: 20px;
  font-size: 14px;
}

.settings-personal-info-birthdate {
  order: 8;
}

.register-personal-info {
  display: flex;
  flex-direction: column;

  @media screen and (min-width: 575px) {
    &-birthdate {
      margin-top: auto;
    }
  }

  @media screen and (max-width: 575px) {
    &-name {
      order: 2;
    }

    &-surname {
      order: 3;
    }

    &-birthdate {
      order: 4;
    }

    &-street {
      order: 5;
    }

    &-housenumber {
      order: 6;
    }

    &-postalcode {
      order: 7;
    }

    &-place {
      order: 8;
    }
  }
}

@include media-breakpoint-down(xs) {
  .desktop-only-label {
    display: none;
  }

  .mobile-only-label {
    display: inline-block;
  }

  .right-box-reg {
    padding-top: 1rem;
    padding-left: 10%;
    padding-right: 10%;
  }
}
#registerForm {
  .register-subheader {
    line-height: 35px;
    margin-bottom: 15px;
    font-size: 25px;
  }
  .register-substep-two .register-subheader {
    margin-bottom: 35px;
  }
  .register-substep-two {
    .step-wrapper {
      min-width: 318px;
    }
  }
  .register-substep-one {
    .step-wrapper {
      min-width: 240px;
    }
  }
  .form-check-input {
    width: 20px;
    height: 20px;
  }
  .form-check-input-inline {
    display: inline;
    width: 20px;
    height: 20px;
    margin-bottom: 10px;
  }
  .form-check-label {
    display: inline;
    font-size: 12px;
    line-height: 20px;
    margin-left: 15px;
  }

  .fb-img {
    height: 50px;
    width: 370px;
  }
}
.gray-box-settings.gray-box {
  b {
    padding-bottom: 0;
    display: block;
    font-size: 20px;
    line-height: 32px;
  }
  p {
    line-height: 24px;
    font-size: 14px;
    margin-bottom: 0px;
  }
  #registerForm {
    .register-label {
      line-height: 28px;
      font-size: 16px;
      margin-bottom: 5px;
    }
  }
}

.register-button {
  @include mediaMobile {
    font-size: 14px;
  }
}
