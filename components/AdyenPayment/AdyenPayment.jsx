import React, { useState, useCallback, useEffect, useReducer } from 'react';
import { ListGroup, ListGroupItem } from 'reactstrap';

import { AdyenDropin } from './AdyenDropin';
import { Loading } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import reducer, { ADYEN_INITIAL_STATE } from './Adyen.reducer';
import * as adyenActions from './Adyen.actions';
import * as adyenService from 'services/adyen.service';

import styles from './AdyenPayment.module.scss';
import CartTotal from '../CartTotal/CartTotal';

export const badAdyenResultCodes = [
  null,
  'Refused',
  'Error',
  'Cancelled',
  'Failed',
];
export const REDIRECT_ACTION = 'redirect';
export const adyenMessages = {
  Refused: 'Deine Zahlung wurde abgelehnt',
  Authorised: 'Dein Zahlung war erfolgreich',
  'There is already an active or pending subscription':
    'Du hast bereits eine aktive Mitgliedschaft oder es gibt Probleme mit der ausgewählten Zahlungsmethode',
  Pending:
    'Deine Zahlung wird derzeit verarbeitet. Wenn diese Mitteilung länger als 5 Minuten sichtbar ist wähle bitte eine andere Zahlmethode oder kontaktiere unseren <NAME_EMAIL> oder telefonisch unter 030 3080 6387.',
};

export default function AdyenPayment({
  shoppingCart,
  preSubmitAction = () => {},
  handleAdyenResponse,
  CartItem,
  productType = 'PACKAGE',
}) {
  const { addMessage } = useAPIError();

  const [
    { loading, error, config, paymentMethodsRes, orderReferences, paymentRes },
    dispatch,
  ] = useReducer(reducer, ADYEN_INITIAL_STATE);

  const [dropInComponent, setDropInComponent] = useState(null);

  const updateDropInStatus = useCallback(
    function (paymentRes, error, dropIn) {
      if (error) {
        addMessage(
          adyenMessages[error?.response?.data] || error.message,
          'error'
        );
        dropIn.setStatus('error', {
          message: adyenMessages[error?.response?.data] || error.message,
        });
      } else {
        if (paymentRes?.action?.type === REDIRECT_ACTION) {
          addMessage('Payment type not yet implemented!', 'error');
          dropIn.setStatus('error', {
            message: 'Payment type not yet implemented!',
          });
        } else {
          if (!badAdyenResultCodes.includes(paymentRes.resultCode)) {
            addMessage(
              adyenMessages[paymentRes.resultCode] || paymentRes.resultCode,
              'success'
            );
            dropIn.setStatus('success', {
              message:
                adyenMessages[paymentRes.resultCode] || paymentRes.resultCode,
            });
          } else {
            if (paymentRes.resultCode === 'Error') {
              addMessage(`Error: ${paymentRes.refusalReason}`, 'error');
              dropIn.setStatus('error', {
                message: `Error: ${paymentRes.refusalReason}`,
              });
            } else {
              addMessage(
                adyenMessages[paymentRes.resultCode] ||
                  `Payment status: ${paymentRes.resultCode}`,
                'error'
              );
              dropIn.setStatus('error', {
                message:
                  adyenMessages[paymentRes.resultCode] ||
                  `Payment status: ${paymentRes.resultCode}`,
              });
            }
          }
        }
      }
    },
    [addMessage]
  );

  useEffect(() => {
    adyenActions.initPaymentDropIn(dispatch, productType, shoppingCart);
  }, [shoppingCart]);

  useEffect(() => {
    if (paymentRes || error) {
      if (handleAdyenResponse) {
        if (dropInComponent) {
          updateDropInStatus(paymentRes, error, dropInComponent);
        }
        handleAdyenResponse(paymentRes, error);
      }
    }
  }, [paymentRes, error]);

  const onSubmit = useCallback(
    async function (dropInState, dropIn) {
      setDropInComponent(dropIn);
      try {
        await preSubmitAction();
        if (!orderReferences?.length) {
          adyenActions.setPaymentError(dispatch, 'Shopping cart is empty!');
        }
        if (orderReferences?.length && dropInState.isValid) {
          adyenActions.initiatePayment(
            dispatch,
            {
              paymentMethod: dropInState.data.paymentMethod,
              productType: productType,
              orders: orderReferences,
              returnUrl: process.env.NEXT_PUBLIC_ADYEN_REDIRECT_COMPLETED,
            },
            dropIn
          );
        }
      } catch (error) {
        adyenActions.setPaymentError(
          dispatch,
          error?.response?.data || 'Error while executing pre-submit action'
        );
      }
    },
    [orderReferences, productType]
  );

  const onAdditionalDetails = function (dropInState, dropIn) {
    // Your function to submit a state.data object to the payments/details endpoint.
    adyenActions.onAdditionalDetails(
      dispatch,
      { ...dropInState.data, orders: orderReferences, productType },
      dropIn
    );
  };

  const onError = (state, dropin) => {
    if (dropin) {
      if (dropin.props.name === 'PayPal') {
        console.log(state);
      }

      // Sets your prefered status of Drop-in when an error occurs. In this example, return to the initial state.
      dropin.setStatus('ready');
    }
  };

  const onCancel = () => {
    adyenService.cancelPayment({
      productType: productType,
      orders: orderReferences,
    });
  };

  return loading ? (
    <Loading />
  ) : shoppingCart?.length ? (
    <div className={styles.paymentColumns}>
      <div className={`col-sm-12 col-md-6 ${styles.paymentDropin}`}>
        <h6>
          <b>Bitte wähle deine Zahlmethode</b>
        </h6>
        <AdyenDropin
          configuration={config}
          paymentMethodsRes={paymentMethodsRes}
          onAdditionalDetails={onAdditionalDetails}
          onSubmit={onSubmit}
          onError={onError}
          onCancel={onCancel}
        />
      </div>
      <div className={`col-sm-12 col-md-6 ${styles.paymentCart}`}>
        <h6>
          <b>Bestellung</b>
        </h6>
        <ListGroup>
          {shoppingCart.map((product) =>
            CartItem ? (
              <CartItem {...product} />
            ) : (
              <ListGroupItem key={product.id}>
                ID: {product.id}, price: {product.price}
              </ListGroupItem>
            )
          )}
          <CartTotal products={shoppingCart} />
        </ListGroup>
      </div>
    </div>
  ) : (
    <h6>Empty shopping cart</h6>
  );
}
