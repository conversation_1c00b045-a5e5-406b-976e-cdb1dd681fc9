import React, { useEffect, useRef } from 'react';

const adyenVersion = '4.5.0';
const adyenIntegrity = {
  '3.21.1': {
    css: 'sha384-KM3xJKzswGJ0xqiPCOHrWUtn0i0LHqEngauvYxSfy0eRtqcImL7ArxjV2HVgeRJ/',
    js: 'sha384-qgB03MgLihAbvkTmWIkmZxFUxyAqJ4Ozk1My6beIRqit6+8e5HFg8ysln5y5FSw0',
  },
  '4.5.0': {
    css: 'sha384-8EGo5meqBqlQ4MFf3nbQYD/onCuTfclYfNl3a5uQ1swwv0XXcTkda75dvjlYbZI8',
    js: 'sha384-Co94gRjtPsf3110lIIB8CaogV5Khwg8lcSh4fK5r1gzfWZHxaqpBXwEFjaFGcLaj',
  },
};

export function AdyenDropin({
  configuration,
  paymentMethodsRes,
  onAdditionalDetails,
  onSubmit,
  onError,
  onCancel,
  ...attributes
}) {
  const dropinRef = useRef();

  const initAdyenCheckout = function () {
    const checkout = new window.AdyenCheckout({
      locale: configuration.locale,
      environment: process.env.NEXT_PUBLIC_ADYEN_ENV,
      clientKey: configuration.clientKey,
      returnUrl: process.env.NEXT_PUBLIC_ADYEN_REDIRECT_COMPLETED,
      showPayButton: true,
      paymentMethodsResponse: paymentMethodsRes,
      removePaymentMethods: [],
      amount: {
        value: configuration?.amount?.value,
        currency: configuration?.amount?.currency,
      },
      onSubmit,
      onAdditionalDetails,
      onError,
      onCancel: (data, dropin) => {
        dropin.setStatus('ready');
        onCancel();
        // Sets your prefered status of the Drop-in component when a PayPal payment is cancelled. In this example, return to the initial state.
      },
      paymentMethodsConfiguration: {
        card: {
          hasHolderName: true,
          holderNameRequired: true,
        },
        paypal: {
          // Required configuration for PayPal
          environment: configuration.environment, // 'test'
          countryCode: 'DE', // Only needed for test. This will be automatically retrieved when you are in production
          amount: {
            value: configuration?.amount?.value,
            currency: configuration?.amount?.currency,
          },
          onError: function (error, dropin) {
            dropin.setStatus('ready');
            console.error('paypal onError', error);
          },
        },
        applepay: {
          // Required configuration for Apple Pay
          amount: {
            value: configuration?.amount?.value,
            currency: configuration?.amount?.currency,
          },
          countryCode: 'DE',
        },
      },
    });
    const dropin = checkout
      .create('dropin', {
        createPaymentLinkRequest: true,
        openFirstPaymentMethod: false,
      })
      .mount(dropinRef.current);
  };

  useEffect(() => {
    const cssHref = `https://checkoutshopper-live.adyen.com/checkoutshopper/sdk/${adyenVersion}/adyen.css`;
    const jsSrc = `https://checkoutshopper-live.adyen.com/checkoutshopper/sdk/${adyenVersion}/adyen.js`;

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = cssHref;
    link.integrity = adyenIntegrity[adyenVersion].css;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);

    const script = document.createElement('script');
    script.src = jsSrc;
    script.integrity = adyenIntegrity[adyenVersion].js;
    script.crossOrigin = 'anonymous';
    script.async = true;
    script.onload = initAdyenCheckout; // Wait until the script is loaded before initiating AdyenCheckout
    document.body.appendChild(script);

    return () => {
      // cleanup after closing the Drop-in
      [...document.getElementsByTagName('link')]
        .find((link) => link.href === cssHref)
        .remove();
      [...document.getElementsByTagName('script')]
        .find((script) => script.src === jsSrc)
        .remove();
    };
  }, []);

  return (
    <>
      <div ref={dropinRef} {...attributes} />
    </>
  );
}
