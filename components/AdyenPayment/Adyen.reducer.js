import * as adyenActionTypes from './Adyen.actionTypes';

export const ADYEN_INITIAL_STATE = {
  loading: true,
  error: '',
  paymentMethodsRes: null,
  paymentRes: null,
  paymentDetailsRes: null,
  paymentDataStoreRes: null,
  orderReferences: [],
  config: {
    locale: process.env.NEXT_PUBLIC_ADYEN_LOCALE,
    environment: process.env.NEXT_PUBLIC_ADYEN_ENV,
    clientKey: process.env.NEXT_PUBLIC_ADYEN_CLIENT_KEY,
    amount: {
      value: 100,
      currency: 'EUR',
    },
  },
};

export default function adyenReducer(state = INITIAL_STATE, action) {
  switch (action.type) {
    case adyenActionTypes.SETUP_PAYMENT_SCREEN:
      return {
        ...state,
        loading: true,
        paymentMethodsRes: null,
        orderReferences: [],
        config: {
          ...state.config,
          amount: {
            ...state.config.amount,
            value: action.payload,
          },
        },
      };
    case adyenActionTypes.SETUP_PAYMENT_SCREEN_SUCCESS:
      return {
        ...state,
        loading: false,
        error: false,
        paymentMethodsRes: action.payload.paymentMethodsRes,
        orderReferences: action.payload.orderReferences,
      };
    case adyenActionTypes.SETUP_PAYMENT_SCREEN_ERROR:
      return {
        ...state,
        loading: false,
        error: action.payload,
      };
    case adyenActionTypes.INITIATE_PAYMENT:
      return {
        ...state,
        loading: action.payload.paymentMethodType === 'paypal' ? false : true,
        paymentRes: null,
      };
    case adyenActionTypes.INITIATE_PAYMENT_SUCCESS:
      return {
        ...state,
        loading: false,
        error: false,
        paymentRes: action.payload,
      };
    case adyenActionTypes.INITIATE_PAYMENT_PENDING:
      return { ...state, loading: false };
    case adyenActionTypes.INITIATE_PAYMENT_ERROR:
      return {
        ...state,
        loading: false,
        error: action.payload,
      };
    default:
      return state;
  }
}
