import * as actionTypes from './Adyen.actionTypes';
import * as service from 'services/adyen.service';

export async function initPaymentDropIn(dispatch, productType, shoppingCart) {
  try {
    const paymentAmount = Number.parseInt(
      (
        Number.parseFloat(
          shoppingCart?.reduce(
            (acc, product) =>
              acc + (Number.parseFloat(product.initialAmount) || 0),
            0
          ) || 0
        ) * 100
      ).toFixed(),
      10
    );

    dispatch({
      type: actionTypes.SETUP_PAYMENT_SCREEN,
      payload: paymentAmount,
    });

    const paymentMethodsRes = await service.getPaymentMethods();
    const checkoutRes = await service.checkout(productType, shoppingCart);

    dispatch({
      type: actionTypes.SETUP_PAYMENT_SCREEN_SUCCESS,
      payload: {
        paymentMethodsRes: paymentMethodsRes.data,
        orderReferences: checkoutRes.data.orders.map(
          (order) => order.orderReference
        ),
      },
    });
  } catch (error) {
    dispatch({
      type: actionTypes.SETUP_PAYMENT_SCREEN_ERROR,
      payload: error,
    });
  }
}

export async function initiatePayment(dispatch, payload, dropIn) {
  try {
    dispatch({
      type: actionTypes.INITIATE_PAYMENT,
      payload: { paymentMethodType: payload.paymentMethod?.type },
    });

    const response = await service.initiatePayment({
      ...payload,
    });

    if (response?.data?.action) {
      dispatch({
        type: actionTypes.INITIATE_PAYMENT_PENDING,
      });
      dropIn.handleAction(response.data.action);
    } else {
      dispatch({
        type: actionTypes.INITIATE_PAYMENT_SUCCESS,
        payload: response.data,
      });
    }
  } catch (error) {
    dispatch({ type: actionTypes.INITIATE_PAYMENT_ERROR, payload: error });
  }
}

export async function setPaymentError(dispatch, error) {
  dispatch({ type: actionTypes.INITIATE_PAYMENT_ERROR, payload: error });
}

export async function onAdditionalDetails(dispatch, payload, dropIn) {
  try {
    const response = await service.onAdditionalDetails(payload);

    dispatch({
      type: actionTypes.INITIATE_PAYMENT_SUCCESS,
      payload: response.data,
    });
  } catch (error) {
    dispatch({ type: actionTypes.INITIATE_PAYMENT_ERROR, payload: error });
  }
}
