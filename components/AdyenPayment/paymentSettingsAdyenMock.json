{"clientKey": "test_C3WBVQ4M4BGOPMKS2HOMZRLQLYN343U3", "configuration": {"avsEnabled": false, "hiddenShopperFields": [], "requireNameOnCard": false, "showBillingAddressForm": true, "showDeliveryAddressForm": true, "showPersonalDetailsForm": true}, "environment": "test", "features": {"enablePartialPayments": false}, "merchant": {}, "originKey": "pub.v2.8016109720012405.aHR0cHM6Ly90ZXN0LmFkeWVuLmxpbms.fKx_n5iicrrCToIYyp6v8nK7xgovKeUqKO-7_Jj95t4", "paymentLink": {"amount": {"currency": "EUR", "value": 1000}, "countryCode": "NL", "reference": "adyenTestPayPal", "reusable": false, "status": "active"}, "paymentMethodsResponse": {"paymentMethods": [{"brands": ["mc", "visa", "amex", "maestro"], "details": [{"key": "number", "type": "text"}, {"key": "expiry<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "expiryYear", "type": "text"}, {"key": "cvc", "type": "text"}, {"key": "<PERSON><PERSON><PERSON>", "optional": true, "type": "text"}], "name": "Credit Card", "type": "scheme"}, {"configuration": {"merchantId": "9Z6NDAH43S9UY", "intent": "capture"}, "name": "PayPal", "type": "paypal"}, {"details": [{"key": "sepa.ownerName", "type": "text"}, {"key": "sepa.ibanNumber", "type": "text"}], "name": "SEPA Direct Debit", "type": "sepadirectdebit"}, {"configuration": {"merchantId": "000000000200183", "merchantName": "Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "details": [{"key": "applepay.token", "type": "applePayToken"}], "name": "Apple Pay", "type": "applepay"}]}}