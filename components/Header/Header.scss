@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
.Header {
  background-color: #81e9f0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 15;
  &__utilities {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  &__content {
    // display: flex;
    margin: 0 auto;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 435px 1fr;
    @include media-breakpoint-down(md) {
      grid-template-columns: auto 1fr auto;
      padding: 0 10px;
    }
  }
}

@include media-breakpoint-down(md) {
  .Header {
    height: 85px;
    z-index: 999;

    .logos-container .main-logo {
      max-width: 140px;
      max-height: 88px;
    }

    .btn.btn-dark {
      padding: 5px 2px;
      font-size: 12px;
    }

    & + main {
      margin-top: 90px;
    }
  }
}

@include media-breakpoint-down(xs) {
  .Header .logos-container.has-collaboration-partner-logo .main-logo {
    max-width: 100px;
    max-height: 73px;
  }
}

@media (max-width: 575.98px) {
  .padding-side-25 {
    padding-left: 10px;
    padding-right: 10px;
  }
}
