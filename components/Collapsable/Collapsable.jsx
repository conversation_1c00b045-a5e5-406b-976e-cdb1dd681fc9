import { BiPlus, BiMinus } from 'react-icons/bi/index';
import { Collapse } from 'reactstrap';
import styles from './Collapsable.module.scss';

const Collapsable = ({ title, children, isOpen, toggle }) => {
  return (
    <div className={styles.faqCard}>
      <h2 className={styles.faqCardTitle} onClick={toggle}>
        {title}
        <span className={styles.faqCardToggler}>
          {isOpen ? <BiMinus /> : <BiPlus />}
        </span>
      </h2>

      <Collapse isOpen={isOpen}>
        <p>{children.content}</p>
        <ul>
          {children.subitems?.map((item, index) => {
            return (
              <li key={index}>
                <b>{item.title}</b> {item.content}
              </li>
            );
          })}
        </ul>
      </Collapse>
    </div>
  );
};

export default Collapsable;