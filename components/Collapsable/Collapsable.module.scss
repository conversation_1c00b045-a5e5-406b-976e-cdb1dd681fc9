.faqCard {
  display: block;
  border-top: 1px solid #000000;
  &:first-of-type {
    border-top: 0;
  }
}

.faqCardTitle {
  font: normal normal 900 20px/22px Montserrat;
  cursor: pointer;
  margin-bottom: 0px;
  padding: 20px 0;
  display: flex;
  align-items: center;
}

.faqCardToggler {
  float: right;
  font-size: 28px;
  margin-right: 0;
  margin-left: auto;
}

.faqCard p {
  padding: 10px 0;
}

.faqCard ul {
  list-style-type: none;
}

.faqCard ul li {
  margin-bottom: 7px;
}

.faqCard ul li:before {
  content: '\2014';
  position: absolute;
  margin-left: -30px;
}