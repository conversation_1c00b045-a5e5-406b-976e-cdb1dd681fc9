@import '../../styles/sass/variables';

.SelectorCarousel {
  $root: &;

  position: relative;
  display: flex;
  flex-direction: row;
  width: 100%;
  overflow: hidden;

  &__container {
    flex: 1;
    display: flex;
    flex-direction: row;
    transform: translateX(0px);
    transition: transform 0.2s ease-out;
  }

  &--dragging {
    #{ $root }__container {
      transition: none;
      cursor: grabbing;

      #{ $root }__option {
        pointer-events: none;
      }
    }
  }

  &__option {
    padding: 6px 12px;
    margin-right: 6px;
    font-size: 14px;
    font-weight: 600;
    color: black;
    border: 2px solid transparent;
    transition: border-color ease-out 0.2s;
    text-decoration: none;
    white-space: nowrap;
    border-radius: 50px;

    @media screen and (max-width: $handheld-breakpoint) {
      padding: 3px 6px;
      font-size: 11px;
      margin-right: 3px;
    }

    &:active,
    &:focus {
      color: black;
    }

    &:hover {
      border-color: black;
      color: black;
      text-decoration: none;
    }

    &--selected {
      background: black;
      border-color: black;
      color: white;

      &:hover,
      &:active,
      &:focus {
        color: white;
      }
    }
  }

  &__skip-button {
    $size: 32px;
    $inner-padding: 12px;

    display: flex;
    align-items: center;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    flex-shrink: 0;
    width: $size;
    height: 100%;
    padding: 0;
    border: 0;
    top: 0;
    background: #F8F8F8;
    transition: opacity 0.3s ease-out;

    @media screen and (max-width: $handheld-breakpoint) {
      display: none;
    }

    &--back {
      left: 0;
      padding-right: $inner-padding;
    }

    &--forward {
      right: 0;
      padding-left: $inner-padding;
    }

    &--active {
      opacity: 1;
      pointer-events: all;
    }

    &:active,
    &:focus {
      border: 0;
      outline: 0;
    }

    img {
      width: $size;
      height: $size;
      pointer-events: none; // to make it easier to catch events on <button /> directly
    }
  }
}
