import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useOnWindowResize from './useOnWindowResize';

const POINTER_DOWN_EVENTS = ['mousedown', 'touchstart'];
const POINTER_MOVE_EVENTS = ['mousemove', 'touchmove'];
const POINTER_UP_EVENTS = ['mouseup', 'touchend', 'touchcancel'];
const DRAG_THRESHOLD_PX = 4;

const getEventScreenX = (event) => {
  if (event.touches) {
    if (!event.touches.length) return null;
    return event.touches[0].screenX;
  }

  return event.screenX;
};

const useSelectorCarousel = (selectedItem) => {
  const rootRef = useRef();
  const slideContainerRef = useRef();
  const skipButtonBackRef = useRef();
  const skipButtonForwardRef = useRef();
  const [rootWidth, setRootWidth] = useState(0);
  const [slideContainerWidth, setSlideContainerWidth] = useState(0);
  const [skipButtonWidth, setSkipButtonWidth] = useState(0);
  const [posX, setPosX] = useState(0);
  const [sliderPosXOnDown, setSliderPosXOnDown] = useState(null);
  const [downPosX, setDownPosX] = useState(null);
  const [hasMoved, setHasMoved] = useState(false);

  // Calculate minSliderContainerPosX before it's used in useEffect dependencies
  const minSliderContainerPosX = useMemo(
    () => -slideContainerWidth + rootWidth,
    [rootWidth, slideContainerWidth]
  );

  const onWindowResize = useCallback(() => {
    if (
      !rootRef.current ||
      !slideContainerRef.current ||
      !skipButtonBackRef.current
    )
      return;
    const rootWidth = rootRef.current.getBoundingClientRect().width;
    const slideContainerWidth =
      slideContainerRef.current.getBoundingClientRect().width;
    const skipButtonWidth =
      skipButtonBackRef.current.getBoundingClientRect().width;

    setRootWidth(rootWidth);
    setSlideContainerWidth(slideContainerWidth);
    setSkipButtonWidth(skipButtonWidth);
  }, [
    rootRef,
    slideContainerRef,
    skipButtonBackRef,
    setRootWidth,
    setSlideContainerWidth,
    setSkipButtonWidth,
  ]);
  useOnWindowResize(onWindowResize);

  useEffect(() => {
    if (!selectedItem || !rootRef.current || !slideContainerRef.current) return;
    const rootBoundingRect = rootRef.current.getBoundingClientRect();
    const rootPosX = rootBoundingRect.left;
    const slideContainerPosX =
      slideContainerRef.current.getBoundingClientRect().left;
    const selectedItemBoundingRect = selectedItem.getBoundingClientRect();
    const selectedItemPosX = selectedItemBoundingRect.left;
    const selectedItemWidth = selectedItemBoundingRect.width;
    const isSelectedItemOffScreenToTheLeft =
      selectedItemPosX < rootPosX + skipButtonWidth;
    const isSelectedItemOffScreenToTheRight =
      selectedItemPosX + selectedItemWidth >
      rootPosX + rootWidth - skipButtonWidth;

    if (isSelectedItemOffScreenToTheLeft || isSelectedItemOffScreenToTheRight) {
      let newPosX = slideContainerPosX - selectedItemPosX + skipButtonWidth;
      newPosX = Math.min(newPosX, 0);
      newPosX = Math.max(newPosX, minSliderContainerPosX);
      setPosX(newPosX);
    }
  }, [
    selectedItem,
    rootRef,
    rootWidth,
    slideContainerRef,
    skipButtonWidth,
    setPosX,
    minSliderContainerPosX,
  ]);

  const isEventTargetSkipButton = useCallback(
    ({ target }) =>
      target === skipButtonBackRef.current ||
      target === skipButtonForwardRef.current,
    [skipButtonBackRef, skipButtonForwardRef]
  );

  const isBackButtonActive = useMemo(
    () => posX < -skipButtonWidth,
    [posX, skipButtonWidth]
  );

  const isForwardButtonActive = useMemo(
    () => posX > minSliderContainerPosX + skipButtonWidth,
    [posX, minSliderContainerPosX, skipButtonWidth]
  );

  const skipStepX = useMemo(() => rootWidth / 2, [rootWidth]);

  const onBackClick = useCallback(() => {
    let newPosX = Math.min(posX + skipStepX, 0);
    if (newPosX > -skipButtonWidth) newPosX = 0; // snap to 0
    setPosX(newPosX);
  }, [setPosX, posX, skipStepX, skipButtonWidth]);

  const onForwardClick = useCallback(() => {
    const newPosX = Math.max(posX - skipStepX, minSliderContainerPosX);
    setPosX(newPosX);
  }, [setPosX, posX, minSliderContainerPosX, skipStepX]);

  const onPointerDown = useCallback(
    (evt) => {
      if (isEventTargetSkipButton(evt)) return;
      setDownPosX(getEventScreenX(evt));
      setSliderPosXOnDown(posX);
      setHasMoved(false);
    },
    [setDownPosX, setSliderPosXOnDown, setHasMoved, posX, isEventTargetSkipButton]
  );

  const onPointerMove = useCallback(
    (evt) => {
      if (isEventTargetSkipButton(evt)) return;
      if (downPosX === null || sliderPosXOnDown === null) return;
      const deltaX = getEventScreenX(evt) - downPosX;
      if (Math.abs(deltaX) <= DRAG_THRESHOLD_PX) {
        evt.preventDefault();
        evt.stopPropagation();
        return;
      }
      let newPosX = sliderPosXOnDown + deltaX;
      if (newPosX > 0) newPosX = 0;
      if (newPosX < minSliderContainerPosX) newPosX = minSliderContainerPosX;
      setPosX(newPosX);
      setHasMoved(true);
    },
    [
      setPosX, 
      setHasMoved, 
      downPosX, 
      sliderPosXOnDown, 
      minSliderContainerPosX, 
      isEventTargetSkipButton
    ]
  );

  const onPointerUp = useCallback(() => {
    setDownPosX(null);
    setHasMoved(false);
  }, [setDownPosX, setHasMoved]);

  useEffect(() => {
    const rootElement = rootRef.current;
    if (!rootElement) return;
    POINTER_DOWN_EVENTS.forEach((type) =>
      rootElement.addEventListener(type, onPointerDown)
    );
    POINTER_MOVE_EVENTS.forEach((type) =>
      rootElement.addEventListener(type, onPointerMove, { passive: false })
    );
    POINTER_UP_EVENTS.forEach((type) =>
      window.document.addEventListener(type, onPointerUp, true)
    );

    return () => {
      POINTER_DOWN_EVENTS.forEach((type) =>
        rootElement.removeEventListener(type, onPointerDown)
      );
      POINTER_MOVE_EVENTS.forEach((type) =>
        rootElement.removeEventListener(type, onPointerMove, {
          passive: false,
        })
      );
      POINTER_UP_EVENTS.forEach((type) =>
        window.document.removeEventListener(type, onPointerUp, true)
      );
    };
  }, [rootRef, onPointerDown, onPointerMove, onPointerUp]);

  return {
    slideContainerPosX: posX,
    isDragged: hasMoved,
    rootRef,
    slideContainerRef,
    skipButtonBackRef,
    skipButtonForwardRef,
    onBackClick,
    onForwardClick,
    isBackButtonActive,
    isForwardButtonActive,
  };
};

export default useSelectorCarousel;