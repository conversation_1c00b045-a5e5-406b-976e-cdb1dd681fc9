import { useMemo, useRef } from 'react';
import styles from './SelectorCarousel.module.scss';
import useSelectorCarousel from './useSelectorCarousel';

const SelectorCarousel = ({
  className = '',
  options,
  selectedId,
  onSelect = undefined,
  buttonBackground = undefined,
}) => {
  const selectedItemRef = useRef();

  const {
    slideContainerPosX,
    rootRef,
    slideContainerRef,
    skipButtonBackRef,
    skipButtonForwardRef,
    isBackButtonActive,
    isForwardButtonActive,
    isDragged,
    onBackClick,
    onForwardClick,
  } = useSelectorCarousel(selectedItemRef.current);

  const buttonBackgroundStyle = useMemo(
    () => (buttonBackground ? { background: buttonBackground } : undefined),
    [buttonBackground]
  );

  return (
    <div
      ref={rootRef}
      className={`${styles.selectorCarousel} ${
        isDragged ? styles.selectorCarouselDragging : ''
      } ${className}`}
    >
      <div
        ref={slideContainerRef}
        className={styles.selectorCarousel__container}
        style={{ transform: `translateX(${slideContainerPosX}px)` }}
      >
        {options.map((option) => {
          const isSelected = option.id == selectedId;
          return (
            <a
              key={option.id}
              ref={isSelected ? selectedItemRef : undefined}
              className={`${styles.selectorCarousel__option} ${
                isSelected ? styles.selectorCarousel__optionSelected : ''
              }`}
              href={option.href}
              onClick={(evt) => {
                if (onSelect) {
                  evt.preventDefault();
                  onSelect(option);
                }
              }}
            >
              {option.label}
            </a>
          );
        })}
      </div>
      <button
        ref={skipButtonBackRef}
        className={`${styles.selectorCarousel__skipButton} ${styles.selectorCarousel__skipButtonBack} ${
          isBackButtonActive ? styles.selectorCarousel__skipButtonActive : ''
        }`}
        style={buttonBackgroundStyle}
        onClick={onBackClick}
      >
        <img src="/icons/arrow-left.svg" alt="Ein schwarzer Pfeil nach links" />
      </button>
      <button
        ref={skipButtonForwardRef}
        className={`${styles.selectorCarousel__skipButton} ${styles.selectorCarousel__skipButtonForward} ${
          isForwardButtonActive ? styles.selectorCarousel__skipButtonActive : ''
        }`}
        style={buttonBackgroundStyle}
        onClick={onForwardClick}
      >
        <img
          src="/icons/arrow-right.svg"
          alt="Ein schwarzer Pfeil nach rechts"
        />
      </button>
    </div>
  );
};

export default SelectorCarousel;