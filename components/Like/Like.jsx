import styles from './Like.module.scss';

import { setFavouriteStatus } from 'services/coupons.service';
import { isAuth } from 'services/auth.service';

import OutlineHeartIcon from '../CustomIcons/OutlineHeartIcon';
import FillHeartIcon from '../CustomIcons/FillHeartIcon';
import { useMemo } from 'react';

const handleLikeClick = (id, fave, onSuccess, onError) => {
  setFavouriteStatus(id, fave, onSuccess, onError);
};

const LikeIcon = ({ className = '', isLiked }) => {
  if (isLiked) {
    return <FillHeartIcon width="36px" height="36px" className={className} />;
  } else {
    return (
      <OutlineHeartIcon width="36px" height="36px" className={className} />
    );
  }
};

const Like = ({
  className = '',
  isLiked = false,
  id,
  handleClick = handleLikeClick,
  onSuccess,
  onError,
}) => {
  const isTouchDevice = useMemo(
    () =>
      'ontouchstart' in window ||
      navigator.maxTouchPoints > 0 ||
      navigator.msMaxTouchPoints > 0,
    []
  );

  const icon = <LikeIcon isLiked={isLiked} />;
  const hoverIcon = <LikeIcon isLiked={true} className={styles.showOnHover} />;
  return isAuth() ? (
    <div
      className={`${styles.like} ${className} ${isTouchDevice ? '' : styles.hoverEnabled} ${
        isLiked ? styles.liked : ''
      }`}
      onClick={() => handleClick(id, !isLiked, onSuccess, onError)}
    >
      {icon}
      {hoverIcon}
    </div>
  ) : null;
};

export default Like;