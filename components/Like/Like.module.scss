.like {
  @keyframes rocket {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.5);
    }
    100% {
      transform: scale(1);
    }
  }

  width: 36px;
  height: 36px;
  border-radius: 100%;
  background-color: #fff;
  font-size: 22px;
  cursor: pointer;
  z-index: 1;

  &.liked {
    animation: rocket ease-in-out 0.3s;
  }
  .showOnHover {
    display: none;
    transition: transform ease-out 0.08s;
  }

  &.hoverEnabled:hover {
    svg {
      display: none;
    }
    .showOnHover {
      display: inline-block;
      transform: scale(1.2);
    }
  }
}