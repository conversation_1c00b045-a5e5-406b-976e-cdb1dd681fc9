#! /bin/bash
echo """
======================================================================
 Installing docker compose
======================================================================
"""
# https://docs.docker.com/engine/install/ubuntu/
sudo apt-get remove docker docker-engine docker.io containerd runc
sudo apt-get install apt-transport-https ca-certificates curl gnupg lsb-release
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo \
  "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io 

# https://docs.docker.com/compose/install/
# sudo curl -L "https://github.com/docker/compose/releases/download/1.29.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
# sudo chmod +x /usr/local/bin/docker-compose
sudo apt-get install docker-compose

echo """
======================================================================
  Installing mkcert
======================================================================
"""
sudo apt install libnss3-tools
wget https://github.com/FiloSottile/mkcert/releases/download/v1.1.2/mkcert-v1.1.2-linux-amd64
mv mkcert-v1.1.2-linux-amd64 mkcert
chmod +x mkcert
sudo mv mkcert /usr/local/bin/
mkcert -install

if [ "$1" != '' ]
then
  domain=$1
else
  domain="stg.captaincoupon.de"
fi
echo """
======================================================================
  Creating certificate for:
  $domain
======================================================================
"""
mkcert $domain

echo """
======================================================================
  Moving certificates to ./nginx/certs
======================================================================
"""

if [ ! -d "./certs" ]
then
  mkdir certs
fi

mv "./$domain.pem" "./certs/$domain.crt"
mv "./$domain-key.pem" "./certs/$domain.key"



if grep -q "127.0.0.1[[:blank:]]\+$domain" "/etc/hosts" ; then
  echo """
======================================================================
  /etc/hosts file allready contains this line:
  127.0.0.1 $domain
======================================================================
"""
else
  echo """
======================================================================
  Adding the following to /etc/hosts file:
  127.0.0.1 $domain
======================================================================
"""
  sudo echo """
# dev domain for captain coupon web
127.0.0.1       $domain" | sudo tee -a /etc/hosts
fi
