@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');
// Override default variables before the import
$body-bg: #F8F8F8;
$body-color: #000;
$enable-rounded: false;
$theme-colors: (
  'primary': #007bff,
  'danger': #ff4136,
  'custom-color': #900,
  'dark': #000,
  'gray': #000,
  'red': #ff0000,
);
$font-family-sans-serif: 'Montserrat', sans-serif;
html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

$container-max-widths: (
  sm: 600px,   // default: 540px
  md: 780px,   // default: 720px
  lg: 1020px,  // default: 960px
  xl: 1200px   // default: 1140px
);

@import '~bootstrap/scss/bootstrap';
// Import Bootstrap and its default variables

@import 'sass/headings';
@import 'sass/common';
@import 'sass/typography';
@import 'sass/layout';
@import 'sass/mixins';
@import 'sass/forms';
@import 'sass/tablet';
@import 'sass/calendar';
