@mixin centerImage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  min-width: 100%;
  min-height: 100%;
  object-fit: cover;
}

@mixin mediaMobile {
  @media screen and (max-width: 480px) {
    @content;
  }
}
@mixin mediaTablet {
  @media screen and (min-width: 481px) and (max-width: 768px) {
    @content;
  }
}
@mixin mediaLaptop {
  @media screen and (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}
@mixin mediaDesktop {
  @media screen and (min-width: 1025px) and (max-width: 1360px) {
    @content;
  }
}

@mixin mediaLargeDesktop {
  @media screen and (min-width: 1361px) {
    @content;
  }
}
