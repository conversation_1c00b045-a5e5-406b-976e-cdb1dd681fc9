@include media-breakpoint-down(md) {
  .hamburgerIcon {
    font-size: 64px;
    margin-left: -16px;
  }
  .search-block {
    align-items: center;
    display: flex;
    justify-content: flex-end;
  }
  #coupons {
    order: 1;
  }
  #link {
    order: 2;
  }
  #steps {
    order: 3;
  }
  .headline-link {
    font-size: 30px;
  }
}
@media (max-width: 575.98px) {
  .brands-overview {
    padding-bottom: 40px;
  }

  .headline-section {
    font-size: 25px;
    line-height: 30px;
    .headline-link {
      font-size: 25px;
      line-height: 30px;
    }
    &:first-line {
      font-size: 25px;
      line-height: 30px;
    }
  }

  .alle-marken-btn-landing-page {
    padding-top: 0px;
    margin-top: -10px;
  }
  .padding-side-25 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .hamburgerIcon {
    padding-left: 0px;
  }
  .search-block:not(.compact) {
    justify-content: normal;
  }

  .pad-lr-10 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .Header .btn.btn-dark {
    margin-right: 0px !important;
  }

  .settings-image {
    margin-top: 25px;
  }

  .settings-description {
    padding-bottom: 5px;
  }

  .settings-back-btn {
    padding-bottom: 30px;
  }

  .coupon-card-grid {
    margin-bottom: 0px !important;
  }

  .coupon-card-landing-page {
    margin-bottom: 0px !important;
  }

  .container-lg.video-section-landing .row {
    margin-left: -25px;
    margin-right: -25px;
  }
}

@media (max-width: 767px) {
  .custom-header {
    margin-top: 25px;
  }
  .headline-link {
    font-size: 25px;
  }
  .layout-custom-header {
    margin-top: 25px;
  }
}

@media (min-width: 998px) {
  .custom-header {
    margin-top: 50px;
  }
  .headline-link {
    font-size: 40px;
  }
  .layout-custom-header {
    margin-top: 50px;
  }
}

@media (max-width: 768px) {
  .filter-and-sort-section {
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .search-block {
    padding-right: 0px;
  }
  .custom-modal-padding {
    padding-left: 25px;
    padding-right: 25px;
  }
  .container-lg.video-section-landing .row {
    .video-section {
      padding-left: 25px;
      padding-right: 25px;
      @media (max-width: 382.98px) {
        margin-top: 40px;
        margin-bottom: 40px;
      }
    }
  }
}

@media (min-width: 768px) and (max-width: 997.98px) {
  .custom-header {
    margin-top: 40px;
    img {
      height: 340px;
      object-fit: cover;
    }

    .section1-about {
      padding-bottom: 25px;
    }
  }

  .section1-about-content {
    padding-top: 25px;
    padding-bottom: 25px;
  }

  .section1-howdoesitwork {
    padding-top: 15px;
    margin-bottom: 50px;
  }

  .search-block {
    padding-right: 0px;
  }
  .layout-custom-header {
    margin-top: 40px;
    .section1-about {
      padding-bottom: 25px;
    }
  }
}

// @media (max-width: 998px) {
//   .video-placeholder-container {
//     position: relative;
//     width: 100%;
//     left: 0;
//     top: 0;
//     height: 0;
//     /* max-height: 400px; */
//     padding-bottom: 53.25%;
//     /* object-fit: scale-down; */
//   }
//   .video-placeholder-container iframe {
//     position: absolute;
//     top: 0;
//     left: 0;
//     height: 100%;
//     width: 100%;
//     z-index: 1;
//   }
// }

//edno nad iframe

/*position: relative;
width: 100vw;
height: 0;
padding-bottom: 53%;
*/

// iframe
/*position: absolute;
top: 0px;
left: 0px;
height: 100%;
width: 100%;
z-index: 1;
pointer-events: none;
*/
