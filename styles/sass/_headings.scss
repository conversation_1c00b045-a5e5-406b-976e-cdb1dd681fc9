@import '../../styles/sass/variables';

.text-content {
  margin-bottom: 320px;

  @media (min-width: 1200px) {
    /* Add your logic here for desktop large devices */
    margin-bottom: 0px;
    height: 542px;
    display: flex;
  }

  @media (min-width: 992px) and (max-width: 1199.98px) {
    margin-bottom: 0px;
    height: 400px;
    display: flex;
  }

  @media (max-width: 991px) {
    margin-bottom: 0px;
    height: 400px;
    display: flex;
  }

  @include media-breakpoint-down(sm) {
    margin-bottom: 50px;
    height: auto;
  }

  @media (max-width: 488px) {
    margin-bottom: 85px;
  }

  @media (max-width: 400px) {
    margin-bottom: 30px;
  }
}

.main-header {
  width: 100%;
  text-align: left;
  font: 44px/50px Montserrat;
  letter-spacing: 0px;
  color: #000000;
  font-weight: 900;
  word-wrap: break-word;

  &.landing-page-header {
    @include media-breakpoint-down(md) {
      z-index: 1;
      text-align: left;
    }

    @media screen and (max-width: 1199.98px) {
      font: 33px/41px Montserrat;
      font-weight: 900;
    }

    @media screen and (max-width: 767px) {
      width: 120%;
    }

    @media screen and (max-width: 575.98px) {
      font: 28px/31px Montserrat;
      font-weight: 900;
    }
  }

  @include media-breakpoint-down(xs) {
    font: 30px/35px Montserrat;
    font-weight: 900;
  }
}

.video-section-h1 {
  text-align: left;
  font: 45px Montserrat;
  letter-spacing: 0px;
  color: #000000;
  font-weight: 900;

  @media (max-width: 575.98px) {
    font: 30px/35px Montserrat;
    font-weight: 900;
    text-align: center;
  }

  @media (min-width: 992px) and (max-width: 1199.98px) {
    font-size: 42px;
    font-weight: 900;
  }
}

.subtitle-landing-page {
  font-family: Montserrat;
  font-weight: 300;
  line-height: 1.4;
  font-size: 20px;

  @include media-breakpoint-down(xs) {
    & {
      font-size: 14px;
      line-height: 20px;
      width: 70%;
    }
  }

  @media(max-width: 575.98px) {
    // width: 90%;
  }

  @media screen and (max-width: 767px) {
    // width: 150%;
  }

  @media (min-width: 768px) and (max-width: 991px) {
    padding-right: 36px;
  }
}

.secondary-header {
  font-size: 25px;
  font-family: Montserrat;
  font-weight: 300;
  @include media-breakpoint-down(xs) {
    & {
      font-size: 14px;
      line-height: 20px;
    }
  }
}
.secondary-header--bold {
  @extend .secondary-header;
  font-weight: 900;
}
.section-header {
  font-size: 45px;
  line-height: 1.2;
  font-weight: 900;
  @include media-breakpoint-down(xs) {
    & {
      font-size: 30px;
    }
  }

  &__with-icon {
    justify-content: space-between;
    display: flex;
    align-items: center;

    .section-header-companion-icon {
      @media screen and (max-width: 359px) {
        display: none;
      }
    }
  }
}

.jumbo-header {
  font-size: 62px;
  font-family: Montserrat;
  font-weight: 900;
  @include media-breakpoint-down(lg) {
    & {
      font-size: 45px;
    }
  }

  @include media-breakpoint-down(xs) {
    & {
      font-size: 30px;
      text-align: center;
    }
  }
}

.login-header {
  font-size: 30px;
  padding-left: 0px;
  margin-bottom: 33px;
  line-height: 35px;
  font-family: Montserrat;
  b {
    font-weight: 900;
  }
}

.sidebar-header {
  font-size: 30px;
  line-height: 30px;
  font-family: Montserrat;
  font-weight: 900;
}

.headline-section {
  font-size: 50px;
  text-align: center;
  font-weight: 900;
  color: #000;
  line-height: 50px;
  margin: 40px auto 60px;

  @media screen and (max-width: $handheld-breakpoint) {
    margin: 12px auto 36px auto;
  }

  a {
    display: inline-block;
    color: #000;
    text-decoration: none;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    &:hover {
      text-decoration: underline;
    }
  }
  &:first-line {
    font-size: 30px;
    font-weight: normal;
  }

  @include media-breakpoint-down(lg) {
    & {
      font-size: 50px;
    }
  }
}

.brand-page-logo {
  max-height: 250px;
  max-width: 80%;
  margin: 0 auto;
  display: block;
}

@include media-breakpoint-down(md) {
  .brand-logo-wrapper .Centered__children {
    height: 150px;
    justify-content: center;
    flex-direction: column;
    display: flex;
    .brand-page-logo {
      max-height: 150px;
      height: auto;
    }
  }
}

.home-header-image {
  position: absolute;
  bottom: -250px;
  max-width: 144%;
  margin-left: -218px;

  @include media-breakpoint-down(lg) {
    margin-left: -100px;
    max-width: 160%;
  }

  @media (min-width: 992px) and (max-width: 1199.98px) {
    max-width: 124%;
    margin-left: -132px;
    bottom: -175px;
  }

  @media (max-width: 991px) {
    max-width: 144%;
    margin-left: -132px;
    bottom: -155px;
  }

  @media (max-width: 767px) {
    max-width: 144%;
    margin-left: -106px;
    bottom: -80px;
  }

  @media (max-width: 576px) {
    max-width: 124%;
    margin-left: -106px;
    bottom: -100px;
  }

  @media (max-width: 488px) {
    max-width: 144%;
    margin-left: -106px;
    bottom: -55px; // 80px
  }
  @media (max-width: 400px) {
    max-width: 120%;
    margin-left: -46px;
    bottom: -50px;
  }
  @media (max-width: 375px) {
    max-width: 130%;
    margin-left: -66px;
    bottom: -60px;
  }
}
