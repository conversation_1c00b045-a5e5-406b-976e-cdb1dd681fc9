@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');

a {
  color: #000;
  text-decoration: underline;
}
.link-dark {
  color: #000;
  align-self: center;
  font-size: 14px;
  &:hover {
    color: #000;
  }
}
img {
  max-width: 100%;
}
.noBg {
  background: transparent;
}

.gray-box {
  background-color: #f7f7f7;
  padding: 30px;
  position: relative;
  height: 100%;
  margin-top: 35px;

  .settings-btn {
    position: absolute;
    bottom: 30px;
    height: 50px;
    max-width: fit-content;
    font-weight: 700;
    line-height: 14px;
  }
  .full-height-container {
    height: 100%;
  }
  .buttons-wrapper {
    justify-content: space-between;
  }
  .group-button {
    height: 50px;
    max-width: fit-content;
    min-width: 177px;
    font-weight: 700;
    line-height: 14px;
  }

  .email-wrapper {
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-top: 10px;
    margin-bottom: 10px;
    p {
      font-size: 14px;
      word-break: break-all;
    }

    @media (max-width: 455px) {
      p {
        font-size: 13px;
      }
      small {
        font-size: 12px;
      }
    }

    @media (min-width: 769px) and (max-width: 991px) {
      p {
        font-size: 13px;
      }
      small {
        font-size: 13px;
      }
    }
  }

  @media (max-width: 991px) {
    .edit-email-buttons-wraper button {
      min-width: auto;
    }
  }

  .edit-email-btn-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    @media (max-width: 343px) {
      padding: 0;
    }
    @media (min-width: 343.98px) and (max-width: 1199px) {
      padding: 0 15px 0 0;
    }

    .make-primary-email-btn {
      padding: 0 0 0 0;
      margin-right: 13px;
      margin-bottom: 10px;
      &:focus {
        box-shadow: none;
      }
      img {
        height: 19px;
        max-width: none;
        &:hover {
          content: url('/icons/circle-check-hover.svg');
        }
      }
    }

    .primary-email-check {
      padding: 0 0 0 0;
      margin-bottom: 10px;
      &:focus {
        box-shadow: none;
      }
      img {
        height: 19px;
        max-width: none;
      }
    }

    .delete-email-btn {
      padding: 0;
      margin-bottom: 10px;
      &:focus {
        box-shadow: none;
      }
      img {
        max-width: none;
        height: 15px;
      }
    }
  }
  .group-btn-wrap-left {
    text-align: left !important;
  }
  &.second-row {
    min-height: 220px;
    &.white-box-override {
      background-color: white;
    }
  }
  .left-button {
    left: 30px;
  }
  .right-button {
    right: 30px;
  }
  &.first-row:not(.edit-user-form) {
    height: auto;
  }
  &.payment-method-card .info-card b {
    padding-bottom: 0px;
  }
  @include media-breakpoint-down(md) {
    &.edit-user-form {
      height: fit-content;
    }
    &.first-row {
      height: fit-content;
    }
    &.first-row:not(.edit-user-form) {
      min-height: 420px;
    }
  }
  .user-infobox-edit-btn-wrapper {
    position: absolute;
    bottom: 30px;
    right: 20px;
    @include media-breakpoint-down(sm) {
      position: relative;
      bottom: 0;
      right: 0;
    }
  }

  @include media-breakpoint-down(sm) {
    &.first-row:not(.edit-user-form) {
      min-height: 406px;
    }
    .group-btn-wrap-left {
      width: fit-content;
    }
    .group-btn-wrap-left {
      text-align: right !important;
    }
  }

  .large-btn {
    min-width: 240px;
    max-width: fit-content;
  }
}

.subs-icon {
  height: 70px;
}
.no-subs-icon {
  height: 110px;
  margin-bottom: 30px;
}

.gray-box {
  .no-subscription-note {
    margin-bottom: 10px;
  }

  .additional-info-footnote {
    margin-top: 10px;
    font-size: 10px;
    line-height: 14px;
  }
}

.payment-img {
  height: 83px;
  width: auto;
  object-fit: scale-down;
  object-position: 0;
}
.payment-card {
  font-size: 1rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.neutral-box {
  position: relative;
  height: 207px;
  background-color: #fff;
  &.lower-box {
    height: 140px;
  }
}
.delete-account-btn {
  position: absolute;
  right: 30px;
  height: 50px;
  min-width: 177px;
  font-weight: 700;
  line-height: 14px;
}

.pl-20 {
  padding-left: 20px;
}

.font-16 {
  font-size: 16px;
}

.subscription-title {
  margin-top: 18px;
  font-size: 14px;
}

.info-card,
.edit-block-header {
  padding-bottom: 22px;
}
.gray-box {
  b {
    padding-bottom: 0px;
    display: block;
    font-size: 14px;
    line-height: 24px;

    &.box-title {
      font-size: 20px;
      margin-bottom: 10px;
    }
  }
  p {
    line-height: 24px;
    font-size: 14px;
    margin-bottom: 0px;
  }
}
@include media-breakpoint-up(sm) {
  .gray-box {
    p {
      line-height: 24px;
      font-size: 14px;
      margin-bottom: 0px;
    }
  }
}
.edit-block-header {
  padding-bottom: 10px;
}
.couponLabel {
  border: 1px solid #000;
  font-size: 20px;
  font-weight: bold;
  padding: 10px 15px;
  margin: 20px 0;
  display: inline-block;
}
.inline-error {
  font-size: 12px;
  color: #900;
  display: block;
}
.inline-info {
  font-size: 12px;
  display: block;
}
.fullWidthContainer {
  background-color: #fff;
  width: calc(100% + 30px);

  margin: 0 -15px;
  &__inner {
    max-width: 1500px;
    margin: auto;
    display: block;
  }
}
.modal-90w {
  max-width: 90%;
}
@include media-breakpoint-down(xs) {
  .modal-90w {
    max-width: 100%;
  }
}

.brand-link {
  display: flex;
  height: 120px;
  padding: 0 25px;
  margin-top: 30px;
  margin-bottom: 30px;
  @media (max-width: 575.98px) {
    margin-top: 5px;
    height: 70px;
    margin-bottom: 5px;
  }

  img {
    transition: transform ease-out 0.2s;
    max-height: 100%;
    margin: auto;
  }
  &:hover {
    img {
      transform: scale(1.1);
      transform-origin: center;
    }
  }
}

.cancel-sub-modal {
  max-width: 1140px;
}

.cancel-sub-heading {
  font-size: 40px;
  line-height: 43px;
  font-family: Montserrat;
  font-weight: 900;
}

.btn-red:hover {
  background-color: #000;
  border-color: #000;
}

.btn-red:focus {
  box-shadow: none !important;
}

.inlineLoading {
  right: 10px;
  float: right;
  bottom: -50px;
  display: block;
  position: relative;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@include media-breakpoint-down(md) {
  .cancel-sub-heading {
    font-size: 30px;
    line-height: 35px;
  }
}
.CardHorizontal__coupon-code-btn a.pdfDownloadLink,
.Card__coupon-code-btn a.pdfDownloadLink {
  height: auto;
  line-height: 1;
  display: inline-block;
}

.settings-image {
  margin-top: 50px;
  margin-bottom: 30px;
}

.section1-about-content {
  padding-top: 30px;
  padding-bottom: 30px;
}

.coupon-card-langing-page {
  margin-bottom: 1.5rem;
}

.section-404 {
  height: 47vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.border-radius-50px {
  border-radius: 50px;
}

.border-grey {
  border: 1px solid #AFAFAF;
}

.border-rounded-top-14 {
  border-radius: 14px 14px 0 0;
}

.mb-md-4 {
  margin-bottom: 1.5rem;
}

.min-height-250 {
  min-height: 250px;
}

.header-image-wrap {
  border-radius: 15px 0px 0px 15px;
  border: 1px solid #AFAFAF;
  border-right: none;
}

@media screen and (max-width: 991px) {
  .header-image-wrap {
    display: none;
  }
}

.mb-35 {
  margin-bottom: 35px;
}

.header-image-wrap {
  position: relative;
  width: 100%;
  height: 340px;
  overflow: hidden;

  @media (max-width: 479px) {
    height: 150px;
  }

  @media (max-width: 575.98px) {
    margin-top: 0px;
  }

  @media screen and (max-width: $handheld-breakpoint) {
    display: none;
  }

  img {
    min-height: 150px;
    object-fit: cover;
    @media (min-width: 480px) {
      position: absolute;
      min-width: 100%;
      height: auto;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      min-height: 100%;
      max-width: none;
    }
  }
}

.rabatteButton {
  width: 180px;
  height: 35px;
  font-size: 14px;

  @media screen and (max-width: $handheld-breakpoint) {
    width: 150px;
    max-width: 100%;
    height: 25px;
    line-height: 25px !important;
    font-size: 11px !important;
    margin-top: 10px !important;
    font-weight: 700;
  }
}

.couponCodeBtn {
  text-align: center;
  vertical-align: middle;
  user-select: none;
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  color: #fff;
  border-color: #000;
  line-height: 35px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 900;
  display: inline-block;
  margin: 20px auto 0;

  .couponCode {
    width: 120px;
    background-color: white;
    color: black;
    float: left;
    cursor: auto;
    border: 1px solid #000;
    font-weight: 700;
    font-size: 12px;
    line-height: 1.2;
    min-height: 37px;
    display: flex;
    justify-content: center;
    align-items: center;

    &.stationaryCode {
      line-height: 18px;
      font-size: 12px;
    }
  }

  .copyBtn {
    width: 60px;
    float: left;
    padding: 7px 0 8px 0;
    background-color: #000;
    border: 1px solid #000;
    border-left: 0;
    cursor: pointer;
    display: table;

    &.stationaryCoupon {
      padding: 0 0 1px 0;
    }
    p {
      font-size: 10px;
      line-height: 10px;
      font-weight: 900;
      padding: 0 10%;
      margin-bottom: 0rem;
      color: white;
      display: table-cell;
      vertical-align: middle;
      text-align: center;
    }

    &:hover {
      background-color: #81e9f0;
      color: #000;

      p {
        background-color: #81e9f0;
        color: #000;
      }
    }
  }

  .copyPdfButton {
    width: 180px;
    height: 35px;
    margin-bottom: 0rem;
    background-color: black;

    &:hover {
      background-color: #81e9f0;
      color: #000;
      cursor: pointer;
    }
  }

  p {
    margin-bottom: 0rem;
    background-color: #000;
    border-radius: 50px;

    &:hover {
      background-color: #81e9f0;
      color: #000;
      cursor: pointer;
    }
  }

  a {
    width: 180px;
    height: 35px;
    text-decoration: none;
    background-color: black;
    color: white;
    padding: 8px 23px;

    &:hover {
      background-color: #81e9f0;
      color: #000;
      cursor: pointer;
    }
  }
}

.border-grey-rounded {
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 15px;
}

.font-500 {
  font-weight: 500;
}

.font-15 {
  font-size: 15px;
  // font-size: 1vw;
  // font-size: 0.8vw;
}