.form-control {
  height: calc(2.2rem + 0.75rem + 2px);
  border: 1px solid #000;
}
.form-group {
  position: relative;
  margin-bottom: 30px;
}
.btn {
  font-weight: 600;
}

.btn.btn-dark {
  padding: 5px 15px;
  text-decoration: none;
  border-radius: 50px;
  
  &.activation-failed-btn {
    padding-left: 30px;
    padding-right: 30px;
    margin-top: 40px;
  }

  &:focus {
    box-shadow: none;
  }
  &:hover {
    background-color: #81e9f0;
    border-color: #81e9f0;
    color: #000;
  }
  &.hover-white {
    &:hover {
      background-color: #fff;
    }
    &:focus {
      box-shadow: none;
    }
  }
}

.btn-tall {
  height: calc(2.2rem + 0.75rem + 2px);
}

@media (min-width: 357.98px) and (max-width: 415px) {
  .LoginModal button.facebook-button {
    padding-left: 25px;
    font-size: 14px;
  }
  .facebook-button {
    padding-left: 25px;
  }
}

.header-login-btn {
  border-radius: 70px;
  background: transparent !important;
  color: #000;
  border: 1px solid #000;

  &:hover {
    background: #000 !important;
    color: #fff;
  }

  &:active, &:focus {
    color: #000;
    box-shadow: none;
  }
}

.login-btn {

  @media (max-width: 415px) {
    font-size: 14px;
    padding: 5px !important;
  }
}

@media (max-width: 357px) {
  .LoginModal button.facebook-button {
    font-size: 11px;
    svg {
      width: 22px;
      height: 20px;
    }
  }
}

select.form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  & + .select-icon {
    position: absolute;
    top: 0;
    right: 10px;
    width: 20px;
    height: calc(2.2rem + 0.75rem + 2px);
    pointer-events: none;
  }
  //
  //&:focus ~ .select-icon {
  //  transform: rotate(-180deg);
  //}

  &::-ms-expand {
    display: none;
  }
}

.paymentBtn {
  border: 0;
  text-align: center;
  height: 150px;
  margin-bottom: 30px;
  position: relative;
  label {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f7f7f7;
    .marker {
      position: absolute;
      background-color: #ccc;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      z-index: 1;
      display: none;
    }
    img {
      position: relative;
      z-index: 2;
      max-width: 80%;
    }
  }
  input[type='radio'] {
    opacity: 0;
    position: absolute;
  }
  input:checked ~ div.marker {
    display: block;
  }
  img {
    max-height: 70px;
  }
}
.btn-main {
  background-color: #81e9f0;
  text-decoration: none;
  &:hover {
    background-color: #000;
    color: #fff;
  }
}
.btn-secondary {
  background-color: #000;;
}

.newsletterInput {
  border: 0;
  border-radius: 30px 0 0 30px;
}

.brand-newsletterInput {
  height: calc(1.8rem + 0.75rem + 2px);
  padding: 0.375rem 1rem;
}

.not-found {
  margin-top: 74px;
  margin-bottom: 187px;
  height: 322px;
}
.two-col {
  padding-left: 0;
}

.selectDark {
  padding-right: 0px;
  .two-col & {
    max-width: calc(50% - 7px);
    display: inline-block;
    & + .selectDark {
      float: right;
    }
  }
}

.selectDark {
  padding-right: 0px;
  .dropdown {
    border-radius: 50px;
    border: 1px solid #000 !important;
    color: #000;
    padding: 0.375rem 15px;
    font-size: 1rem;
    line-height: 1.7;
    height: 38px;
    max-width: 100%;
    -moz-appearance: none; /* Firefox */
    -webkit-appearance: none; /* Safari and Chrome */
    appearance: none;
    background: url('/icons/arrow-down-black.svg') no-repeat 92% 50% transparent;
    background-size: 10px;
    cursor: pointer;

    &:hover {
      background: url('/icons/arrow-down.png') no-repeat 92% 50% #000;
      span p {
        color: #fff;
      }
    }

    span p {
      text-decoration: none;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #000;
    }

    .dropdown-menu {
      margin: 3px 0 0 0;
      .custom-dropdown-item {
        display: block;
        color: #fff;
        border-bottom: 1px solid #fff;
        margin: 2px 5px 0px 5px;
        padding-left: 0px 5px !important;
        text-decoration: none;
        font-size: 14px;
        padding: 5px 0px;
        margin: 0px 10px;
        &:hover {
          text-decoration: none;
          font-weight: bold;
          cursor: pointer;
        }
        &.active-link {
          font-weight: bold;
        }
        &:last-child {
          border-bottom: 0;
        }
      }
    }
  }

  .dropdown.show {
    .dropdown-menu.show {
      position: absolute;
      will-change: transform;
      top: 0px;
      width: auto;
      transform: translate3d(-1px, 35px, 0px) !important;
      &.right {
        right: 0px !important;
        left: auto !important;
        margin-right: -1px;
      }
    }
  }
  &.sorting > .dropdown.show > .dropdown-menu.show {
    width: max-content;
  }

  .two-col & {
    max-width: calc(50% - 7px);
    display: inline-block;
    & + .selectDark {
      float: right;
    }
  }
}

@include media-breakpoint-down(xs) {
  .selectDark {
    padding-right: 5px;
    padding-left: 0px;
  }
}

.facebook-button {
  background-color: #405a94;
  border: 0;
  display: flex;
  width: 100%;
  margin: 0 auto;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: #fff;
  font-size: 16px;
  position: relative;
  padding-right: 12px;
  height: 57px;
  i,
  svg {
    position: absolute;
    width: 24px;
    height: 34px;
    line-height: 24px;
    font-size: 32px;
    left: 15px;
  }
  &:disabled {
    opacity: 0.5;
  }
}
.form-check {
  padding-left: 40px;
}
.calendar-label {
  background-color: #fff;
  position: absolute;
  z-index: 0;
  top: 7px;
  left: 5px;
  padding: 5px;
  color: #717981;
}

.mollie-component {
  width: 100%;
  padding: 10px 15px;
  color: #222;

  border: 2px solid transparent;
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.1),
    0px 1px 3px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.05);

  transition: all 0.05s ease;
}

.mollie-component.has-focus {
  border-color: #07f;
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.1),
    0px 2px 6px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.05);
  color: #444;
}

.mollie-component.is-invalid {
  border-color: #f00;
  background-color: #fff0f0;
}
