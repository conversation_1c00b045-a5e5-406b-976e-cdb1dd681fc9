@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import '../../styles/sass/mixins';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');

.regModal {
  max-width: 65%;
  max-height: 1000px;
  top: 20%;

  @include mediaMobile {
    max-width: 100%;
    top: 15%;
  }

  @include mediaTablet {
    max-width: 90%;
  }

  @include mediaLaptop {
    top: 10%;
    max-width: 90%;
  }

  @include mediaDesktop {
    top: 10%;
    max-width: 85%;
  }

  @include mediaLargeDesktop {
    top: 10%;
    max-width: 55%;
  }

  :global(.modal-content) {
    :global(.modal-body) {
      position: relative;
      display: flex;
      align-items: stretch;
      justify-content: stretch;
      flex-direction: row;
      padding: 0;
      margin: 0;

      & > * {
        flex: 1;
      }

      :global(.reg-side-image),
      :global(.reg-side-title) {
        width: 50%;
      }

      :global(.reg-side-image) {
        @media (max-width: 800px) {
          display: none;
        }
      }

      :global(.reg-side-title) {
        background-color: #81e9f0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;

        * {
          font-size: 29px;
          line-height: 1.3;
          font-family: Montserrat;
          font-weight: 900;
          text-align: center;

          @include mediaMobile {
            font-size: 24px;
            font-family: Montserrat;
            font-weight: 900;
            text-align: center;
          }
        }

        p {
          font-size: 17px;
          font-weight: 400;
          line-height: 1.5;
          text-align: center;
        }
      }

      :global(.reg-close-btn) {
        position: absolute;
        right: 0;
        margin: 5px;
        border: 0;
        background: transparent;
      }
    }
  }
}

.step3BackBtnWrapper {
  :global(.next-prev-btn) {
    @include media-breakpoint-down(sm) {
      margin-top: 30px;
    }
  }
}

.nextPrevBtn {
  min-height: 64px;
  width: 100%;
  font-size: 1.5rem;
}

.bottomContainer {
  margin-bottom: 5rem;
  margin-top: 2rem;
}

.additionalInformation {
  font-size: 12px;
  letter-spacing: 0px;
  margin-bottom: 16px;
}

.registerSubSteps {
  margin-bottom: 20px;
  :global(h2) {
    display: inline;
    width: 170px;
    vertical-align: bottom;
    text-align: left;
    margin-top: auto;
    margin-bottom: 0px;
    line-height: 25px;
    font-size: 25px;
    font-weight: 900;
  }
  @media (max-width: 728px) {
    display: none;
  }
}
@include media-breakpoint-up(lg) {
  .registerSubSteps {
    padding-left: 0px;
    display: flex;
    height: 175px;
    :global(h2) {
      display: inline;
    }
  }
}