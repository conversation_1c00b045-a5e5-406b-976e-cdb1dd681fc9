@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import '../../styles/sass/mixins';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700;900&display=swap');

.reg-modal {
  max-width: 65%;
  max-height: 1000px;

  top: 20%;

  @include mediaMobile {
    max-width: 100%;
    top: 15%;
  }

  @include mediaTablet {
    max-width: 90%;
  }

  @include mediaLaptop {
    top: 10%;
    max-width: 90%;
  }

  @include mediaDesktop {
    top: 10%;
    max-width: 85%;
  }

  @include mediaLargeDesktop {
    top: 10%;
    max-width: 55%;
  }

  .modal-content {
    .modal-body {
      position: relative;
      display: flex;
      align-items: stretch;
      justify-content: stretch;
      flex-direction: row;
      padding: 0;
      margin: 0;

      & > * {
        flex: 1;
      }

      .reg-side-image,
      .reg-side-title {
        width: 50%;
      }

      .reg-side-image {
        @media (max-width: 800px) {
          display: none;
        }
      }

      .reg-side-title {
        background-color: #81e9f0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;

        * {
          font-size: 29px;
          line-height: 1.3;
          font-family: Montserrat;
          font-weight: 900;
          text-align: center;

          @include mediaMobile {
            font-size: 24px;
            font-family: Montserrat;
            font-weight: 900;
            text-align: center;
          }
        }

        p {
          font-size: 17px;
          font-weight: 400;
          line-height: 1.5;
          text-align: center;
        }
      }

      .reg-close-btn {
        position: absolute;
        right: 0;
        margin: 5px;
        border: 0;
        background: transparent;
      }
    }
  }
}

.step-3-back-btn-wrapper {
  .next-prev-btn {
    @include media-breakpoint-down(sm) {
      margin-top: 30px;
    }
  }
}

.next-prev-btn {
  min-height: 64px;
  width: 100%;
  font-size: 1.5rem;
}
