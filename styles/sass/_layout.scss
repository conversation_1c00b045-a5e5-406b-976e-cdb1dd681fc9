@for $i from 1 through 10 {
  .pad-tb-#{$i} {
    padding-bottom: #{$i * 10 + 'px'};
    padding-top: #{$i * 10 + 'px'};
  }
  .pad-t-#{$i} {
    padding-top: #{$i * 10 + 'px'};
  }
  .pad-b-#{$i} {
    padding-bottom: #{$i * 10 + 'px'};
  }
  .pad-r-#{$i} {
    padding-right: #{$i * 10 + 'px'};
  }

  .pad-l-#{$i} {
    padding-left: #{$i * 10 + 'px'};
  }

  .mar-tb-#{$i} {
    margin-bottom: #{$i * 10 + 'px'};
    margin-top: #{$i * 10 + 'px'};
  }
  .mar-t-#{$i} {
    margin-top: #{$i * 10 + 'px'};
  }
  .mar-b-#{$i} {
    margin-bottom: #{$i * 10 + 'px'};
  }
}

@each $breakpoint in map-keys($grid-breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);
    @for $i from 1 through 10 {
      .pad#{$infix}-tb-#{$i} {
        padding-bottom: #{$i * 10 + 'px'};
        padding-top: #{$i * 10 + 'px'};
      }
      .pad#{$infix}-t-#{$i} {
        padding-top: #{$i * 10 + 'px'};
      }
      .pad#{$infix}-b-#{$i} {
        padding-bottom: #{$i * 10 + 'px'};
      }
      .pad#{$infix}-r-#{$i} {
        padding-right: #{$i * 10 + 'px'};
      }

      .pad#{$infix}-l-#{$i} {
        padding-left: #{$i * 10 + 'px'};
      }
    }
  }
}

.pad-l-0 {
  padding-left: 0px;
}
.pad-rl-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.pad-r-0 {
  padding-right: 0px;
}
.pad-b-0 {
  padding-bottom: 0px;
}

.mw-auto {
  max-width: none;
}
.dropdown-menu {
  background-color: #000;
  button {
    color: #fff;
  }
}

.newsletter-btn {
  color: #fff;
  background-color: #000;
  border-color: #000;
  border-radius: 0 30px 30px 0;
}

.brand-newsletter-btn {
  padding: 0.375rem 1rem;
}

.br-15 {
  border-radius: 15px;
}

.newsletter-info {
  font-size: 25px;
  width: 70%;
}

.brand-newsletter-info {
  color: #505050;
}

.brand-newsletter-subtext {
  font-size: 1.5rem;
}

@media (max-width: 767.98px) {
  .brand-newsletter-subtext {
    font-size: 1.1rem;
  }

  .newsletter-brand-padding {
    padding-top: 10px;
  }

  .newsletter-brand-section > div {
    padding-top: 15px;
    padding-bottom: 25px;
  }
}

.newsletter-brand-padding {
  padding-top: 20px;
}

.newsletter-btn:hover {
  background-color: #81E9F0;
  border-color: #81E9F0;
  color: #000;
}

.video-section {
  height: 550px;
  @media (max-width: 575px) {
    height: 450px;
  }
}

.alle-marken-btn-landing-page {
  padding-top: 40px;
}

.video-placeholder-container {
  overflow: hidden;
  position: absolute;
  right: 0;
  left: 50%;
  background-color: #e2e2e2;
  top: 0;
  bottom: 0;
  background-size: cover;
  transition: all 0.5s;
  background-position: center;
  cursor: pointer;

  // @include media-breakpoint-down(sm) {
  //   margin-left: -10px;
  //   margin-right: -10px;
  //   width: 110% !important;
  // }

  @include media-breakpoint-down(md) {
    position: relative;
    width: 100%;
    left: 0;
    top: 0;
    min-height: 450px;
  }

  &.col-sm-6 {
    position: relative;
    left: 0;
  }
  img {
    margin: auto;
    display: block;
  }
  iframe {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
  .video-placeolder-play {
    font-size: 100px;
    position: absolute;
    top: 50%;
    left: 50%;
    color: #fff;
    opacity: 0.5;
    transform: translate(-50%, -50%);
    transition: all 0.2s;
    z-index: 5;
  }
  .video-placeolder-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 4;
  }
  &:hover {
    opacity: 0.9;
    .video-placeolder-play {
      font-size: 120px;
      opacity: 0.7;
    }
  }
}
.closeVideoModal {
  position: absolute;
  top: 10px;
  right: -50px;
  font-size: 40px;
  color: #fff;
  display: block;
  z-index: 9999;
  cursor: pointer;
}

@include media-breakpoint-down(md) {
  .closeVideoModal {
    position: absolute;
    top: -45px;
    right: 0px;
    font-size: 40px;
    color: #fff;
    display: block;
    z-index: 9999;
    cursor: pointer;
  }
}

.section-max-width {
  max-width: 1500px;
  margin: 0 auto;
}

.main-page-content {
  padding-top: 109px;
}

.container-footer-bottom {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  justify-content: space-between;
  background: #fff;
  &.gray-background {
    background-color: rgb(235, 235, 235);
  }
}

.alle-marken-btn {
  height: 34px;
  width: 177px;
}

@include media-breakpoint-down(md) {
  .main-page-content {
    padding-top: 85px;
  }
}

.fb-subs-btn {
  background-color: black;
  color: white;
  @media (max-width: 328.98px) {
    font-size: 11px;
  }
  @media (min-width: 329px) and (max-width: 342.98px) {
    font-size: 12px;
  }
  @media (min-width: 343px) and (max-width: 427px) {
    font-size: 13px;
  }
  @media (max-width: 427px) {
    padding-left: 4px;
    padding-right: 4px;
  }
  @media (min-width: 427.02px) {
    padding-left: 3rem;
    padding-right: 3rem;
    font-size: 14px;
  }
}

.agb {
  ol > li {
    counter-increment: item;
    font-weight: normal;
  }

  ol.number-bold > li {
    font-weight: bold;
  }

  li p {
    font-weight: normal;
  }

  li {
    word-break: break-word;
  }

  ol > li:first-child {
    counter-reset: item;
    padding-top: 20px;
  }

  ol ol > li {
    display: block;
    padding-bottom: 20px;
  }

  ol ol > li:before {
    content: counters(item, '.') '. ';
    margin-left: -20px;
  }

  .pt-20 {
    padding-top: 20px;
  }

  .pb-20 {
    padding-bottom: 20px;
  }

  .field {
    min-width: 300px;
    display: inline-block;
    display: contents;
  }

  .line {
    width: 300px;
    max-width: 90%;
    height: 50px;
    margin: auto;
    display: inline-block;
    border-bottom: 1px solid #1f1209;
  }
}

.main-header.datenschutz-header {
  @media (max-width: 792px) {
    font-size: 30px;
  }

  @media (max-width: 514px) {
    font-size: 25px;
  }

  @media (max-width: 425px) {
    font-size: 20px;
  }
}

.datenschutz {
  .subtitle {
    font-weight: 700;
    font-size: 17px;
    margin-top: 25px;
    margin-bottom: 20px;
  }

  ol.number-bold {
    .sub-subtitle {
      margin-bottom: 15px;
      font-weight: bold;
    }
  }

  ol.number-bold > li {
    font-weight: bold;
  }

  li p {
    font-weight: normal;
  }

  li {
    word-break: break-word;
  }

  ol.bracket-letter-numbering {
    counter-reset: list;
    > li {
      list-style: none;
    }
    > li:before {
      content: counter(list, lower-alpha) ') ';
      counter-increment: list;
    }
  }
  ul.line-list {
    counter-reset: item;
    list-style-type: none;
    li:before {
      content: counter(item, cjk-ideographic) '  ';
      counter-increment: item;
      counter-reset: item;
    }
    li {
      font-weight: 400;
    }
  }
}

.custom-video-size .modal-content {
  border: none;
}

.modal-dialog.custom-video-size {
  height: 100%;
  .modal-content {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.MembershipOfferContainer {
  background-color: #f7f7f7;
  text-align: center;
  padding: 30px;
  display: flex;
  height: 100%;

  @include media-breakpoint-down(sm) {
    border-bottom: 50px solid #fff;
  }
}

.mobile-block {
  display: inline-block;
  margin-bottom: 0;
}

.MembershipOffer__content {
  margin: auto;
}

.LoginPageForm {
  margin: 50px auto;
  max-width: 600px;
}

.second-column {
  padding-top: 20px;
}

@media (max-width: 767.98px) {
  .gray-box.first-row:not(.edit-user-form) {
    min-height: auto;
  }

  .gray-box.subscription-info-box {
    margin-top: 0px;
  }

  .second-column {
    padding-top: 0;
  }
}

.newsletter-link {
  color: #007bff;
  text-decoration: none;
  font-weight: bold;

  &:hover {
    text-decoration: none;
  }
}