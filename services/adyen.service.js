import API from './api';

export const getPaymentMethods = async () =>
  API.get('/payments/payment-methods');

// this method returns user's previously chosen (on registration) product package
export const getSubscriptionPackage = async () => API.get(`/product-packages`);

export const getSubscriptionPackageById = async (id) =>
  API.get(`/product-packages/${id}`);

export const getSubscriptionPackagesTop = async () =>
  API.get(`/product-packages/list/top`);

export const checkout = async (productType, shoppingCart) =>
  API.post('/payments/checkout', {
    productType: productType,
    shoppingCart: shoppingCart.map((product) => ({
      productId: product.productPackageId,
    })),
  });

export const initiatePayment = async function (payload) {
  const response = await API.post('/payments', payload);
  return response;
};

export const getStoredPayment = async () =>
  API.get('/payments/stored-payment-methods');

export const onAdditionalDetails = async (payload) =>
  API.post('/payments/details', payload);

export const cancelPayment = async (payload) =>
  API.post('payments/cancel', payload);
