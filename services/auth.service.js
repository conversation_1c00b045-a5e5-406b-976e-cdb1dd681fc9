import Router from 'next/router';

import API from './api';
import {
  setIdTokenCookie,
  setRefreshTokenCookie,
  removeIdTokenCookie,
  removeAllCookies,
} from 'services/cookies.service';
import {
  checkValidSubscription,
  getSubscription,
} from 'services/subscription.service';
import * as consumerService from 'services/consumer.service';

export const signIn = (email, password) => {
  return API.post('/authentication/login', {
    email,
    password,
  });
};

export const storeUserData = async function (
  { idToken, refreshToken },
  callback
) {
  //TODO call only on login - there will be changes - maybe it is obsolete
  let firstCallAfterLogin = false;
  let subscribed = false;
  if (!localStorage.getItem('idToken')) {
    localStorage.setItem('idToken', idToken);
    localStorage.setItem('refreshToken', refreshToken);

    const [, , consumerResponse, hasValidSubscription, subscriptionData] =
      await Promise.allSettled([
        setIdTokenCookie(idToken),
        setRefreshTokenCookie(refreshToken),
        consumerService.getConsumer(),
        checkValidSubscription(),
        getSubscription(),
      ]);
    subscribed =
      hasValidSubscription.value?.data ||
      subscriptionData.value?.data?.status === 'CANCELED';
    localStorage.setItem(
      'userAttributes',
      JSON.stringify({
        firstName: consumerResponse.value?.data?.firstName,
        surname: consumerResponse.value?.data?.surname,
        email: consumerResponse.value?.data?.emails.find(
          (email) => email.primaryEmail
        )?.email,
        signed_up_at: parseInt((new Date().getTime() / 1000).toFixed(0)),
      })
    );
    firstCallAfterLogin = true;
  }
  callback({
    justLoggedIn: firstCallAfterLogin,
    subscribed: subscribed,
  });
};

export const logoutUser = async () => {
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('userAttributes');
  localStorage.removeItem('idToken');
  localStorage.removeItem('hideRegister');
  localStorage.removeItem('firstLogin');
  await removeIdTokenCookie();
  await removeAllCookies();
  if (window.Intercom) {
    Intercom('shutdown');
  }
  setTimeout(() => {
    window.location = `https://${process.env.NEXT_PUBLIC_OAUTH_DOMAIN}/logout?client_id=${process.env.NEXT_PUBLIC_REACT_APP_COGNITO_CLIENT_ID}&logout_uri=${process.env.NEXT_PUBLIC_REDIRECT_SIGNIN}`;
  }, 100);
};

export const logoutUserTokenExpired = async () => {
  await logoutUser();
  Router.push(
    {
      pathname: Router.pathname,
      query: { tokenExp: true },
    },
    {
      pathname: window.location.pathname,
      query: { tokenExp: true },
    },
    { shallow: true }
  );
};

export const isAuth = () => {
  try {
    return localStorage.getItem('idToken');
  } catch {
    return null;
  }
};

export const resendConfirmationCodeEmail = (user) => {
  return API.post('/authentication/resend-confirmation-code', {
    email: user.username,
  });
};

export const resendConfirmationCodeUserId = (user) =>
  API.get(`/authentication/resend-confirmation-code?userId=${user}`);

export const sendResetPasswordEmail = (email) => {
  return API.post('/authentication/forgot-password', { email });
};

export const saveNewPassword = (email, code, password) => {
  return API.post('/authentication/confirm-password', {
    email,
    code,
    password,
  });
};

export const getNewIdToken = (refreshToken) => {
  return API.post('/authentication/refresh-token', {
    refreshToken,
  });
};
