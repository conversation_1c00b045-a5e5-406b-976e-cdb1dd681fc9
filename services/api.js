import Axios from 'axios';
import Router from 'next/router';

import { isAuth, getNewIdToken, logoutUserTokenExpired } from './auth.service';
import { setIdTokenCookie } from './cookies.service';

const isClientSide = () => typeof window !== 'undefined';

async function refreshIdToken() {
  try {
    const response = await getNewIdToken(localStorage.getItem('refreshToken'));

    localStorage.setItem('idToken', response.data.idToken);
    await setIdTokenCookie(response.data.idToken);
    return response?.data?.idToken;
  } catch (error) {
    await logoutUserTokenExpired();
    throw error;
  }
}

const instance = Axios.create({
  baseURL: process.env.NEXT_PUBLIC_REACT_APP_API_URL,
});

instance.interceptors.request.use(
  async function (config) {
    if (isClientSide()) {
      const token = isAuth();
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }

      return config;
    } else {
      return config;
    }
  },
  (error) => Promise.reject(error)
);

instance.interceptors.response.use(
  (response) => response,
  async function (error) {
    if (isClientSide()) {
      const status = error.response ? error.response.status : null;
      if (
        (status === 401 ||
          status === null ||
          error.response.data.indexOf('The Token has expired') !== -1) &&
        error.config?.url !== '/authentication/login'
      ) {
        if (error.config?.url !== '/authentication/refresh-token') {
          try {
            const idToken = await refreshIdToken();
            error.config.headers['Authorization'] = `Bearer ${idToken}`;
            return Promise.resolve(instance.request(error.config));
          } catch (error) {
            await logoutUserTokenExpired();
            return Promise.reject(error);
          }
        } else {
          await logoutUserTokenExpired();
          return Promise.reject(error);
        }
      } else {
        return Promise.reject(error);
      }
    } else {
      return Promise.reject(error);
    }
  }
);

export default instance;
