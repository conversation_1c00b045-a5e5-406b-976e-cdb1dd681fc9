export const loadScript = (scriptUrl, callback) => {
  // TODO check if this func is needed
  const script = document.createElement('script');
  script.src = scriptUrl;

  script.async = true;
  script.onload = () => callback();

  document.body.appendChild(script);
};

export const facebookSignin = () => {
  window.location = `https://${process.env.NEXT_PUBLIC_OAUTH_DOMAIN}/oauth2/authorize?identity_provider=Facebook&redirect_uri=${process.env.NEXT_PUBLIC_REACT_APP_API_URL}/authentication/federated-login&response_type=CODE&client_id=${process.env.NEXT_PUBLIC_REACT_APP_COGNITO_CLIENT_ID}&scope=aws.cognito.signin.user.admin%20email%20openid%20profile`;
};
