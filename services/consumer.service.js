import API from './api';

export const getConsumer = (headers = null) =>
  API.get('/consumers', headers ? { headers } : null);

export const createUser = async (data) => {
  const {
    acceptTerms,
    confirmPass,
    registrationCode,
    isNewslettersSubscribed,
    ...userData
  } = data;

  const dataToSend = {
    ...userData,
    isNewslettersSubscribed: isNewslettersSubscribed === '1' ? true : false,
    registrationCode: registrationCode !== '' ? registrationCode : null,
  };

  return API.post('/authentication/register', dataToSend);
};

export const editUser = async (data) => {
  return API.put('/consumers', data);
};

export const setUserProductPackage = async (packageId) => {
  return API.post(`/consumers/product-package?productPackageId=${packageId}`);
};

export const setUserRegistrationCode = async (code) => {
  return API.post(`/consumers/registration-code?registrationCode=${code}`);
};

export const deleteUser = async () => {
  return API.delete('/consumers');
};

export const addEmail = async ({ email, password }) => {
  return API.post('/consumer-emails', {
    email,
    password,
  });
};

export const deleteEmail = async (email) => {
  const emailToDelete = encodeURIComponent(email);
  return API.delete(`/consumer-emails?email=${emailToDelete}`);
};

export const makeEmailPrimary = async (email) => {
  const selectedEmail = encodeURIComponent(email);
  return API.put(`/consumer-emails?email=${selectedEmail}`);
};
