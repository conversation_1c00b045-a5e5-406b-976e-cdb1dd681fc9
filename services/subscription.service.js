import API from './api';

export const cancelSubscription = () => {
  return API.post(`/subscription/cancel-subscription`);
};

export const checkValidSubscription = (headers = null) =>
  API.get(`/subscription/check-subscription`, headers ? { headers } : null);

export const getSubscription = (headers = null) =>
  API.get(`/subscription`, headers ? { headers } : null);

export const renewSubscription = (data) => {
  console.log('renewSubscriptionFired');
  console.log('data', data);
  console.log('data.consumerSelectedPayment', data?.consumerSelectedPayment);
  console.log('data.email', data?.email);

  return API.post(`/subscription/renew-subscription`, data);
};

export const validateAndGetProductPackageForDiscountCode = (code) => {
  let discountCode = code;
  if (code.endsWith(' ')) {
    discount = discountCode.trim();
    discountCode = discountCode + '%20';
  }
  return API.post(`/product-packages/validate/code`, {
    code: discountCode,
  });
};

export const getProductPackageForDiscountCode = (code) => {
  let discountCode = code;
  if (code.endsWith(' ')) {
    discount = discountCode.trim();
    discountCode = discountCode + '%20';
  }
  return API.post(`/product-packages/code`, {
    code: discountCode,
  });
};
