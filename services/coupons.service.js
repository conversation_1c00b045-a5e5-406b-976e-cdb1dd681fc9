import API from './api';

export const getCoupons = async ({
  pageSize = 10,
  page = 0,
  sort = [{ field: 'timeAdded', dir: 'DESC' }],
  search = '',
  filters = [],
  boost = false,
}) => {
  let data = [];
  const result = await API.post(
    '/coupons/list',
    {
      page,
      pageSize,
      sort,
      search,
      filters,
    },
    { params: { ...(boost ? { boost: true } : {}) } }
  );
  if (result && result.data) {
    data = result.data;
  }
  return data;
};

export const getLatestCoupons = async (headers = null) => {
  let data = [];
  const result = await API.get('/coupons/latest', headers ? { headers } : null);
  if (result && result.data) {
    data = result.data;
  }
  return data;
};

export const getRelatedCoupons = async ({
  brandId,
  numberOfCoupons = 4,
  reachBoost = false,
}) => {
  let data = [];
  const params = {
    brandId,
    numberOfCoupons,
    status:"ACTIVE",
    ...(reachBoost ? { boost: true } : {}),
  };
  const result = await API.get('/coupons/listRandomCoupons', { params });
  if (result && result.data) {
    data = result.data;
  }
  return data;
};

export const getDiscountCode = async (
  couponId,
  onSuccess,
  onError,
  reachBoost = false
) => {
  try {
    const { data } = await API.get('/coupons/get-discount-code', {
      params: { couponId, ...(reachBoost ? { boost: true } : {}) },
    });
    onSuccess(data);
  } catch (error) {
    const { status, data } = error.response || { status: 401 };
    onError(status, data);
  }
};

export const getCouponLink = async (
  couponId,
  onSuccess,
  onError
) => {
  try {
    const { data } = await API.post('/coupons/coupon-links', {couponIds: [couponId]});
    onSuccess(data[0]);
  } catch (error) {
    const { status, data } = error.response || { status: 401 };
    onError(status, data);
  }
};

export const setFavouriteStatus = async (
  couponId,
  newFavStatus,
  onSuccess,
  onError
) => {
  try {
    const { data } = await API.get(
      `/coupons/fave-coupon?couponId=${couponId}&fave=${newFavStatus}`
    );
    onSuccess(data);
  } catch (error) {
    const status = error.response?.status || { status: 401 };
    const data =
      error.response?.data ||
      error.message ||
      'Unknown error occured while trying to change the favourite status.';
    onError(status, data);
  }
};

export const getFavouriteCoupons = async ({
  pageSize = 10,
  page = 0,
  sort = [{ field: 'usedNumber', dir: 'DESC' }],
  search = '',
  filters = [],
}) => {
  let data = [];

  const result = await API.post('/coupons/list-favourite-coupons', {
    page,
    pageSize,
    sort,
    search,
    filters,
  });
  if (result && result.data) {
    data = result.data;
  }
  return data;
};
