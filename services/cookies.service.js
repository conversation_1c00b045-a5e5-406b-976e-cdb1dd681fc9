import { getJWTCookieExpiration, getDatePlusNMonths } from 'util/helpers';

export const setCookie = (cookieName, cookieValue, cookieExpires) =>
  fetch('/api/setCookie', {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      cookieName,
      cookieValue,
      cookieExpires,
    }),
  });

export const getCookies = () =>
  fetch('/api/listAllCookies', {
    method: 'post',
  }).then((response) => response.json());

export const removeCookie = (cookieName) => setCookie(cookieName, '', 0);

export const setIdTokenCookie = (jwtToken) =>
  setCookie('idToken', jwtToken, getJWTCookieExpiration(jwtToken));

export const setRefreshTokenCookie = (refreshToken) =>
  setCookie('refreshToken', refreshToken, getDatePlusNMonths(1).toGMTString());

export const removeIdTokenCookie = () => removeCookie('idToken');

export const removeAllCookies = () =>
  getCookies().then(({ cookies }) =>
    cookies.forEach((cookieName) => removeCookie(cookieName))
  );
