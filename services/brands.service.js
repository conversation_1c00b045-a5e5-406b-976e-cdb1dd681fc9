import API from './api';

export const getBrands = async ({
  pageSize = 24,
  page = 0,
  sort = [{ field: 'timeAdded', dir: 'DESC' }],
  search = '',
  popular = false,
}) => {
  let data = [];
  try {
    const result = await API.post('/brands/list', {
      page,
      pageSize,
      sort,
      search,
      popular,
    });
    if (result && result.data) {
      data = result.data.data;
    }
    return data;
  } catch (error) {
    return error;
  }
};

export const getBrandBySlug = async (slug, boost, headers) => {
  return API.get(`/brands/${slug}?boost=${boost === ''}`, { headers });
};
