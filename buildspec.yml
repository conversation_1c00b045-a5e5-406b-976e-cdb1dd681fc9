version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - echo "Using Node.js version $(node -v)"
      - echo "Updating npm to the latest version..."
      - npm install -g npm@latest
  pre_build:
    commands:
      - echo "Installing Yarn..."
      - npm install -g yarn
      - echo "Installing project dependencies..."
      - yarn install
  build:
    commands:
      - echo "Copying environment configuration..."
      - cp ./env/.env.$ENV .env.production
      - echo "Building the project..."
      - NODE_ENV=production yarn build

artifacts:
  files:
    - appspec.yml
    - '**/*'

cache:
  paths:
    - 'node_modules/**/*'
